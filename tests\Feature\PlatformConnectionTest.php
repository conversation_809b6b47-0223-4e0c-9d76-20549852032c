<?php

declare (strict_types = 1);

namespace Tests\Feature;

use App\Models\Platform;
use App\Models\Token;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PlatformConnectionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private Platform $instagramPlatform;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create();

        // Create Instagram platform
        $this->instagramPlatform = Platform::factory()->create([
            'key'     => 'instagram',
            'name'    => 'Instagram',
            'enabled' => true,
        ]);
    }

    public function test_platform_connection_status_for_user_without_token(): void
    {
        $this->actingAs($this->user);

        $response = $this->getJson('/api/v1/platforms/service/instagram');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data'    => [
                    'key'       => 'instagram',
                    'connected' => false,
                ],
            ]);

        // Should include auth URL when not connected
        $response->assertJsonStructure([
            'data' => [
                'auth_url',
            ],
        ]);
    }

    public function test_platform_connection_status_for_user_with_active_token(): void
    {
        $this->actingAs($this->user);

        // Create an active instagram token for the user via Token (polymorphic)
        $tokenable = \App\Models\InstagramToken::factory()->create([
            'status'    => 'active',
            'is_active' => true,
            'username'  => 'test_user',
            'ig_id'     => (string) rand(100000000000, 999999999999),
        ]);
        Token::factory()->create([
            'platform_id'    => $this->instagramPlatform->id,
            'user_id'        => $this->user->id,
            'tokenable_type' => \App\Models\InstagramToken::class,
            'tokenable_id'   => $tokenable->id,
        ]);

        $response = $this->getJson('/api/v1/platforms/service/instagram');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data'    => [
                    'key'       => 'instagram',
                    'connected' => true,
                ],
            ])
            ->assertJsonStructure([
                'data' => [
                    'token' => [
                        'username',
                        'account_type',
                    ],
                ],
            ]);

        // Should not include auth URL when connected
        $response->assertJsonMissing([
            'data' => [
                'auth_url',
            ],
        ]);
    }

    public function test_platform_connection_status_for_different_users(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Create token only for user1
        $tokenable = \App\Models\InstagramToken::factory()->create([
            'status'    => 'active',
            'is_active' => true,
        ]);
        Token::factory()->create([
            'platform_id'    => $this->instagramPlatform->id,
            'user_id'        => $user1->id,
            'tokenable_type' => \App\Models\InstagramToken::class,
            'tokenable_id'   => $tokenable->id,
        ]);

        // Check user1 connection status
        $this->actingAs($user1);
        $response1 = $this->getJson('/api/v1/platforms/service/instagram');
        $response1->assertJson([
            'data' => [
                'connected' => true,
            ],
        ]);

        // Check user2 connection status
        $this->actingAs($user2);
        $response2 = $this->getJson('/api/v1/platforms/service/instagram');
        $response2->assertJson([
            'data' => [
                'connected' => false,
            ],
        ]);
    }

    public function test_get_all_platforms_with_user_status(): void
    {
        $this->actingAs($this->user);

        $response = $this->getJson('/api/v1/platforms/available');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'data' => [
                    'social_media' => [
                        'instagram' => [
                            'name',
                            'connected',
                        ],
                    ],
                ],
            ]);
    }

    public function test_get_auth_url_for_disconnected_platform(): void
    {
        $this->actingAs($this->user);

        $response = $this->getJson('/api/v1/platforms/service/instagram/auth-url');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data'    => [
                    'service' => 'instagram',
                ],
            ])
            ->assertJsonStructure([
                'data' => [
                    'auth_url',
                    'full_auth_url',
                ],
            ]);
    }

    public function test_get_auth_url_for_connected_platform(): void
    {
        $this->actingAs($this->user);

        // Create an active token for the user
        $tokenable = \App\Models\InstagramToken::factory()->create([
            'status'    => 'active',
            'is_active' => true,
        ]);
        Token::factory()->create([
            'platform_id'    => $this->instagramPlatform->id,
            'user_id'        => $this->user->id,
            'tokenable_type' => \App\Models\InstagramToken::class,
            'tokenable_id'   => $tokenable->id,
        ]);

        $response = $this->getJson('/api/v1/platforms/service/instagram/auth-url');

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'User is already connected to this service',
            ]);
    }

    public function test_unauthorized_access_requires_authentication(): void
    {
        $response = $this->getJson('/api/v1/platforms/service/instagram');

        $response->assertStatus(401);
    }
}
