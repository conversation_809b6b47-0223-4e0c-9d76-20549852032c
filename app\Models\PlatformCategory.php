<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class PlatformCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * Get platforms for this category
     */
    public function platforms(): HasMany
    {
        return $this->hasMany(Platform::class, 'category_id');
    }

    /**
     * Scope to filter by name
     */
    public function scopeByName(Builder $query, string $name): Builder
    {
        return $query->where('name', $name);
    }

    /**
     * Get all category names
     */
    public static function getCategoryNames(): array
    {
        return static::pluck('name')->toArray();
    }

    /**
     * Get platforms by category name
     */
    public static function getPlatformsByCategoryName(string $categoryName): array
    {
        return static::where('name', $categoryName)
            ->with('platforms')
            ->first()
        ?->platforms
        ?->toArray() ?? [];
    }

    /**
     * Get category by name
     */
    public static function getCategoryByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }

    /**
     * Get category ID by name
     */
    public static function getCategoryIdByName(string $name): ?int
    {
        return static::where('name', $name)->value('id');
    }
}