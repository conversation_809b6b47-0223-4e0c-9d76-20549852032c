<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_categories', function (Blueprint $table) {
            $table->id();

            $table->string('name')->unique();
            $table->text('description')->nullable();

            $table->timestamps();

            $table->index(['name']);
        });

        Schema::create('platforms', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->string('key')->unique();
            $table->text('description')->nullable();

            $table->foreignId('category_id')->constrained('platform_categories')->onDelete('cascade');

            $table->boolean('enabled')->default(false)->index();

            $table->timestamps();

            $table->index(['key', 'enabled']);
            $table->index(['category_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platforms');
        Schema::dropIfExists('platform_categories');
    }
};