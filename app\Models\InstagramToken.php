<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;

final class InstagramToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'app_scope_id',
        'ig_id',
        'username',
        'name',
        'account_type',
        'profile_picture_url',
        'followers_count',
        'follows_count',
        'media_count',
        'access_token',
        'token_type',
        'expires_at',
        'status',
        'is_active',
        'scopes',
        'has_business_permission',
        'page_id',
    ];

    protected $casts = [
        'expires_at'              => 'datetime',
        'is_active'               => 'boolean',
        'has_business_permission' => 'boolean',
        'scopes'                  => 'array',
        'followers_count'         => 'integer',
        'follows_count'           => 'integer',
        'media_count'             => 'integer',
    ];

    protected $hidden = [
        'access_token',
    ];

    /**
     * Get the polymorphic token relationship.
     */
    public function token(): MorphOne
    {
        return $this->morphOne(Token::class, 'tokenable');
    }

    // Access platform and user via $this->token relation in services/repositories

    /**
     * Scope a query to only include active tokens.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include valid tokens (not expired).
     */
    public function scopeValid(Builder $query): Builder
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }

    // Removed scopeByPlatform; instagram_tokens table does not own platform_id

    /**
     * Scope a query to only include tokens by user.
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->whereHas('token', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }

    /**
     * Check if the token is valid (active and not expired).
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Check if the token is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get the Instagram username.
     */
    public function getInstagramUsername(): ?string
    {
        return $this->username;
    }

    /**
     * Get the Instagram account type.
     */
    public function getAccountType(): ?string
    {
        return $this->account_type;
    }

    /**
     * Get the Instagram profile picture URL.
     */
    public function getProfilePictureUrl(): ?string
    {
        return $this->profile_picture_url;
    }

    /**
     * Get the Instagram followers count.
     */
    public function getFollowersCount(): ?int
    {
        return $this->followers_count;
    }

    /**
     * Get the Instagram follows count.
     */
    public function getFollowsCount(): ?int
    {
        return $this->follows_count;
    }

    /**
     * Get the Instagram media count.
     */
    public function getMediaCount(): ?int
    {
        return $this->media_count;
    }

    /**
     * Get the access token.
     */
    public function getAccessToken(): string
    {
        return $this->access_token;
    }

    /**
     * Get the token scopes.
     */
    public function getScopes(): ?array
    {
        return $this->scopes;
    }

    /**
     * Check if the token has business permissions.
     */
    public function hasBusinessPermission(): bool
    {
        return $this->has_business_permission;
    }

    /**
     * Get the Facebook page ID if connected.
     */
    public function getPageId(): ?string
    {
        return $this->page_id;
    }

    /**
     * Get the Instagram ID.
     */
    public function getInstagramId(): ?string
    {
        return $this->ig_id;
    }

    /**
     * Get the app scope ID.
     */
    public function getAppScopeId(): string
    {
        return $this->app_scope_id;
    }

    /**
     * Get the user ID through the token relation
     */
    public function getUserId(): ?int
    {
        return $this->token->user_id ?? null;
    }

    /**
     * Get the platform ID through the token relation
     */
    public function getPlatformId(): ?int
    {
        return $this->token->platform_id ?? null;
    }

    /**
     * Check if token is eligible for refresh
     */
    public function isEligibleForRefresh(): bool
    {
        // Must be active, not expired, and have business permissions
        if (!$this->is_active || $this->isExpired() || !$this->hasBusinessPermission()) {
            return false;
        }

        // Must be at least 24 hours old
        return $this->created_at->diffInHours(now()) >= 24;
    }

    /**
     * Get days until token expiration
     */
    public function getDaysUntilExpirationAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        if ($this->expires_at->isPast()) {
            return 0;
        }

        return $this->expires_at->diffInDays(now());
    }

    /**
     * Check if token needs refresh (within 7 days of expiration)
     */
    public function needsRefresh(): bool
    {
        if (!$this->expires_at) {
            return false;
        }

        // Refresh if less than 7 days until expiration
        return $this->expires_at->diffInDays(now()) < 7 && !$this->expires_at->isPast();
    }
}