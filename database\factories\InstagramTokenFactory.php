<?php

declare (strict_types = 1);

namespace Database\Factories;

use App\Models\InstagramToken;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<InstagramToken>
 */
final class InstagramTokenFactory extends Factory
{
    protected $model = InstagramToken::class;

    public function definition(): array
    {
        return [
            'app_scope_id'            => $this->faker->uuid(),
            'ig_id'                   => (string) $this->faker->numberBetween(************, ************),
            'username'                => $this->faker->userName(),
            'name'                    => $this->faker->name(),
            'account_type'            => $this->faker->randomElement(['BUSINESS', 'PERSONAL']),
            'profile_picture_url'     => $this->faker->imageUrl(),
            'followers_count'         => $this->faker->numberBetween(100, 1000000),
            'follows_count'           => $this->faker->numberBetween(10, 10000),
            'media_count'             => $this->faker->numberBetween(0, 5000),
            'access_token'            => $this->faker->sha256(),
            'token_type'              => 'bearer',
            'expires_at'              => now()->addDays(30),
            'status'                  => 'active',
            'is_active'               => true,
            'scopes'                  => ['instagram_basic', 'pages_messaging'],
            'has_business_permission' => true,
            'page_id'                 => (string) $this->faker->numberBetween(************000, ************999),
        ];
    }

    public function inactive(): self
    {
        return $this->state(fn() => [
            'status'    => 'expired',
            'is_active' => false,
        ]);
    }
}
