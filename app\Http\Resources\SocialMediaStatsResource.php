<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SocialMediaStatsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'platform'      => $this->resource['platform'] ?? 'unknown',
            'overview'      => [
                'total_messages'   => $this->resource['total_messages'] ?? 0,
                'replied_messages' => $this->resource['replied_messages'] ?? 0,
                'pending_messages' => $this->resource['pending_messages'] ?? 0,
                'failed_messages'  => $this->resource['failed_messages'] ?? 0,
                'response_rate'    => $this->calculateResponseRate(),
            ],
            'conversations' => [
                'unique_conversations'              => $this->resource['unique_conversations'] ?? 0,
                'unique_senders'                    => $this->resource['unique_senders'] ?? 0,
                'average_messages_per_conversation' => $this->calculateAverageMessagesPerConversation(),
            ],
            'time_periods'  => [
                'today_messages'      => $this->resource['today_messages'] ?? 0,
                'this_week_messages'  => $this->resource['this_week_messages'] ?? 0,
                'this_month_messages' => $this->resource['this_month_messages'] ?? 0,
            ],
            'engagement'    => [
                'response_rate'         => $this->resource['response_rate'] ?? 0,
                'average_response_time' => $this->resource['average_response_time'] ?? 0,
                'engagement_score'      => $this->resource['engagement_score'] ?? 0,
            ],
            'generated_at'  => now()->toISOString(),
        ];
    }

    private function calculateResponseRate(): float
    {
        $total   = $this->resource['total_messages'] ?? 0;
        $replied = $this->resource['replied_messages'] ?? 0;

        if ($total === 0) {
            return 0.0;
        }

        return round(($replied / $total) * 100, 2);
    }

    private function calculateAverageMessagesPerConversation(): float
    {
        $totalMessages       = $this->resource['total_messages'] ?? 0;
        $uniqueConversations = $this->resource['unique_conversations'] ?? 0;

        if ($uniqueConversations === 0) {
            return 0.0;
        }

        return round($totalMessages / $uniqueConversations, 2);
    }
}