<?php

declare (strict_types = 1);

namespace App\Repositories\Agent;

use App\Models\Agent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface AgentRepositoryInterface
{
    public function getAll(): Collection;

    public function getAllWithFilters(array $filters = []): Collection;

    public function getPaginated(int $perPage = 15, array $filters = []): LengthAwarePaginator;

    public function findById(string $id): ?Agent;

    public function findByKey(string $key): ?Agent;

    public function getActive(): Collection;

    public function search(string $query): Collection;

    public function updateStatus(Agent $agent, bool $isActive): Agent;

    public function getWithUserAgentsCount(string $id): ?Agent;


}