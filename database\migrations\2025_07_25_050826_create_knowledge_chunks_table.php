<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('knowledge_chunks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('knowledge_file_id')->constrained()->onDelete('cascade');
            $table->text('content');
            $table->vector('embedding', 768);
            $table->integer('chunk_index');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['knowledge_file_id', 'chunk_index']);
        });

        // Create vector index for similarity search
        DB::statement('CREATE INDEX knowledge_chunks_embedding_idx ON knowledge_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('knowledge_chunks');
    }
};