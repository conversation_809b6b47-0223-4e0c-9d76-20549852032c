<?php

declare (strict_types = 1);

namespace App\Services;

final class PlatformUrlService
{
    /**
     * Get webhook URL for a specific platform
     */
    public function getWebhookUrl(string $platform): string
    {
        $baseUrl = config('services.integrations.webhook_base_url');
        $path    = config("services.integrations.urls.{$platform}.webhook");

        return $this->buildUrl($baseUrl, $path);
    }

    /**
     * Get auth URL for a specific platform
     */
    public function getAuthUrl(string $platform): ?string
    {
        $baseUrl = config('services.integrations.auth_base_url');
        $path    = config("services.integrations.urls.{$platform}.auth");

        if (!$path) {
            return null;
        }

        return $this->buildUrl($baseUrl, $path);
    }

    /**
     * Get full webhook URL for a specific platform
     */
    public function getFullWebhookUrl(string $platform): string
    {
        $baseUrl = config('services.integrations.webhook_base_url');
        $path    = config("services.integrations.urls.{$platform}.webhook");

        return $this->buildUrl($baseUrl, $path);
    }

    /**
     * Get full auth URL for a specific platform
     */
    public function getFullAuthUrl(string $platform): ?string
    {
        $baseUrl = config('services.integrations.auth_base_url');
        $path    = config("services.integrations.urls.{$platform}.auth");

        if (!$path) {
            return null;
        }

        return $this->buildUrl($baseUrl, $path);
    }

    /**
     * Get all URLs for a specific platform
     */
    public function getPlatformUrls(string $platform): array
    {
        return [
            'webhook_url'      => $this->getWebhookUrl($platform),
            'auth_url'         => $this->getAuthUrl($platform),
            'full_webhook_url' => $this->getFullWebhookUrl($platform),
            'full_auth_url'    => $this->getFullAuthUrl($platform),
        ];
    }

    /**
     * Get all available platform URLs
     */
    public function getAllPlatformUrls(): array
    {
        $platforms = array_keys(config('services.integrations.urls', []));
        $urls      = [];

        foreach ($platforms as $platform) {
            $urls[$platform] = $this->getPlatformUrls($platform);
        }

        return $urls;
    }

    /**
     * Build a complete URL from base URL and path
     */
    private function buildUrl(string $baseUrl, string $path): string
    {
        // Remove trailing slash from base URL
        $baseUrl = rtrim($baseUrl, '/');

        // Ensure path starts with /
        $path = '/' . ltrim($path, '/');

        return $baseUrl . $path;
    }

    /**
     * Check if a platform has webhook support
     */
    public function hasWebhookSupport(string $platform): bool
    {
        $webhookUrl = config("services.integrations.urls.{$platform}.webhook");
        return !empty($webhookUrl);
    }

    /**
     * Check if a platform has auth support
     */
    public function hasAuthSupport(string $platform): bool
    {
        $authUrl = config("services.integrations.urls.{$platform}.auth");
        return !empty($authUrl);
    }

    /**
     * Get supported platforms
     */
    public function getSupportedPlatforms(): array
    {
        return array_keys(config('services.integrations.urls', []));
    }

    /**
     * Get platforms with webhook support
     */
    public function getWebhookSupportedPlatforms(): array
    {
        $platforms = [];

        foreach ($this->getSupportedPlatforms() as $platform) {
            if ($this->hasWebhookSupport($platform)) {
                $platforms[] = $platform;
            }
        }

        return $platforms;
    }

    /**
     * Get platforms with auth support
     */
    public function getAuthSupportedPlatforms(): array
    {
        $platforms = [];

        foreach ($this->getSupportedPlatforms() as $platform) {
            if ($this->hasAuthSupport($platform)) {
                $platforms[] = $platform;
            }
        }
        return $platforms;
    }
}