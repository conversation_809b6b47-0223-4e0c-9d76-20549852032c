<?php

namespace Database\Factories;

use App\Models\Agent;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentMetric>
 */
class AgentMetricFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $bookingCount  = $this->faker->numberBetween(10, 100);
        $responseCount = $this->faker->numberBetween(5, $bookingCount);
        $responseRate  = ($responseCount / $bookingCount) * 100;

        return [
            'agent_id'            => Agent::factory(),
            'date'                => $this->faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d'),
            'booking_count'       => $bookingCount,
            'response_count'      => $responseCount,
            'response_rate'       => round($responseRate, 2),
            'message_count'       => $this->faker->numberBetween(20, 200),
            'successful_bookings' => $this->faker->numberBetween(0, $responseCount),
            'additional_metrics'  => [
                'avg_response_time'     => $this->faker->numberBetween(10, 300),
                'customer_satisfaction' => $this->faker->randomFloat(1, 3.5, 5.0),
                'peak_hours'            => [
                    $this->faker->numberBetween(9, 12),
                    $this->faker->numberBetween(13, 17),
                ],
            ],
        ];
    }

    /**
     * Configure the model factory to create metrics for today.
     */
    public function today(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'date' => now()->toDateString(),
            ];
        });
    }

    /**
     * Configure the model factory to create metrics with high performance.
     */
    public function highPerformance(): self
    {
        return $this->state(function (array $attributes) {
            $bookingCount  = $this->faker->numberBetween(50, 100);
            $responseCount = $this->faker->numberBetween($bookingCount - 10, $bookingCount);
            $responseRate  = ($responseCount / $bookingCount) * 100;

            return [
                'booking_count'       => $bookingCount,
                'response_count'      => $responseCount,
                'response_rate'       => round($responseRate, 2),
                'successful_bookings' => $this->faker->numberBetween($responseCount - 10, $responseCount),
            ];
        });
    }
}
