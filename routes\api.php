<?php

declare (strict_types = 1);

use App\Http\Controllers\Api\AgentsController;
use App\Http\Controllers\Api\AppointmentsController;
use App\Http\Controllers\Api\InstagramAuthController;
use App\Http\Controllers\Api\InstagramWebhookController;
use App\Http\Controllers\Api\MediaController;
use App\Http\Controllers\Api\PlatformsController;
use App\Http\Controllers\Api\SocialMediaController;
use App\Http\Controllers\Auth\AuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
 */

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status'    => 'ok',
        'timestamp' => now(),
        'service'   => 'Instagram Chatbot API',
    ]);
});

// =============================================================================
// API v1 ENDPOINTS (Protected)
// =============================================================================
Route::prefix('v1')->group(function () {

    // =============================================================================
// INSTAGRAM OAUTH API (Protected - requires authentication)
// =============================================================================
    Route::middleware('auth:sanctum')->prefix('auth/instagram')->group(function () {
        Route::get('/', [InstagramAuthController::class, 'redirectToInstagram']);
        Route::post('/callback', [InstagramAuthController::class, 'handleCallbackJson']);
        Route::get('/status', [InstagramAuthController::class, 'getConnectionStatus']);
        Route::post('/disconnect', [InstagramAuthController::class, 'disconnect']);
        Route::post('/refresh', [InstagramAuthController::class, 'refreshToken']);
    });

    // =============================================================================
    // SOCIAL MEDIA WEBHOOKS (Public)
    // =============================================================================
    Route::prefix('webhooks')->group(function () {
        // Instagram webhook
        Route::get('instagram', [InstagramWebhookController::class, 'verify']);
        Route::post('instagram', [InstagramWebhookController::class, 'webhook']);
    });

    // =============================================================================
    // AUTH ROUTES
    // =============================================================================
    Route::prefix('auth')->group(function () {
        // Public auth routes
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('otp-verify', [AuthController::class, 'otpVerify']);
        Route::post('resend-otp', [AuthController::class, 'resendOtp']);

        // Protected auth routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('logout', [AuthController::class, 'logout']);
            Route::get('user', [AuthController::class, 'user']);
            Route::post('profile', [AuthController::class, 'updateProfile']);
            Route::post('reset-password', [AuthController::class, 'resetPassword']);
        });
    });

    // Protected routes that require authentication
    Route::middleware('auth:sanctum')->group(function () {

        // =============================================================================
        // PLATFORM CHANNELS
        // =============================================================================
        Route::prefix('platforms')->group(function () {
            // Public routes for platform status and auth URLs
            Route::get('available', [PlatformsController::class, 'getAvailablePlatforms']);
            Route::get('category/{category}', [PlatformsController::class, 'getServicesByCategory']);
            Route::get('service/{service}', [PlatformsController::class, 'getService']);
            Route::get('service/{service}/auth-url', [PlatformsController::class, 'getAuthUrl']);
            Route::get('service/{service}/webhook-url', [PlatformsController::class, 'getWebhookUrl']);
            Route::get('service/{service}/urls', [PlatformsController::class, 'getServiceUrls']);
            Route::get('service/{service}/status', [PlatformsController::class, 'getConnectionStatus']);
            Route::get('status', [PlatformsController::class, 'getAllServicesStatus']);

            // Protected routes
            Route::middleware('auth:sanctum')->group(function () {
                // Superadmin routes for managing platforms
                Route::put('service/{service}/toggle', [PlatformsController::class, 'toggleService']);
                Route::get('all', [PlatformsController::class, 'getAllServices']);
            });
        });

        // =============================================================================
        // SOCIAL MEDIA MANAGEMENT
        // =============================================================================
        Route::prefix('social')->group(function () {
            // Send messages
            Route::post('send-message', [SocialMediaController::class, 'sendMessage']);

            // Platform-specific endpoints
            Route::prefix('{platform}')->where(['platform' => 'instagram|facebook'])->group(function () {
                Route::get('messages', [SocialMediaController::class, 'getMessages']);
                Route::get('stats', [SocialMediaController::class, 'getPlatformStats']);
                Route::get('conversations/history', [SocialMediaController::class, 'getConversationHistory']);
                Route::get('conversations/active', [SocialMediaController::class, 'getActiveConversations']);
                Route::get('messages/pending', [SocialMediaController::class, 'getPendingMessages']);
                Route::get('analytics/hourly', [SocialMediaController::class, 'getHourlyDistribution']);
            });
        });

        // =============================================================================
        // AGENTS MANAGEMENT
        // =============================================================================
        Route::prefix('agents')->group(function () {
            Route::get('/', [AgentsController::class, 'index']);
            Route::get('/{id}', [AgentsController::class, 'show']);
            Route::get('/{id}/metrics', [AgentsController::class, 'metrics']);
            Route::put('/{id}/status', [AgentsController::class, 'updateStatus']);
            Route::post('/{id}/subscribe', [AgentsController::class, 'subscribe']);
            Route::post('/{id}/unsubscribe', [AgentsController::class, 'unsubscribe']);
            Route::post('/{id}/user-agent', [AgentsController::class, 'updateUserAgent']);
        });

        // =============================================================================
        // KNOWLEDGE BASE (Integrated into Agents)
        // =============================================================================
        // Knowledge management is now handled through the updateUserAgent endpoint:
        // - PUT /api/agents/{id}/user-agent with knowledge_files[] parameter (upload)
        // - Knowledge files are included in the main agent response (GET /api/agents/{id})

        // =============================================================================
        // MEDIA MANAGEMENT
        // =============================================================================
        Route::prefix('media')->group(function () {
            Route::post('upload', [MediaController::class, 'upload']);
            Route::get('list', [MediaController::class, 'index']);
            Route::delete('{id}', [MediaController::class, 'destroy']);
        });

        // =============================================================================
        // APPOINTMENTS MANAGEMENT - OPTIMIZED UNIFIED ENDPOINTS
        // =============================================================================
        Route::prefix('appointments')->group(function () {
            // Universal GET endpoint - handles all queries: list, search, filter, stats, calendar
            // Examples:
            // GET /appointments - list all
            // GET /appointments?type=search&search=john - search
            // GET /appointments?type=calendar&month=8&year=2025 - calendar
            // GET /appointments?type=stats - statistics
            // GET /appointments?type=today - today's appointments
            // GET /appointments?status=pending&platform=instagram - filtered list
            Route::get('/', [AppointmentsController::class, 'index']);

            // CRUD operations
            Route::post('/', [AppointmentsController::class, 'store']);
            Route::get('/{id}', [AppointmentsController::class, 'show']);
            Route::put('/{id}', [AppointmentsController::class, 'update']);
            Route::delete('/{id}', [AppointmentsController::class, 'destroy']);

            // Universal status change endpoint - handles all status changes
            // Examples:
            // PUT /appointments/123/status {"status": "confirmed"}
            // PUT /appointments/123/status {"status": "cancelled", "notes": "Customer cancelled"}
            Route::put('/{id}/status', [AppointmentsController::class, 'updateStatus']);

            // Utility endpoints
            Route::get('/meta/statuses', [AppointmentsController::class, 'statuses']);
        });
    });
});

// =============================================================================
// FALLBACK ROUTE
// =============================================================================
Route::fallback(function () {
    return response()->json([
        'message'             => 'API endpoint not found',
        'available_endpoints' => [
            'social_media'    => [
                'send_message'         => 'POST /api/v1/social/send-message',
                'platform_stats'       => 'GET /api/v1/social/{platform}/stats',
                'platform_messages'    => 'GET /api/v1/social/{platform}/messages',
                'conversation_history' => 'GET /api/v1/social/{platform}/conversations/history',
            ],
            'webhooks'        => [
                'instagram' => 'POST /api/webhooks/instagram',
            ],
            'platforms'       => [
                'available_services'   => 'GET /api/platforms/available',
                'services_by_category' => 'GET /api/platforms/category/{category}',
                'service_details'      => 'GET /api/platforms/service/{service}',
                'auth_url'             => 'GET /api/platforms/service/{service}/auth-url',
                'webhook_url'          => 'GET /api/platforms/service/{service}/webhook-url',
                'service_urls'         => 'GET /api/platforms/service/{service}/urls',
                'connection_status'    => 'GET /api/platforms/service/{service}/status',
                'all_services_status'  => 'GET /api/platforms/status',
                'toggle_service'       => 'PUT /api/platforms/service/{service}/toggle',
                'all_services'         => 'GET /api/platforms/all',
            ],
            'business_config' => [
                'agent_profile'     => 'GET/PUT /api/v1/agent/profile',
                'business_info'     => 'GET/PUT /api/v1/business/info',
                'platform_channels' => 'GET/PUT /api/v1/platforms/channels',
            ],
            'agents'          => [
                'list_agents'   => 'GET /api/v1/agents',
                'get_agent'     => 'GET /api/v1/agents/{id}',
                'agent_metrics' => 'GET /api/v1/agents/{id}/metrics',
                'update_status' => 'PUT /api/v1/agents/{id}/status',
            ],
            'user_agents'     => [
                'list_user_agents'    => 'GET /api/v1/user-agents',
                'subscribe_agent'     => 'POST /api/v1/user-agents',
                'available_agents'    => 'GET /api/v1/user-agents/available',
                'dashboard'           => 'GET /api/v1/user-agents/dashboard',
                'get_user_agent'      => 'GET /api/v1/user-agents/{id}',
                'update_user_agent'   => 'PUT /api/v1/user-agents/{id}',
                'unsubscribe_agent'   => 'DELETE /api/v1/user-agents/{id}',
                'user_agent_metrics'  => 'GET /api/v1/user-agents/{id}/metrics',
                'update_agent_status' => 'PUT /api/v1/user-agents/{id}/status',
                'reset_to_default'    => 'POST /api/v1/user-agents/{id}/reset',
            ],
            'chatbot'         => [
                'prompts' => 'GET/PUT /api/v1/chatbot/prompts',
            ],
            'auth'            => [
                'register'        => 'POST /api/v1/auth/register',
                'login'           => 'POST /api/v1/auth/login',
                'forgot_password' => 'POST /api/v1/auth/forgot-password',
                'reset_password'  => 'POST /api/v1/auth/reset-password',
            ],
        ],
    ], 404);
});