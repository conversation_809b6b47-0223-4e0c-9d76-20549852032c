<?php

declare (strict_types = 1);

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;

class OtpVerificationRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'exists:users,email'],
            'otp'   => ['required', 'string', 'min:6', 'max:6'],
        ];
    }
}
