<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Media;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

final class MediaController extends Controller
{
    use ApiResponseTrait;

    /**
     * Upload a file and attach it to a model.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'file'              => 'required|file|max:10240', // 10MB max
                'model_type'        => 'required|string',
                'model_id'          => 'required|integer',
                'collection'        => 'sometimes|string|max:255',
                'custom_properties' => 'sometimes|array',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation error',
                    422,
                    ['errors' => $validator->errors()]
                );
            }

            // Get model class from model_type
            $modelClass = '\\App\\Models\\' . $request->model_type;
            if (!class_exists($modelClass)) {
                return $this->errorResponse(
                    'Invalid model type',
                    422,
                    ['error' => 'The specified model type does not exist']
                );
            }

            // Find the model
            $model = $modelClass::find($request->model_id);
            if (!$model) {
                return $this->errorResponse(
                    'Model not found',
                    404,
                    ['error' => 'The specified model could not be found']
                );
            }

            // Check if model uses HasMedia trait
            if (!method_exists($model, 'addMedia')) {
                return $this->errorResponse(
                    'Invalid model',
                    422,
                    ['error' => 'The specified model does not support media attachments']
                );
            }

            // Upload the file
            $collection       = $request->collection ?? 'default';
            $customProperties = $request->custom_properties ?? [];
            $media            = $model->addMedia($request->file('file'), $collection, $customProperties);

            return $this->successResponse(
                $media,
                'File uploaded successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get all media for a model.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'model_type' => 'required|string',
                'model_id'   => 'required|integer',
                'collection' => 'sometimes|string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation error',
                    422,
                    ['errors' => $validator->errors()]
                );
            }

            // Get model class from model_type
            $modelClass = '\\App\\Models\\' . $request->model_type;
            if (!class_exists($modelClass)) {
                return $this->errorResponse(
                    'Invalid model type',
                    422,
                    ['error' => 'The specified model type does not exist']
                );
            }

            // Find the model
            $model = $modelClass::find($request->model_id);
            if (!$model) {
                return $this->errorResponse(
                    'Model not found',
                    404,
                    ['error' => 'The specified model could not be found']
                );
            }

            // Check if model uses HasMedia trait
            if (!method_exists($model, 'getMedia')) {
                return $this->errorResponse(
                    'Invalid model',
                    422,
                    ['error' => 'The specified model does not support media attachments']
                );
            }

            // Get media
            $collection = $request->collection ?? 'default';
            $media      = $model->getMedia($collection);

            return $this->successResponse(
                $media,
                'Media retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Delete a media item.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $media = Media::findOrFail($id);

            // Delete the file from storage
            Storage::disk($media->disk)->delete($media->path);

            // Delete the media record
            $media->delete();

            return $this->successResponse(
                null,
                'Media deleted successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                500,
                ['error' => $e->getMessage()]
            );
        }
    }
}