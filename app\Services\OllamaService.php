<?php

declare (strict_types = 1);

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class OllamaService
{
    private string $apiUrl;
    private string $model;
    private string $embeddingModel;
    private array $defaultOptions;

    public function __construct()
    {
        $this->apiUrl         = rtrim(config('services.ollama.api_url', 'http://localhost:11434'), '/');
        $this->model          = config('services.ollama.model', 'deepseek-r1:8b');
        $this->embeddingModel = config('services.ollama.embedding_model', 'nomic-embed-text');
        $this->defaultOptions = [
            'temperature' => (float) config('services.ollama.temperature', 0.7),
            'top_p'       => (float) config('services.ollama.top_p', 0.9),
            'top_k'       => (int) config('services.ollama.top_k', 40),
            'num_predict' => (int) config('services.ollama.max_tokens', 600),
        ];
    }

    /**
     * Generate text completion using Ollama API
     */
    public function generateCompletion(string $prompt, array $options = []): string
    {
        try {
            $messages = [
                [
                    'role'    => 'system',
                    'content' => 'You are a helpful assistant for our car care business. Keep responses concise.',
                ],
                [
                    'role'    => 'user',
                    'content' => $prompt,
                ],
            ];

            $response = Http::timeout(60)->retry(2, 2000)->post("{$this->apiUrl}/api/chat", [
                'model'    => $this->model,
                'messages' => $messages,
                'stream'   => false,
                'options'  => array_merge($this->defaultOptions, $options),
            ]);

            if (!$response->successful()) {
                Log::error('Ollama API error', [
                    'status'   => $response->status(),
                    'response' => $response->json(),
                ]);
                throw new Exception('Failed to generate completion: ' . $response->status());
            }

            $responseData = $response->json();
            if (!isset($responseData['message']['content'])) {
                throw new Exception('Invalid response format from Ollama API');
            }

            // Clean up response by removing thinking process
            $content = $responseData['message']['content'];
            $content = preg_replace('/<think>.*?<\/think>/s', '', $content);
            return trim($content);

        } catch (Exception $e) {
            Log::error('Ollama completion error', [
                'error'          => $e->getMessage(),
                'prompt_preview' => substr($prompt, 0, 100) . '...',
            ]);
            throw $e;
        }
    }

    /**
     * Generate text completion with conversation history
     */
    public function generateChatCompletion(array $messages, array $options = []): string
    {
        try {
            // Add system message if not present
            if (empty($messages) || $messages[0]['role'] !== 'system') {
                array_unshift($messages, [
                    'role'    => 'system',
                    'content' => 'You are a helpful assistant for our car care business. Keep responses concise.',
                ]);
            }

            $response = Http::timeout(60)->retry(2, 2000)->post("{$this->apiUrl}/api/chat", [
                'model'    => $this->model,
                'messages' => $messages,
                'stream'   => false,
                'options'  => array_merge($this->defaultOptions, $options),
            ]);

            if (!$response->successful()) {
                Log::error('Ollama API error', [
                    'status'   => $response->status(),
                    'response' => $response->json(),
                ]);
                throw new Exception('Failed to generate chat completion: ' . $response->status());
            }

            $responseData = $response->json();
            if (!isset($responseData['message']['content'])) {
                throw new Exception('Invalid response format from Ollama API');
            }

            // Clean up response by removing thinking process
            $content = $responseData['message']['content'];
            $content = preg_replace('/<think>.*?<\/think>/s', '', $content);
            return trim($content);

        } catch (Exception $e) {
            Log::error('Ollama chat completion error', [
                'error'          => $e->getMessage(),
                'messages_count' => count($messages),
            ]);
            throw $e;
        }
    }

    /**
     * Generate embeddings for text using Ollama
     */
    public function generateEmbedding(string $text): array
    {
        try {
            $response = Http::timeout(30)->retry(2, 2000)->post("{$this->apiUrl}/api/embeddings", [
                'model'  => $this->embeddingModel,
                'prompt' => $text,
            ]);

            if (!$response->successful()) {
                Log::error('Ollama embedding API error', [
                    'status'   => $response->status(),
                    'response' => $response->json(),
                ]);
                throw new Exception('Failed to generate embedding: ' . $response->status());
            }

            $responseData = $response->json();
            if (!isset($responseData['embedding'])) {
                throw new Exception('Invalid embedding response format from Ollama API');
            }

            return $responseData['embedding'];

        } catch (Exception $e) {
            Log::error('Ollama embedding error', [
                'error'        => $e->getMessage(),
                'text_preview' => substr($text, 0, 100) . '...',
            ]);
            throw $e;
        }
    }

    /**
     * Check if Ollama service is available
     */
    public function isAvailable(): bool
    {
        try {
            $response = Http::timeout(5)->get("{$this->apiUrl}/api/tags");
            return $response->successful();
        } catch (Exception $e) {
            Log::warning('Ollama service unavailable', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get available models from Ollama
     */
    public function getAvailableModels(): array
    {
        try {
            $response = Http::timeout(5)->get("{$this->apiUrl}/api/tags");

            if (!$response->successful()) {
                return [];
            }

            $data = $response->json();
            if (!isset($data['models'])) {
                return [];
            }

            return array_map(function ($model) {
                return $model['name'];
            }, $data['models']);

        } catch (Exception $e) {
            Log::warning('Failed to get Ollama models', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }
}