<h2 id="authentication">Authentication</h2>
<p>
    Kortana AI uses token-based authentication with Laravel Sanctum. All protected endpoints require a valid
    authentication token in the request header.
</p>

<div class="section-description">
    <p>Authentication flow:</p>
    <ol>
        <li>Register a new account</li>
        <li>Verify email with OTP</li>
        <li>Login to receive an authentication token</li>
        <li>Include the token in subsequent API requests</li>
    </ol>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/register</span>
    </div>
    <div class="description">Register a new user account and receive an email verification OTP</div>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>first_name <span class="required">*</span></td>
                <td>string</td>
                <td>User's first name</td>
            </tr>
            <tr>
                <td>last_name <span class="required">*</span></td>
                <td>string</td>
                <td>User's last name</td>
            </tr>
            <tr>
                <td>email <span class="required">*</span></td>
                <td>string</td>
                <td>User's email address (must be unique)</td>
            </tr>
            <tr>
                <td>password <span class="required">*</span></td>
                <td>string</td>
                <td>User's password (min 8 chars, must include uppercase, lowercase, number, and special character)</td>
            </tr>
            <tr>
                <td>password_confirmation <span class="required">*</span></td>
                <td>string</td>
                <td>Password confirmation (must match password)</td>
            </tr>
            <tr>
                <td>phone</td>
                <td>string</td>
                <td>User's phone number (optional)</td>
            </tr>
            <tr>
                <td>address</td>
                <td>string</td>
                <td>User's address (optional)</td>
            </tr>
            <tr>
                <td>company</td>
                <td>string</td>
                <td>User's company (optional)</td>
            </tr>
            <tr>
                <td>timezone</td>
                <td>string</td>
                <td>User's timezone (optional)</td>
            </tr>
            <tr>
                <td>about</td>
                <td>string</td>
                <td>User's bio (optional)</td>
            </tr>
            <tr>
                <td>avatar</td>
                <td>file</td>
                <td>User's profile picture (optional)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "User registered successfully. Please check your email for verification OTP.",
  "code": 201,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St",
    "company": "Acme Inc",
    "timezone": "UTC",
    "about": "Software developer",
    "avatar": null,
    "email_verified_at": null,
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:30:45",
    "requires_verification": true
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "email": [
      "The email has already been taken."
    ],
    "password": [
      "The password must be at least 8 characters.",
      "The password must contain at least one uppercase letter, one lowercase letter, one number, and one special character."
    ]
  }
}</code></pre>

    <h4>Server Error (500)</h4>
    <pre><code>{
  "success": false,
  "message": "An error occurred during registration",
  "code": 500
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/verify-otp</span>
    </div>
    <div class="description">Verify email address with OTP sent during registration</div>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>email <span class="required">*</span></td>
                <td>string</td>
                <td>User's email address</td>
            </tr>
            <tr>
                <td>otp <span class="required">*</span></td>
                <td>string</td>
                <td>One-time password received via email</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Email verified successfully",
  "code": 200,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St",
    "company": "Acme Inc",
    "timezone": "UTC",
    "about": "Software developer",
    "avatar": null,
    "email_verified_at": "2023-08-15 14:35:22",
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:35:22",
    "token": "1|laravel_sanctum_token_hash"
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Invalid OTP (400)</h4>
    <pre><code>{
  "success": false,
  "message": "Invalid or expired OTP",
  "code": 400
}</code></pre>

    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "email": [
      "The email field is required."
    ],
    "otp": [
      "The otp field is required."
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/login</span>
    </div>
    <div class="description">Login to obtain an authentication token</div>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>email <span class="required">*</span></td>
                <td>string</td>
                <td>User's email address</td>
            </tr>
            <tr>
                <td>password <span class="required">*</span></td>
                <td>string</td>
                <td>User's password</td>
            </tr>
            <tr>
                <td>remember</td>
                <td>boolean</td>
                <td>Whether to remember the user (extends token expiration to 6 months)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Login successful",
  "code": 200,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St",
    "company": "Acme Inc",
    "timezone": "UTC",
    "about": "Software developer",
    "avatar": "https://example.com/storage/avatars/user1.jpg",
    "email_verified_at": "2023-08-15 14:35:22",
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:35:22",
    "token": "1|laravel_sanctum_token_hash"
  }
}</code></pre>

    <h3>Response (Email Not Verified)</h3>
    <pre><code>{
  "success": true,
  "message": "Email not verified. A verification OTP has been sent to your email.",
  "code": 200,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St",
    "company": "Acme Inc",
    "timezone": "UTC",
    "about": "Software developer",
    "avatar": null,
    "email_verified_at": null,
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:30:45",
    "requires_verification": true
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Invalid Credentials (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Invalid credentials",
  "code": 401
}</code></pre>

    <h4>Account Locked (423)</h4>
    <pre><code>{
  "success": false,
  "message": "Account temporarily locked. Please try again later.",
  "code": 423
}</code></pre>

    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "email": [
      "The email field is required."
    ],
    "password": [
      "The password field is required."
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/logout</span>
    </div>
    <div class="description">Invalidate the current authentication token</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Successfully logged out",
  "code": 200
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Unauthorized (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Unauthenticated",
  "code": 401
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/auth/user</span>
    </div>
    <div class="description">Get authenticated user details</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "User details retrieved successfully",
  "code": 200,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": "123 Main St",
    "company": "Acme Inc",
    "timezone": "UTC",
    "about": "Software developer",
    "avatar": "https://example.com/storage/avatars/user1.jpg",
    "email_verified_at": "2023-08-15 14:35:22",
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:35:22"
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Unauthorized (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Unauthenticated",
  "code": 401
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method put">PUT</span>
        <span class="url">/api/v1/auth/profile</span>
    </div>
    <div class="description">Update authenticated user's profile information</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>multipart/form-data</td>
                <td>Required if uploading avatar</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>first_name</td>
                <td>string</td>
                <td>User's first name</td>
            </tr>
            <tr>
                <td>last_name</td>
                <td>string</td>
                <td>User's last name</td>
            </tr>
            <tr>
                <td>phone</td>
                <td>string</td>
                <td>User's phone number</td>
            </tr>
            <tr>
                <td>address</td>
                <td>string</td>
                <td>User's address</td>
            </tr>
            <tr>
                <td>company</td>
                <td>string</td>
                <td>User's company</td>
            </tr>
            <tr>
                <td>timezone</td>
                <td>string</td>
                <td>User's timezone</td>
            </tr>
            <tr>
                <td>about</td>
                <td>string</td>
                <td>User's bio</td>
            </tr>
            <tr>
                <td>avatar</td>
                <td>file</td>
                <td>User's profile picture</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Profile updated successfully",
  "code": 200,
  "data": {
    "id": 1,
    "first_name": "John",
    "last_name": "Smith",
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1987654321",
    "address": "456 Oak St",
    "company": "New Company Inc",
    "timezone": "America/New_York",
    "about": "Senior software developer",
    "avatar": "https://example.com/storage/avatars/user1_updated.jpg",
    "email_verified_at": "2023-08-15 14:35:22",
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 15:45:12"
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "first_name": [
      "The first name field is required when last name is present."
    ],
    "avatar": [
      "The avatar must be an image.",
      "The avatar must not be greater than 2048 kilobytes."
    ]
  }
}</code></pre>

    <h4>User Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "User not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/forgot-password</span>
    </div>
    <div class="description">Request a password reset OTP</div>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>email <span class="required">*</span></td>
                <td>string</td>
                <td>User's email address</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Password reset link sent to your email",
  "code": 200
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "email": [
      "The email field is required.",
      "The email must be a valid email address."
    ]
  }
}</code></pre>

    <h4>User Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "User not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/reset-password</span>
    </div>
    <div class="description">Reset password using OTP</div>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>email <span class="required">*</span></td>
                <td>string</td>
                <td>User's email address</td>
            </tr>
            <tr>
                <td>otp <span class="required">*</span></td>
                <td>string</td>
                <td>One-time password received via email</td>
            </tr>
            <tr>
                <td>password <span class="required">*</span></td>
                <td>string</td>
                <td>New password (min 8 chars, must include uppercase, lowercase, number, and special character)</td>
            </tr>
            <tr>
                <td>password_confirmation <span class="required">*</span></td>
                <td>string</td>
                <td>New password confirmation (must match password)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Password reset successfully",
  "code": 200
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "email": [
      "The email field is required."
    ],
    "otp": [
      "The otp field is required."
    ],
    "password": [
      "The password field is required.",
      "The password must be at least 8 characters.",
      "The password confirmation does not match."
    ]
  }
}</code></pre>

    <h4>Invalid OTP (400)</h4>
    <pre><code>{
  "success": false,
  "message": "Invalid or expired OTP",
  "code": 400
}</code></pre>

    <h4>User Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "User not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/auth/resend-verification</span>
    </div>
    <div class="description">Resend email verification OTP</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Verification email sent successfully",
  "code": 200
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Unauthorized (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Unauthenticated",
  "code": 401
}</code></pre>

    <h4>Email Already Verified (400)</h4>
    <pre><code>{
  "success": false,
  "message": "Email already verified",
  "code": 400
}</code></pre>

    <h4>User Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "User not found",
  "code": 404
}</code></pre>
</div>

<div class="section-notes">
    <h3>Authentication Notes</h3>
    <ul>
        <li>All tokens are generated using Laravel Sanctum</li>
        <li>Default token expiration is 24 hours</li>
        <li>With "remember me" option, tokens expire after 6 months</li>
        <li>After 5 failed login attempts, the account is temporarily locked for 15 minutes</li>
        <li>Email verification is required before full access is granted</li>
        <li>Password reset OTPs expire after 10 minutes</li>
        <li>All authentication activities are logged for security purposes</li>
    </ul>
</div>
