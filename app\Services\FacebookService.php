<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\Message;
use App\Repositories\Message\MessageRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class FacebookService
{
    public function __construct(
        private readonly MessageRepositoryInterface $messageRepository
    ) {}

    /**
     * Send message to Facebook user
     */
    public function sendMessage(string $recipientId, string $message): bool
    {
        try {
            $accessToken = config('services.meta.facebook_access_token');
            $pageId      = config('services.meta.facebook_page_id');

            if (!$accessToken || !$pageId) {
                Log::error('Facebook credentials not configured');
                return false;
            }

            $response = Http::timeout(30)->retry(3, 1000)->post("https://graph.facebook.com/v23.0/{$pageId}/messages", [
                'recipient'    => ['id' => $recipientId],
                'message'      => ['text' => $message],
                'access_token' => $accessToken,
            ]);

            if ($response->successful()) {
                Log::info('Facebook message sent successfully', [
                    'recipient_id'   => $recipientId,
                    'message_length' => strlen($message),
                ]);
                return true;
            }

            Log::error('Failed to send Facebook message', [
                'recipient_id' => $recipientId,
                'status'       => $response->status(),
                'response'     => $response->json(),
            ]);

            return false;

        } catch (Exception $e) {
            Log::error('Facebook message sending failed', [
                'recipient_id' => $recipientId,
                'error'        => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Process incoming Facebook message
     */
    public function processIncomingMessage(array $messageData): Message
    {
        // Store the message
        $message = $this->messageRepository->create([
            'sender_id' => $messageData['sender_id'],
            'message'   => $messageData['content'], // Changed from 'content' to 'message'
            'platform'  => 'facebook',
            'sent_at'   => now(),
            'status'    => 'received',
            'meta_data' => $messageData['meta_data'] ?? [],
        ]);

        Log::info('Facebook message processed', [
            'message_id' => $message->id,
            'sender_id'  => $message->sender_id,
        ]);

        return $message;
    }

    /**
     * Verify webhook token
     */
    public function verifyWebhookToken(string $token): bool
    {
        $verifyToken = config('services.meta.facebook_webhook_verify_token');
        return $token === $verifyToken;
    }

    /**
     * Get Facebook user profile
     */
    public function getUserProfile(string $userId): ?array
    {
        try {
            $accessToken = config('services.meta.facebook_access_token');

            if (!$accessToken) {
                return null;
            }

            $response = Http::timeout(30)->retry(3, 1000)->get("https://graph.facebook.com/v23.0/{$userId}", [
                'fields'       => 'name,profile_pic',
                'access_token' => $accessToken,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::warning('Failed to get Facebook user profile', [
                'user_id' => $userId,
                'status'  => $response->status(),
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('Facebook user profile fetch failed', [
                'user_id' => $userId,
                'error'   => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Handle Facebook postback
     */
    public function handlePostback(array $postbackData): bool
    {
        try {
            Log::info('Facebook postback received', [
                'sender_id' => $postbackData['sender_id'] ?? null,
                'payload'   => $postbackData['payload'] ?? null,
            ]);

            // Process postback based on payload
            $payload  = $postbackData['payload'] ?? '';
            $senderId = $postbackData['sender_id'] ?? '';

            if (empty($payload) || empty($senderId)) {
                return false;
            }

            // Handle different postback types
            switch ($payload) {
                case 'GET_STARTED':
                    return $this->sendWelcomeMessage($senderId);
                case 'BOOK_APPOINTMENT':
                    return $this->sendBookingPrompt($senderId);
                default:
                    return $this->sendGenericResponse($senderId);
            }

        } catch (Exception $e) {
            Log::error('Facebook postback handling failed', [
                'error'         => $e->getMessage(),
                'postback_data' => $postbackData,
            ]);

            return false;
        }
    }

    /**
     * Send welcome message
     */
    private function sendWelcomeMessage(string $recipientId): bool
    {
        $welcomeMessage = "Welcome to Frame Auto Styling! 🚗✨ How can I help you today?";
        return $this->sendMessage($recipientId, $welcomeMessage);
    }

    /**
     * Send booking prompt
     */
    private function sendBookingPrompt(string $recipientId): bool
    {
        $bookingMessage = "I'd be happy to help you book an appointment! What type of service are you interested in?";
        return $this->sendMessage($recipientId, $bookingMessage);
    }

    /**
     * Send generic response
     */
    private function sendGenericResponse(string $recipientId): bool
    {
        $genericMessage = "Thanks for reaching out! How can I assist you today?";
        return $this->sendMessage($recipientId, $genericMessage);
    }
}
