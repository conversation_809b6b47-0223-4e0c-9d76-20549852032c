<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agents', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key')->unique(); // unique key generated from name
            $table->string('description')->nullable();
            $table->json('default_capabilities')->nullable(); // default capabilities of this agent
            $table->json('default_configuration')->nullable(); // default agent configuration
            $table->json('default_prompt_templates')->nullable(); // default templates for agent responses (centralized)
            $table->boolean('is_active')->default(true); // whether this agent type is available for use
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agents');
    }
};
