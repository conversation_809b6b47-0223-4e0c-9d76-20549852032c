<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\UserAgent;
use App\Repositories\BusinessEmbedding\BusinessEmbeddingRepositoryInterface;
use App\Repositories\Knowledge\KnowledgeRepositoryInterface;
use App\Services\Chatbot\ChatbotAIService;
use Exception;
use Illuminate\Support\Facades\Log;

final class KnowledgeSearchService
{
    public function __construct(
        private readonly KnowledgeRepositoryInterface $knowledgeRepository,
        private readonly BusinessEmbeddingRepositoryInterface $businessEmbeddingRepository,
        private readonly ChatbotAIService $aiService
    ) {
    }

    /**
     * Find relevant knowledge using vector similarity search (UserAgent-specific)
     */
    public function findRelevantKnowledge(string $userMessage, ?UserAgent $userAgent = null): array
    {
        // Skip knowledge search for simple greetings and short messages
        $simpleGreetings = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening', 'thanks', 'thank you', 'bye', 'goodbye'];
        $lowerMessage    = strtolower(trim($userMessage));

        if (strlen($lowerMessage) <= 10 || in_array($lowerMessage, $simpleGreetings)) {
            Log::info('Skipping knowledge search for simple greeting', ['message' => $userMessage]);
            return [];
        }

        try {
            // Generate embedding for user message
            $embedding = $this->aiService->generateEmbedding($userMessage);

            // Find similar chunks - prioritize UserAgent-specific knowledge if available
            if ($userAgent && $userAgent->knowledgeFiles->isNotEmpty()) {
                Log::info('Searching UserAgent-specific knowledge', [
                    'user_agent_id'         => $userAgent->id,
                    'knowledge_files_count' => $userAgent->knowledgeFiles->count(),
                ]);

                // Get knowledge file IDs for this user agent
                $knowledgeFileIds = $userAgent->knowledgeFiles->pluck('id')->toArray();

                // Search within user agent's knowledge files first
                $similarChunks = $this->knowledgeRepository->findSimilarChunksForUserAgent(
                    $userAgent->id,
                    $embedding,
                    3// max chunks from user agent knowledge
                );

                // If we found enough relevant chunks, use them
                if ($similarChunks->count() >= 2) {
                    return $similarChunks->map(function ($chunk) {
                        return [
                            'content'       => $chunk->content,
                            'source'        => $chunk->knowledgeFile->original_name ?? 'User Knowledge',
                            'distance'      => $chunk->distance ?? null,
                            'user_specific' => true,
                        ];
                    })->toArray();
                }

                // If not enough user-specific knowledge, supplement with global knowledge
                $globalChunks = $this->knowledgeRepository->findSimilarChunksWithThreshold(
                    $embedding,
                    0.75, // slightly higher threshold for global knowledge
                    2// fewer global chunks to supplement
                );

                // Combine user-specific and global knowledge
                $combinedChunks = $similarChunks->concat($globalChunks)->take(5);

                return $combinedChunks->map(function ($chunk) use ($knowledgeFileIds) {
                    return [
                        'content'       => $chunk->content,
                        'source'        => $chunk->knowledgeFile->original_name ?? 'Knowledge Base',
                        'distance'      => $chunk->distance ?? null,
                        'user_specific' => in_array($chunk->knowledge_file_id, $knowledgeFileIds),
                    ];
                })->toArray();
            }

            // Fallback to global knowledge search
            Log::info('Using global knowledge search', [
                'user_agent_id' => $userAgent?->id,
                'reason'        => $userAgent ? 'no user-specific knowledge' : 'no user agent',
            ]);

            $similarChunks = $this->knowledgeRepository->findSimilarChunksWithThreshold(
                $embedding,
                0.7, // similarity threshold
                5// max chunks
            );

            return $similarChunks->map(function ($chunk) {
                return [
                    'content'       => $chunk->content,
                    'source'        => $chunk->knowledgeFile->original_name ?? 'Knowledge Base',
                    'distance'      => $chunk->distance ?? null,
                    'user_specific' => false,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::warning('Failed to find relevant knowledge', [
                'error'         => $e->getMessage(),
                'message'       => $userMessage,
                'user_agent_id' => $userAgent?->id,
            ]);

            return [];
        }
    }

    /**
     * Find relevant business context using embeddings
     */
    public function findRelevantBusinessContext(string $userMessage, ?UserAgent $userAgent): array
    {
        if (!$userAgent) {
            return [];
        }

        try {
            // Generate embedding for user message
            $embedding = $this->aiService->generateEmbedding($userMessage);

            // Find similar business embeddings
            $similarEmbeddings = $this->businessEmbeddingRepository->findSimilar(
                $userAgent->id,
                $embedding,
                3// limit to top 3 most relevant
            );

            return $similarEmbeddings->map(function ($embedding) {
                return [
                    'content' => $embedding->content,
                    'type'    => $embedding->content_type,
                    'key'     => $embedding->content_key,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::warning('Failed to find relevant business context', [
                'error'         => $e->getMessage(),
                'user_agent_id' => $userAgent?->id,
                'message'       => $userMessage,
            ]);
            return [];
        }
    }

}