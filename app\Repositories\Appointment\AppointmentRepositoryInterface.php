<?php

declare (strict_types = 1);

namespace App\Repositories\Appointment;

use App\Models\Appointment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface AppointmentRepositoryInterface
{
    /**
     * Universal query method for appointments with filters and options
     */
    public function queryAppointments(array $options = []): LengthAwarePaginator | Collection | array | int;

    /**
     * Find appointment by ID
     */
    public function findAppointment(int $id, int $userId = null): ?Appointment;

    /**
     * Create a new appointment
     */
    public function createAppointment(array $data): Appointment;

    /**
     * Update an appointment
     */
    public function updateAppointment(int $id, array $data): bool;

    /**
     * Update appointment status with optional notes
     */
    public function updateAppointmentStatus(int $id, string $status, int $userId = null, ?string $notes = null): bool;

    /**
     * Delete an appointment
     */
    public function deleteAppointment(int $id): bool;

    /**
     * Check if time slot is available
     */
    public function isTimeSlotAvailable(string $date, string $time, int $userAgentId, int $excludeId = null): bool;

    /**
     * Get available statuses
     */
    public function getAvailableStatuses(): array;

    /**
     * Count bookings overlapping a given time window for a user agent on a date
     */
    public function countOverlappingBookings(int $userAgentId, string $date, string $startTime, string $endTime): int;
}