<?php

declare (strict_types = 1);

namespace App\Jobs;

use App\Models\KnowledgeFile;
use App\Services\KnowledgeService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessKnowledgeFile implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run before timing out.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly KnowledgeFile $knowledgeFile
    ) {
        // Set queue name for knowledge processing
        $this->onQueue('knowledge');
    }

    /**
     * Execute the job.
     */
    public function handle(KnowledgeService $knowledgeService): void
    {
        Log::info('Starting knowledge file processing job', [
            'file_id'   => $this->knowledgeFile->id,
            'file_name' => $this->knowledgeFile->original_name,
            'attempt'   => $this->attempts(),
        ]);

        try {
            // Process the file
            $knowledgeService->processFile($this->knowledgeFile);

            Log::info('Knowledge file processing job completed successfully', [
                'file_id'   => $this->knowledgeFile->id,
                'file_name' => $this->knowledgeFile->original_name,
            ]);

        } catch (\Exception $e) {
            Log::error('Knowledge file processing job failed', [
                'file_id'   => $this->knowledgeFile->id,
                'file_name' => $this->knowledgeFile->original_name,
                'error'     => $e->getMessage(),
                'attempt'   => $this->attempts(),
            ]);

            // If this is the final attempt, mark the file as failed
            if ($this->attempts() >= $this->tries) {
                $this->knowledgeFile->update([
                    'status'        => 'failed',
                    'error_message' => $e->getMessage(),
                ]);
            }

            // Re-throw the exception to trigger retry if attempts remain
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Knowledge file processing job permanently failed', [
            'file_id'        => $this->knowledgeFile->id,
            'file_name'      => $this->knowledgeFile->original_name,
            'error'          => $exception->getMessage(),
            'total_attempts' => $this->tries,
        ]);

        // Mark file as failed in database
        $this->knowledgeFile->update([
            'status'        => 'failed',
            'error_message' => $exception->getMessage(),
        ]);
    }
}
