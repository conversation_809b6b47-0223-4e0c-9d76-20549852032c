<?php

declare (strict_types = 1);

namespace Database\Seeders;

use App\Models\Appointment;
use App\Models\Token;
use App\Models\UserAgent;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

final class AppointmentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Log::info('Starting Appointments Seeder...');

        // Get all active user agents with business data
        $userAgents = UserAgent::where('status', 'active')
            ->whereNotNull('agent_business_data')
            ->with(['user'])
            ->get();

        if ($userAgents->isEmpty()) {
            Log::warning('No active user agents found with business data. Skipping appointments seeding.');
            return;
        }

        $totalAppointments = 0;

        foreach ($userAgents as $userAgent) {
            Log::info("Creating appointments for UserAgent: {$userAgent->name} (ID: {$userAgent->id})");

            // Get business services from user agent
            $businessData = $this->ensureArrayFromJson($userAgent->agent_business_data);
            $services     = $businessData['business_services'] ?? [];

            if (empty($services)) {
                Log::info("No services found for UserAgent {$userAgent->id}, skipping...");
                continue;
            }

            // Get available tokens for this user agent (only our custom Token model)
            $tokens = Token::where('user_id', $userAgent->user_id)
                ->with(['platform', 'tokenable'])
                ->get();

            if ($tokens->isEmpty()) {
                Log::info("No tokens found for UserAgent {$userAgent->id}, creating appointments without token...");
            }

            // Create appointments for the past 30 days, current week, and future 60 days
            $appointmentsCreated = $this->createAppointmentsForUserAgent($userAgent, $services, $tokens);
            $totalAppointments += $appointmentsCreated;

            Log::info("Created {$appointmentsCreated} appointments for UserAgent {$userAgent->id}");
        }

        Log::info("Appointments Seeder completed successfully!");
        Log::info("Total appointments created: {$totalAppointments}");
    }

    /**
     * Create appointments for a specific user agent
     */
    private function createAppointmentsForUserAgent(UserAgent $userAgent, array $services, $tokens): int
    {
        $appointmentsCreated = 0;
        $businessData        = $this->ensureArrayFromJson($userAgent->agent_business_data);
        $businessHours       = $businessData['business_hours'] ?? [];
        $weekendSettings     = $businessData['weekend_settings'] ?? [];

        // Define date ranges
        $dateRanges = [
            'past'    => [Carbon::now()->subDays(30), Carbon::now()->subDay(), ['completed', 'cancelled', 'no_show']],
            'current' => [Carbon::now(), Carbon::now()->addDays(7), ['pending', 'confirmed', 'in_progress']],
            'future'  => [Carbon::now()->addDays(8), Carbon::now()->addDays(60), ['pending', 'confirmed']],
        ];

        foreach ($dateRanges as $period => $range) {
            [$startDate, $endDate, $allowedStatuses] = $range;

            $appointmentsInPeriod = $this->createAppointmentsForDateRange(
                $userAgent,
                $services,
                $tokens,
                $startDate,
                $endDate,
                $allowedStatuses,
                $businessHours,
                $weekendSettings,
                $period
            );

            $appointmentsCreated += $appointmentsInPeriod;
        }

        return $appointmentsCreated;
    }

    /**
     * Create appointments for a specific date range
     */
    private function createAppointmentsForDateRange(
        UserAgent $userAgent,
        array $services,
        $tokens,
        Carbon $startDate,
        Carbon $endDate,
        array $allowedStatuses,
        array $businessHours,
        array $weekendSettings,
        string $period
    ): int {
        $appointmentsCreated = 0;
        $currentDate         = $startDate->copy();

        while ($currentDate <= $endDate) {
            // Skip if no business hours for this day
            $dayName  = strtolower($currentDate->format('l'));
            $dayHours = $businessHours[$dayName] ?? null;

            // Check weekend settings
            if (($dayName === 'saturday' && !($weekendSettings['saturday_enabled'] ?? false)) ||
                ($dayName === 'sunday' && !($weekendSettings['sunday_enabled'] ?? false))) {
                $currentDate->addDay();
                continue;
            }

            if (!$dayHours || $dayHours === null) {
                $currentDate->addDay();
                continue;
            }

            // Determine appointment density based on period
            $appointmentChance = match ($period) {
                'past' => 0.7, // 70% chance of appointments in the past
                'current' => 0.8, // 80% chance for current week
                'future' => 0.4, // 40% chance for future dates
                default => 0.5
            };

            if (rand(1, 100) <= ($appointmentChance * 100)) {
                $dailyAppointments = $this->createDailyAppointments(
                    $userAgent,
                    $services,
                    $tokens,
                    $currentDate,
                    $dayHours,
                    $allowedStatuses,
                    $period
                );
                $appointmentsCreated += $dailyAppointments;
            }

            $currentDate->addDay();
        }

        return $appointmentsCreated;
    }

    /**
     * Create appointments for a specific day
     */
    private function createDailyAppointments(
        UserAgent $userAgent,
        array $services,
        $tokens,
        Carbon $date,
        array $dayHours,
        array $allowedStatuses,
        string $period
    ): int {
        $appointmentsCreated   = 0;
        $maxAppointmentsPerDay = rand(1, 4); // 1-4 appointments per day

        [$startHour, $endHour] = $dayHours;
        $startTime             = Carbon::createFromFormat('H:i', $startHour);
        $endTime               = Carbon::createFromFormat('H:i', $endHour);

        for ($i = 0; $i < $maxAppointmentsPerDay; $i++) {
            // Generate random appointment time within business hours
            $appointmentTime     = $this->generateRandomTimeSlot($startTime, $endTime);
            $appointmentDateTime = $date->copy()->setTimeFromTimeString($appointmentTime);

            // Skip if appointment time conflicts with existing appointments
            $conflictExists = Appointment::where('user_agent_id', $userAgent->id)
                ->where('appointment_date', $date->format('Y-m-d'))
                ->where('appointment_time', $appointmentTime)
                ->exists();

            if ($conflictExists) {
                continue;
            }

            // Select random service
            $serviceKey = array_rand($services);
            $service    = $services[$serviceKey];

            // Select random token if available
            $token = $tokens->isNotEmpty() ? $tokens->random() : null;

            // Create appointment
            $appointment = $this->createSingleAppointment(
                $userAgent,
                $service,
                $serviceKey,
                $token,
                $date,
                $appointmentTime,
                $allowedStatuses,
                $period
            );

            if ($appointment) {
                $appointmentsCreated++;
            }
        }

        return $appointmentsCreated;
    }

    /**
     * Create a single appointment with realistic data
     */
    private function createSingleAppointment(
        UserAgent $userAgent,
        array $service,
        string $serviceKey,
        ?Token $token,
        Carbon $date,
        string $appointmentTime,
        array $allowedStatuses,
        string $period
    ): ?Appointment {
        try {
            // Generate realistic customer data
            $customerData = $this->generateCustomerData($token);

            // Select random status from allowed statuses
            $status = $allowedStatuses[array_rand($allowedStatuses)];

            // Generate booking and confirmation times based on period and status
            $timestamps = $this->generateTimestamps($date, $appointmentTime, $status, $period);

            // Create appointment
            $appointment = Appointment::create([
                'customer_name'        => $customerData['name'],
                'platform_customer_id' => $customerData['platform_customer_id'],
                'platform_username'    => $customerData['platform_username'],
                'platform_name'        => $customerData['platform_name'],
                'customer_phone'       => $customerData['phone'],
                'customer_email'       => $customerData['email'],
                'customer_address'     => $customerData['address'],
                'user_id'              => $userAgent->user_id,
                'user_agent_id'        => $userAgent->id,
                'token_id'             => $token?->id,
                'appointment_date'     => $date->format('Y-m-d'),
                'appointment_time'     => $appointmentTime,
                'duration_minutes'     => $service['duration'] ?? 120,
                'service_name'         => $service['name'],
                'service_key'          => $serviceKey,
                'service_price'        => $service['price'] ?? null,
                'service_category'     => $service['category'] ?? 'general',
                'status'               => $status,
                'notes'                => $this->generateNotes($service, $status),
                'metadata'             => $this->generateMetadata($customerData, $service, $token, $period),
                'booked_at'            => $timestamps['booked_at'],
                'confirmed_at'         => $timestamps['confirmed_at'],
                'booking_method'       => $this->getRandomBookingMethod(),
                'created_at'           => $timestamps['booked_at'],
                'updated_at'           => $timestamps['confirmed_at'] ?? $timestamps['booked_at'],
            ]);

            return $appointment;

        } catch (\Exception $e) {
            Log::error('Failed to create appointment', [
                'error'         => $e->getMessage(),
                'user_agent_id' => $userAgent->id,
                'service'       => $serviceKey,
                'date'          => $date->format('Y-m-d'),
                'time'          => $appointmentTime,
            ]);
            return null;
        }
    }

    /**
     * Generate realistic customer data
     */
    private function generateCustomerData(?Token $token): array
    {
        $firstNames = ['John', 'Jane', 'Mike', 'Sarah', 'David', 'Lisa', 'Chris', 'Emma', 'Ryan', 'Ashley',
            'Kevin', 'Michelle', 'Brian', 'Jessica', 'Mark', 'Amanda', 'Jason', 'Nicole', 'Matt', 'Rachel'];
        $lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez',
            'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'];

        $firstName = $firstNames[array_rand($firstNames)];
        $lastName  = $lastNames[array_rand($lastNames)];
        $fullName  = "{$firstName} {$lastName}";

        // Generate platform-specific customer information
        $platformInfo = $this->generatePlatformCustomerInfo($token);

        return [
            'name'                 => $fullName,
            'phone'                => $this->generatePhoneNumber(),
            'email'                => rand(0, 1) ? strtolower("{$firstName}.{$lastName}@" . ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'][array_rand(['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'])]) : null,
            'address'              => $this->generateAddress(),
            'platform_customer_id' => $platformInfo['customer_id'],
            'platform_username'    => $platformInfo['username'],
            'platform_name'        => $platformInfo['platform'],
        ];
    }

    /**
     * Generate realistic phone number
     */
    private function generatePhoneNumber(): ?string
    {
        if (rand(0, 1) === 0) {
            return null; // 50% chance of no phone number
        }

        $areaCodes = ['416', '647', '437', '905', '289', '365', '519', '226', '548'];
        $areaCode  = $areaCodes[array_rand($areaCodes)];

        return "+1-{$areaCode}-" . rand(100, 999) . "-" . rand(1000, 9999);
    }

    /**
     * Generate realistic address
     */
    private function generateAddress(): ?string
    {
        if (rand(0, 1) === 0) {
            return null; // 50% chance of no address
        }

        $streets   = ['Main St', 'Oak Ave', 'Maple Dr', 'Elm St', 'Pine Rd', 'Cedar Ln', 'Birch Way', 'Willow Ct'];
        $cities    = ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Edmonton', 'Ottawa', 'Winnipeg', 'Quebec City'];
        $provinces = ['ON', 'BC', 'QC', 'AB', 'SK', 'MB', 'NS', 'NB'];

        $streetNumber = rand(100, 9999);
        $street       = $streets[array_rand($streets)];
        $city         = $cities[array_rand($cities)];
        $province     = $provinces[array_rand($provinces)];
        $postalCode   = sprintf('%c%d%c %d%c%d',
            chr(rand(65, 90)), rand(0, 9), chr(rand(65, 90)),
            rand(0, 9), chr(rand(65, 90)), rand(0, 9));

        return "{$streetNumber} {$street}, {$city}, {$province} {$postalCode}";
    }

/**
 * Generate platform-specific customer information
 */
    private function generatePlatformCustomerInfo(?Token $token): array
    {
        if ($token && $token->platform) {
            return match ($token->platform->key) {
                'instagram' => [
                    'customer_id' => (string) rand(100000000000, 999999999999),
                    'username'    => 'user_' . rand(1000, 9999),
                    'platform'    => 'instagram',
                ],
                'facebook' => [
                    'customer_id' => (string) rand(100000000000, 999999999999),
                    'username'    => 'user_' . rand(1000, 9999),
                    'platform'    => 'facebook',
                ],
                default => [
                    'customer_id' => 'customer_' . rand(10000, 99999),
                    'username'    => 'user_' . rand(1000, 9999),
                    'platform'    => 'website',
                ]
            };
        }

        return [
            'customer_id' => 'customer_' . rand(10000, 99999),
            'username'    => 'user_' . rand(1000, 9999),
            'platform'    => 'website',
        ];
    }

    /**
     * Generate random time slot within business hours
     */
    private function generateRandomTimeSlot(Carbon $startTime, Carbon $endTime): string
    {
        $startMinutes = $startTime->hour * 60 + $startTime->minute;
        $endMinutes   = $endTime->hour * 60 + $endTime->minute;

        // Generate time slots in 30-minute intervals
        $possibleSlots = [];
        for ($minutes = $startMinutes; $minutes < $endMinutes - 120; $minutes += 30) {
            $hour            = intval($minutes / 60);
            $minute          = $minutes % 60;
            $possibleSlots[] = sprintf('%02d:%02d:00', $hour, $minute);
        }

        return $possibleSlots[array_rand($possibleSlots)];
    }

    /**
     * Generate timestamps for booking and confirmation
     */
    private function generateTimestamps(Carbon $appointmentDate, string $appointmentTime, string $status, string $period): array
    {
        $appointmentDateTime = Carbon::parse($appointmentDate->format('Y-m-d') . ' ' . $appointmentTime);

        // Generate booking time (1-7 days before appointment for future, actual time for past)
        $daysBeforeBooking = match ($period) {
            'past' => rand(1, 14),
            'current' => rand(1, 7),
            'future' => rand(1, 5),
            default => rand(1, 7)
        };

        $bookedAt = $appointmentDateTime->copy()->subDays($daysBeforeBooking)->subHours(rand(0, 23));

        // Generate confirmation time based on status
        $confirmedAt = null;
        if (in_array($status, ['confirmed', 'in_progress', 'completed'])) {
            $confirmedAt = $bookedAt->copy()->addHours(rand(1, 48));
        }

        return [
            'booked_at'    => $bookedAt,
            'confirmed_at' => $confirmedAt,
        ];
    }

    /**
     * Generate realistic notes based on service and status
     */
    private function generateNotes(array $service, string $status): ?string
    {
        $serviceNotes = [
            'Full Vehicle Wrap'     => [
                'Customer wants matte black finish',
                'Discuss design options with customer',
                'Vehicle prep required - minor scratches',
                'Custom graphics requested',
                'Premium vinyl material selected',
            ],
            'Paint Protection Film' => [
                'Focus on front bumper and hood',
                'Customer concerned about rock chips',
                'Full front end protection requested',
                'Clear film - maintain original color',
                'High-impact areas priority',
            ],
            'Window Tinting'        => [
                'Legal limit - 70% front, 35% rear',
                'Ceramic tint preferred',
                'All windows except windshield',
                'Customer wants heat rejection',
                'UV protection important to customer',
            ],
        ];

        $statusNotes = [
            'cancelled' => [
                'Customer rescheduled',
                'Weather concerns',
                'Vehicle not available',
                'Personal emergency',
            ],
            'no_show'   => [
                'Customer did not arrive',
                'No response to calls',
                'Vehicle in accident',
            ],
            'completed' => [
                'Job completed successfully',
                'Customer very satisfied',
                'Excellent results',
                'Customer referred friend',
            ],
        ];

        $notes = [];

        // Add service-specific note
        $serviceName = $service['name'] ?? 'General Service';
        if (isset($serviceNotes[$serviceName])) {
            $notes[] = $serviceNotes[$serviceName][array_rand($serviceNotes[$serviceName])];
        }

        // Add status-specific note
        if (isset($statusNotes[$status])) {
            $notes[] = $statusNotes[$status][array_rand($statusNotes[$status])];
        }

        return empty($notes) ? null : implode('. ', $notes);
    }

    /**
     * Generate metadata for appointment
     */
    private function generateMetadata(array $customerData, array $service, ?Token $token, string $period): array
    {
        $metadata = [
            'customer_source' => $period === 'past' ? 'referral' : 'social_media',
            'service_details' => [
                'category'           => $service['category'] ?? 'general',
                'duration_estimated' => $service['duration'] ?? 120,
                'price_quoted'       => $service['price'] ?? null,
            ],
            'booking_details' => [
                'method'         => $this->getRandomBookingMethod(),
                'channel'        => $token ? $token->platform->key ?? 'direct' : 'direct',
                'agent_assisted' => rand(0, 1) === 1,
            ],
        ];

        if ($token) {
            $metadata['platform_details'] = [
                'platform'             => $token->platform->key ?? 'unknown',
                'customer_platform_id' => $customerData['platform_customer_id'],
            ];
        }

        return $metadata;
    }

    /**
     * Get random booking method
     */
    private function getRandomBookingMethod(): string
    {
        $methods = ['chatbot', 'manual', 'api', 'phone', 'website'];
        $weights = [40, 25, 10, 15, 10]; // Chatbot is most common

        $rand       = rand(1, 100);
        $cumulative = 0;

        foreach ($methods as $index => $method) {
            $cumulative += $weights[$index];
            if ($rand <= $cumulative) {
                return $method;
            }
        }

        return 'chatbot';
    }

    /**
     * Ensure data is properly decoded from JSON
     */
    private function ensureArrayFromJson($data): array
    {
        if (is_string($data)) {
            try {
                $decoded = json_decode($data, true);
                return $decoded ?: [];
            } catch (\Exception $e) {
                return [];
            }
        }

        return is_array($data) ? $data : [];
    }
}