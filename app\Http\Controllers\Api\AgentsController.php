<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\AgentIdRequest;
use App\Http\Requests\AgentStatusUpdateRequest;
use App\Http\Requests\AgentSubscribeRequest;
use App\Http\Requests\AgentUpdateRequest;
use App\Http\Resources\AgentIndexResource;
use App\Http\Resources\AgentResource;
use App\Services\AgentService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

final class AgentsController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly AgentService $agentService
    ) {}

    /**
     * Get paginated list of agents
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $agents = $this->agentService->getAllAgents([
                'page'     => $request->get('page', 1),
                'per_page' => $request->get('per_page', 10),
            ]);

            return $this->successResponse(
                AgentIndexResource::collection($agents->items())
                    ->additional([
                        'pagination' => [
                            'current_page' => $agents->currentPage(),
                            'per_page'     => $agents->perPage(),
                            'total'        => $agents->total(),
                            'last_page'    => $agents->lastPage(),
                        ],
                    ])
            );
        } catch (\Exception $e) {
            Log::error('Error fetching agents', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('An error occurred while fetching agents', 500);
        }
    }

    /**
     * Get a specific agent by ID
     */
    public function show(AgentIdRequest $request): JsonResponse
    {
        try {
            $agent = $this->agentService->getAgentById((string) $request->getAgentId());

            if (!$agent) {
                return $this->errorResponse('Agent not found', 404);
            }

            return $this->successResponse(new AgentResource($agent));

        } catch (\Exception $e) {
            Log::error('Error fetching agent', [
                'agent_id' => $request->getAgentId(),
                'error'    => $e->getMessage(),
                'trace'    => $e->getTraceAsString(),
            ]);

            return $this->errorResponse('An error occurred while fetching the agent', 500);
        }
    }

    /**
     * Get agent metrics
     */
    public function metrics(AgentIdRequest $request): JsonResponse
    {
        try {
            $metrics = $this->agentService->getAgentMetrics((string) $request->getAgentId());

            if (!$metrics) {
                return $this->errorResponse('Agent not found', 404);
            }

            return $this->successResponse($metrics);

        } catch (\Exception $e) {
            Log::error('Error fetching agent metrics', [
                'agent_id' => $request->getAgentId(),
                'error'    => $e->getMessage(),
            ]);

            return $this->errorResponse('An error occurred while fetching agent metrics', 500);
        }
    }

    /**
     * Update agent status
     */
    public function updateStatus(AgentStatusUpdateRequest $request): JsonResponse
    {
        try {
            $agent = $this->agentService->updateAgentStatus(
                (string) $request->getAgentId(),
                $request->boolean('is_active')
            );

            if (!$agent) {
                return $this->errorResponse('Agent not found', 404);
            }

            return $this->successResponse(new AgentResource($agent));

        } catch (\Exception $e) {
            Log::error('Error updating agent status', [
                'agent_id' => $request->getAgentId(),
                'error'    => $e->getMessage(),
            ]);

            return $this->errorResponse('An error occurred while updating agent status', 500);
        }
    }

    /**
     * Subscribe user to an agent
     */
    public function subscribe(AgentSubscribeRequest $request): JsonResponse
    {
        $user = Auth::user();

        try {
            $userAgent = $this->agentService->subscribeUserToAgent(
                (string) $request->getAgentId(),
                $user->id,
                $request->only(['name', 'agent_gender', 'personality'])
            );

            if (!$userAgent) {
                return $this->errorResponse('Agent not found', 404);
            }

            return $this->successResponse($userAgent, 'Successfully subscribed to agent');

        } catch (\Exception $e) {
            Log::error('Error subscribing to agent', [
                'agent_id' => $request->getAgentId(),
                'user_id'  => $user->id,
                'error'    => $e->getMessage(),
            ]);

            return $this->errorResponse($e->getMessage(), 422);
        }
    }

    /**
     * Unsubscribe user from an agent
     */
    public function unsubscribe(AgentIdRequest $request): JsonResponse
    {
        $user = Auth::user();

        try {
            $result = $this->agentService->unsubscribeUserFromAgent(
                (string) $request->getAgentId(),
                $user->id
            );

            if (!$result) {
                return $this->errorResponse('Agent not found or user not subscribed', 404);
            }

            return $this->successResponse(null, 'Successfully unsubscribed from agent');

        } catch (\Exception $e) {
            Log::error('Error unsubscribing from agent', [
                'agent_id' => $request->getAgentId(),
                'user_id'  => $user->id,
                'error'    => $e->getMessage(),
            ]);

            return $this->errorResponse($e->getMessage(), 422);
        }
    }

    /**
     * Update user agent configuration
     */
    public function updateUserAgent(AgentUpdateRequest $request): JsonResponse
    {

        // dd($request->all());
        $user = Auth::user();

        // Debug: Log incoming request data
        $requestData = $request->except(['id']);
        Log::info('UserAgent update request received', [
            'user_id'        => $user->id,
            'agent_id'       => $request->getAgentId(),
            'raw_data'       => $request->all(),
            'except_id'      => $requestData,
            'request_method' => $request->method(),
            'content_type'   => $request->header('Content-Type'),
            'has_json_data'  => !empty($requestData),
        ]);

        // Check if no data was provided
        if (empty($requestData)) {
            Log::warning('No update data provided in request', [
                'user_id'          => $user->id,
                'agent_id'         => $request->getAgentId(),
                'all_request_data' => $request->all(),
                'request_headers'  => $request->headers->all(),
            ]);

            return $this->errorResponse('No data provided for update. Please check your request body and Content-Type header.', 400);
        }

        try {
            $userAgent = $this->agentService->updateUserAgent(
                $user->id,
                $request->getAgentId(),
                $request->except(['id'])
            );

            if (!$userAgent) {
                return $this->errorResponse('User agent not found or not accessible', 404);
            }

            // Get the updated agent with all relationships to return via AgentResource
            $agent = $this->agentService->getAgentById((string) $request->getAgentId());

            // Send response immediately
            $response = $this->successResponse(new AgentResource($agent), 'User agent updated successfully');

            // Handle knowledge file uploads - store files only, process later
            if ($request->hasFile('knowledge_files')) {
                try {
                    // Get files and ensure it's an array
                    $files = $request->file('knowledge_files');
                    if (!is_array($files)) {
                        $files = [$files];
                    }

                    // Only store files without processing embeddings
                    $uploadedFiles = $this->agentService->storeKnowledgeFilesOnly(
                        $userAgent,
                        $files,
                        $user->id
                    );

                    Log::info('Knowledge files stored successfully (marked as processing)', [
                        'agent_id'    => $request->getAgentId(),
                        'user_id'     => $user->id,
                        'files_count' => count($uploadedFiles),
                    ]);
                } catch (\Exception $e) {
                    Log::error('Knowledge file storage failed', [
                        'agent_id' => $request->getAgentId(),
                        'user_id'  => $user->id,
                        'error'    => $e->getMessage(),
                    ]);
                }
            }

            return $response;

        } catch (\Exception $e) {
            Log::error('Error updating user agent', [
                'agent_id' => $request->getAgentId(),
                'user_id'  => $user->id,
                'error'    => $e->getMessage(),
            ]);

            return $this->errorResponse($e->getMessage(), 422);
        }
    }

}
