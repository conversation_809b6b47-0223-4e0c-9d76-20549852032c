<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

final class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'message_id',
        'sender_id',
        'recipient_id',
        'message', // Changed from 'content' to 'message' to match DB schema
        'status',
        'sent_at',
        'timestamp',
        'reply',
        'meta_data',
        'direction',
        'platform',
    ];

    protected $casts = [
        'sent_at'   => 'datetime',
        'timestamp' => 'datetime',
        'meta_data' => 'array',
        'status'    => 'string',
        'direction' => 'string',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Scope to get pending messages
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get incoming messages
     */
    public function scopeIncoming($query)
    {
        return $query->where('direction', 'incoming');
    }

    /**
     * Scope to get outgoing messages
     */
    public function scopeOutgoing($query)
    {
        return $query->where('direction', 'outgoing');
    }

    /**
     * Scope to get messages for a specific platform
     */
    public function scopeForPlatform($query, string $platform)
    {
        return $query->where('platform', $platform);
    }
}
