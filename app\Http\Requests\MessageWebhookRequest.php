<?php

declare (strict_types = 1);

namespace App\Http\Requests;

class MessageWebhookRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'sender_id'    => 'required|string',
            'recipient_id' => 'required|string',
            'content'      => 'required|string',
            'timestamp'    => 'sometimes|integer',
            'platform'     => 'required|string|in:website,facebook,instagram',
            'message_id'   => 'sometimes|string',
            'meta_data'    => 'sometimes|array',
        ];
    }
}