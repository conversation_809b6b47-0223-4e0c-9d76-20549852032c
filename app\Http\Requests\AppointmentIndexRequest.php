<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class AppointmentIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('sanctum')->check();
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'date'      => $this->normalizeDate($this->input('date')),
            'date_from' => $this->normalizeDate($this->input('date_from')),
            'date_to'   => $this->normalizeDate($this->input('date_to')),
        ]);
    }

    /**
     * Normalize date format from ISO datetime to Y-m-d
     */
    private function normalizeDate(?string $date): ?string
    {
        if (!$date) {
            return null;
        }

        // If it's already in Y-m-d format, return as is
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return $date;
        }

        // If it's ISO datetime format, extract the date part
        if (preg_match('/^(\d{4}-\d{2}-\d{2})T/', $date, $matches)) {
            return $matches[1];
        }

        // Try to parse any other date format
        try {
            return \Carbon\Carbon::parse($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return $date; // Return original if parsing fails, let validation handle it
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status'         => 'sometimes|string|in:all,pending,confirmed,in_progress,completed,cancelled,no_show',
            'platform'       => 'sometimes|string|exists:platforms,key',
            'user_agent_id'  => 'sometimes|integer|exists:user_agents,id',
            'date'           => 'sometimes|date_format:Y-m-d',
            'date_from'      => 'sometimes|date_format:Y-m-d',
            'date_to'        => 'sometimes|date_format:Y-m-d|after_or_equal:date_from',
            'service'        => 'sometimes|string|max:255',
            'customer'       => 'sometimes|string|max:255',
            'booking_method' => 'sometimes|string|in:chatbot,manual,api,phone,website',
            'per_page'       => 'sometimes|integer|min:1|max:100',
            'search'         => 'sometimes|string|max:255',
            'sort_by'        => 'sometimes|string|in:appointment_date,created_at,updated_at,customer_name,service_name',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.in'              => 'The status must be one of: all, pending, confirmed, in_progress, completed, cancelled, no_show.',
            'platform.exists'        => 'The selected platform does not exist.',
            'user_agent_id.exists'   => 'The selected user agent does not exist.',
            'date_to.after_or_equal' => 'The end date must be after or equal to the start date.',
            'per_page.max'           => 'The per page value cannot exceed 100.',
            'booking_method.in'      => 'The booking method must be one of: chatbot, manual, api, phone, website.',
        ];
    }

    /**
     * Get the validated data with defaults
     */
    public function getFilters(): array
    {
        return $this->only([
            'status',
            'platform',
            'user_agent_id',
            'date',
            'date_from',
            'date_to',
            'service',
            'customer',
            'booking_method',
        ]);
    }

    /**
     * Get pagination parameters
     */
    public function getPaginationParams(): array
    {
        return [
            'per_page' => $this->input('per_page', 15),
            'search'   => $this->input('search'),
        ];
    }

    /**
     * Get sorting parameters
     */
    public function getSortParams(): array
    {
        return [
            'sort_by'        => $this->input('sort_by', 'appointment_date'),
            'sort_direction' => $this->input('sort_direction', 'desc'),
        ];
    }
}