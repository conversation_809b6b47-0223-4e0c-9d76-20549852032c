<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\UserAgent;

/**
 * Builds a unified, schema-driven runtime profile for an agent from the fixed JSON structure
 * seeded in AgentsSeeder. This normalizes access to configuration, prompts, capabilities,
 * business context, and company details so downstream chatflow is agent-agnostic.
 */
final class AgentProfileService
{
    /**
     * Build a normalized agent profile from a `UserAgent` model.
     *
     * Structure (stable keys):
     * - type_key: string (agent->key)
     * - name: string (userAgent->name)
     * - gender: string
     * - personality: string
     * - capabilities: array<string>
     * - config: array (merged: agent->default_configuration + userAgent->customize_configuration)
     * - prompts: array (merged: system defaults + agent->default_prompt_templates + userAgent->prompt_templates)
     * - company: array (userAgent->company_details)
     * - business: array (userAgent->agent_business_data)
     * - escalation: array (userAgent->escalation_rules)
     */
    public function buildProfile(UserAgent $userAgent): array
    {
        $agent = $userAgent->agent;

        $defaultConfig = $this->jsonToArray($agent?->default_configuration);
        $customConfig  = $this->jsonToArray($userAgent->customize_configuration);
        $mergedConfig  = array_merge($defaultConfig ?: [], $customConfig ?: []);

        $systemPrompts = $this->getDefaultPromptTemplates();
        $agentPrompts  = $this->jsonToArray($agent?->default_prompt_templates);
        $customPrompts = $this->jsonToArray($userAgent->prompt_templates);
        $mergedPrompts = array_merge($systemPrompts, $agentPrompts ?: [], $customPrompts ?: []);

        $capabilities = $this->jsonToArray($agent?->default_capabilities) ?: [];
        $company      = $this->jsonToArray($userAgent->company_details) ?: [];
        $business     = $this->jsonToArray($userAgent->agent_business_data) ?: [];
        $escalation   = $this->jsonToArray($userAgent->escalation_rules) ?: [];

        return [
            'type_key'     => (string) ($agent?->key ?? 'generic'),
            'name'         => (string) ($userAgent->name ?? 'AI Assistant'),
            'gender'       => (string) ($userAgent->agent_gender ?? 'neutral'),
            'personality'  => (string) ($userAgent->personality ?? 'professional'),
            'capabilities' => array_values($capabilities),
            'config'       => $mergedConfig,
            'prompts'      => $mergedPrompts,
            'company'      => $company,
            'business'     => $business,
            'escalation'   => $escalation,
        ];
    }

    /**
     * Helpers
     */
    private function jsonToArray(mixed $data): array
    {
        if (is_array($data)) {
            return $data;
        }
        if (is_string($data) && $data !== '') {
            $decoded = json_decode($data, true);
            return is_array($decoded) ? $decoded : [];
        }
        return [];
    }

    /**
     * Default prompt templates used as system-level fallbacks
     */
    private function getDefaultPromptTemplates(): array
    {
        return [
            'greeting'              => "Hello! I'm here to help you with your service needs. How can I assist you today?",
            'fallback'              => "I'm not sure I understood that. Could you please rephrase your question?",
            'error'                 => "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
            'escalation'            => 'I understand you need additional assistance. Let me connect you with a human agent who can better help you.',
            'appointment_booking'   => "I'd be happy to help you schedule an appointment. What service are you interested in?",
            'urgent_booking'        => 'I see this is urgent. Let me check our immediate availability for you.',
            'service_inquiry'       => 'Great choice! Let me provide you with details about that service.',
            'booking_confirmation'  => 'Let me confirm all the details before we finalize your appointment.',
            'reminder_info'         => "We'll send you reminders about your upcoming appointment.",
            'same_day_policy'       => 'Please note that same-day bookings require special arrangement.',
            'platform_instructions' => 'Keep responses concise for messaging platforms.',
            'conversation_style'    => 'Be professional, helpful, and conversational.',
            'customer_role'         => 'Customer',
            'assistant_role'        => 'Assistant',
            'system_prompt'         => 'You are a helpful assistant for our services.',
        ];
    }
}