<?php

namespace Database\Factories;

use App\Models\Agent;
use App\Models\User;
use App\Models\UserAgent;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserAgent>
 */
class UserAgentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id'                 => User::factory(),
            'agent_id'                => Agent::factory(),
            'name'                    => fake()->catchPhrase(),
            'status'                  => fake()->randomElement(['active', 'inactive', 'maintenance']),
            'personality'             => fake()->randomElement(['professional', 'friendly', 'casual', 'formal', 'helpful']),
            'agent_gender'            => fake()->randomElement(['male', 'female', 'neutral']),
            'customize_configuration' => [
                'greeting' => fake()->sentence(),
                'farewell' => fake()->sentence(),
            ],
            'prompt_templates'        => [
                'template_1' => 'This is template 1 with {placeholder}',
                'template_2' => 'This is template 2 with {placeholder}',
            ],
            'company_details'         => [
                'name'        => fake()->company(),
                'type'        => fake()->randomElement(['LLC', 'Corp', 'Partnership']),
                'description' => fake()->catchPhrase(),
            ],
            'agent_business_data'     => [
                'services'    => ['service1', 'service2'],
                'specialties' => ['specialty1'],
            ],
            'notification_settings'   => [
                'email_notifications' => fake()->boolean(),
                'sms_notifications'   => fake()->boolean(),
            ],
            'analytics_settings'      => [
                'track_conversations' => fake()->boolean(),
                'track_performance'   => fake()->boolean(),
            ],
            'performance_targets'     => [
                'response_time_target' => fake()->numberBetween(5, 60),
                'satisfaction_target'  => fake()->numberBetween(80, 95),
            ],
            'escalation_rules'        => [
                'escalate_after_attempts' => fake()->numberBetween(3, 5),
                'escalate_keywords'       => ['urgent', 'complaint'],
            ],
        ];
    }

    /**
     * Indicate that the user agent is active.
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the user agent is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Set a specific agent key.
     */
    public function ofKey(string $key): static
    {
        return $this->state(function (array $attributes) use ($key) {
            $agent = Agent::where('key', $key)->first();
            if (!$agent) {
                $agent = Agent::factory()->create(['key' => $key]);
            }
            return [
                'agent_id' => $agent->id,
            ];
        });
    }
}
