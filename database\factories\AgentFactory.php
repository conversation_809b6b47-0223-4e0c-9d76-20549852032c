<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Agent>
 */
class AgentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->catchPhrase();
        $key  = Str::slug($name) . '-' . fake()->unique()->lexify('????');

        return [
            'name'                     => $name,
            'key'                      => $key,
            'description'              => fake()->paragraph(2),
            'default_capabilities'     => [
                'capability_1' => true,
                'capability_2' => true,
                'capability_3' => false,
            ],
            'default_configuration'    => [
                'greeting' => fake()->sentence(),
                'farewell' => fake()->sentence(),
            ],
            'default_prompt_templates' => [
                'template_1' => 'This is template 1 with {placeholder}',
                'template_2' => 'This is template 2 with {placeholder}',
            ],
            'is_active'                => fake()->boolean(80),
        ];
    }

    /**
     * Indicate that the agent is active.
     */
    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the agent is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Set a specific agent key.
     */
    public function ofKey(string $key): static
    {
        return $this->state(fn(array $attributes) => [
            'key' => $key,
        ]);
    }
}