<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserAgent extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'agent_id',
        'name',
        'status',
        'personality',
        'agent_gender',
        'customize_configuration',
        'prompt_templates',
        'company_details',
        'agent_business_data',
        'notification_settings',
        'analytics_settings',
        'performance_targets',
        'escalation_rules',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'customize_configuration' => 'json',
        'prompt_templates'        => 'json',
        'company_details'         => 'json',
        'agent_business_data'     => 'json',
        'notification_settings'   => 'json',
        'analytics_settings'      => 'json',
        'performance_targets'     => 'json',
        'escalation_rules'        => 'json',
        'deleted_at'              => 'datetime',
    ];

    /**
     * Get the user that owns the user agent.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the agent type for this user agent.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the metrics for this user agent.
     */
    public function metrics(): HasMany
    {
        return $this->hasMany(AgentMetric::class);
    }

    /**
     * Get all knowledge files for this user agent.
     */
    public function knowledgeFiles(): HasMany
    {
        return $this->hasMany(KnowledgeFile::class, 'user_agent_id');
    }

    /**
     * Get all business embeddings for this user agent.
     */
    public function businessEmbeddings(): HasMany
    {
        return $this->hasMany(BusinessEmbedding::class, 'user_agent_id');
    }

    /**
     * Get all appointments for this user agent.
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class, 'user_agent_id');
    }

    /**
     * Scope a query to only include active user agents.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive user agents.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    // Removed legacy helpers referencing non-existent columns
}