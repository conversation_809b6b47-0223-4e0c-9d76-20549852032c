<?php

declare (strict_types = 1);

namespace Database\Seeders;

use App\Models\KnowledgeChunk;
use App\Models\KnowledgeFile;
use App\Models\User;
use App\Services\KnowledgeService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

final class KnowledgeSeeder extends Seeder
{
    private KnowledgeService $knowledgeService;

    public function __construct(KnowledgeService $knowledgeService)
    {
        $this->knowledgeService = $knowledgeService;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the admin user
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            $this->command->warn('Admin user not found. Please run UserSeeder first.');
            return;
        }

        // Get the first user agent for this user (if any exists)
        $userAgent = \App\Models\UserAgent::where('user_id', $user->id)->first();

        if (!$userAgent) {
            $this->command->warn('No user agent found for admin user. Please create one first.');
            return;
        }

        // Sample knowledge files data
        $knowledgeFiles = [
            [
                'name'    => 'car_wrap_faq.txt',
                'content' => "Frequently Asked Questions About Car Wraps\n\n" .
                "Q: How long does a car wrap last?\n" .
                "A: A high-quality car wrap typically lasts 5-7 years with proper care and maintenance.\n\n" .
                "Q: Can I wash my wrapped car?\n" .
                "A: Yes, you can wash your wrapped vehicle, but hand washing is recommended. Avoid high-pressure washers.\n\n" .
                "Q: How much does a full car wrap cost?\n" .
                "A: Full car wraps start at $2,500, with final pricing depending on vehicle size and design complexity.\n\n" .
                "Q: How long does the installation take?\n" .
                "A: A full vehicle wrap typically takes 3-5 days for complete installation.",
            ],
            [
                'name'    => 'paint_protection.txt',
                'content' => "Paint Protection Guide\n\n" .
                "Paint protection is crucial for maintaining your vehicle's appearance. Our services include:\n\n" .
                "1. Ceramic Coating\n" .
                "- Long-lasting protection\n" .
                "- Enhances gloss and depth\n" .
                "- Hydrophobic properties\n\n" .
                "2. Paint Protection Film (PPF)\n" .
                "- Self-healing properties\n" .
                "- Protects against rock chips\n" .
                "- Preserves paint condition\n\n" .
                "3. Regular Maintenance\n" .
                "- Gentle washing techniques\n" .
                "- Periodic inspections\n" .
                "- Touch-up services",
            ],
            [
                'name'    => 'vehicle_graphics.txt',
                'content' => "Commercial Vehicle Graphics Guide\n\n" .
                "Transform your business vehicles with professional graphics:\n\n" .
                "1. Types of Vehicle Graphics\n" .
                "- Full wraps\n" .
                "- Partial wraps\n" .
                "- Decals and lettering\n\n" .
                "2. Design Considerations\n" .
                "- Brand consistency\n" .
                "- Visibility and readability\n" .
                "- Contact information placement\n\n" .
                "3. Material Options\n" .
                "- Cast vinyl for curves\n" .
                "- Calendared vinyl for flat surfaces\n" .
                "- Specialty finishes",
            ],
        ];

        foreach ($knowledgeFiles as $fileData) {
            // Create temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'seed_');
            file_put_contents($tempFile, $fileData['content']);

            // Store file
            $storagePath = 'knowledge/' . $fileData['name'];
            Storage::disk('private')->put($storagePath, file_get_contents($tempFile));

            // Create knowledge file record
            $file = KnowledgeFile::create([
                'file_name'     => $fileData['name'],
                'original_name' => $fileData['name'],
                'uploaded_by'   => $user->id,
                'user_agent_id' => $userAgent?->id,
                'type'          => 'txt',
                'file_path'     => $storagePath,
                'file_size'     => strlen($fileData['content']),
                'mime_type'     => 'text/plain',
                'status'        => 'completed',
            ]);

            // Create chunks from content
            $chunks = $this->createChunks($fileData['content']);
            foreach ($chunks as $index => $chunk) {
                try {
                    // Generate real embedding for the chunk content
                    $embedding = $this->knowledgeService->generateEmbedding($chunk);

                    $this->command->info("Generated embedding for chunk {$index} of {$fileData['name']} (dimensions: " . count($embedding) . ")");

                    KnowledgeChunk::create([
                        'content'           => $chunk,
                        'knowledge_file_id' => $file->id,
                        'chunk_index'       => $index,
                        'metadata'          => [
                            'source' => $fileData['name'],
                            'type'   => 'text',
                            'length' => strlen($chunk),
                        ],
                        'embedding'         => $embedding,
                    ]);

                } catch (\Exception $e) {
                    $this->command->error("Failed to generate embedding for chunk {$index} of {$fileData['name']}: " . $e->getMessage());
                    Log::error('Seeder embedding generation failed', [
                        'file'        => $fileData['name'],
                        'chunk_index' => $index,
                        'error'       => $e->getMessage(),
                    ]);

                    // Create chunk with null embedding as fallback
                    KnowledgeChunk::create([
                        'content'           => $chunk,
                        'knowledge_file_id' => $file->id,
                        'chunk_index'       => $index,
                        'metadata'          => [
                            'source'          => $fileData['name'],
                            'type'            => 'text',
                            'length'          => strlen($chunk),
                            'embedding_error' => $e->getMessage(),
                        ],
                        'embedding'         => null, // Will be generated later by background job if needed
                    ]);
                }
            }

            // Clean up temp file
            unlink($tempFile);
        }
    }

    /**
     * Split text into chunks
     */
    private function createChunks(string $text, int $chunkSize = 500): array
    {
        $chunks    = [];
        $sentences = preg_split('/(?<=[.!?])\s+/', $text, -1, PREG_SPLIT_NO_EMPTY);

        $currentChunk = '';
        foreach ($sentences as $sentence) {
            if (strlen($currentChunk) + strlen($sentence) > $chunkSize && !empty($currentChunk)) {
                $chunks[]     = trim($currentChunk);
                $currentChunk = '';
            }
            $currentChunk .= ' ' . $sentence;
        }

        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        return $chunks;
    }

}