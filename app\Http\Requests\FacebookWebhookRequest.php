<?php

declare (strict_types = 1);

namespace App\Http\Requests;

class FacebookWebhookRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'entry'                   => 'required|array',
            'entry.*.changes'         => 'required|array',
            'entry.*.changes.*.field' => 'required|string',
            'entry.*.changes.*.value' => 'required',
        ];
    }
}
