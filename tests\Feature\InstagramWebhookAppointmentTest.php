<?php

declare (strict_types = 1);

namespace Tests\Feature;

use App\Models\Appointment;
use App\Models\Message;
use App\Models\User;
use App\Models\UserAgent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class InstagramWebhookAppointmentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private UserAgent $userAgent;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user and user agent
        $this->user = User::factory()->create();

        $this->userAgent = UserAgent::factory()->create([
            'user_id'             => $this->user->id,
            'status'              => 'active',
            'agent_business_data' => json_encode([
                'business_services' => [
                    'wrap' => [
                        'name'     => 'Full Vehicle Wrap',
                        'price'    => 2500,
                        'duration' => 480,
                        'category' => 'vehicle wrap',
                    ],
                ],
                'business_hours'    => [
                    'monday'    => ['08:00', '18:00'],
                    'tuesday'   => ['08:00', '18:00'],
                    'wednesday' => ['08:00', '18:00'],
                    'thursday'  => ['08:00', '18:00'],
                    'friday'    => ['08:00', '18:00'],
                    'saturday'  => ['09:00', '17:00'],
                ],
                'scheduling_rules'  => [
                    'max_bookings_per_day' => 10,
                    'allow_double_booking' => false,
                    'flexible_scheduling'  => [
                        'max_quantity_per_booking' => 5,
                    ],
                ],
            ]),
        ]);
    }

    public function test_webhook_processes_appointment_booking_message()
    {
        // Simulate Instagram webhook payload for appointment booking
        $webhookPayload = [
            'object' => 'instagram',
            'entry'  => [
                [
                    'id'        => '123456789',
                    'time'      => time(),
                    'messaging' => [
                        [
                            'sender'    => ['id' => 'test_sender_123'],
                            'recipient' => ['id' => 'test_recipient_456'],
                            'timestamp' => time(),
                            'message'   => [
                                'mid'  => 'test_message_' . time(),
                                'text' => 'I want to book a full vehicle wrap for tomorrow at 10 AM',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        // Mock webhook verification
        config(['services.instagram.webhook_verify_token' => 'test_token']);
        config(['services.instagram.app_secret' => null]); // Disable signature verification for test

        // Send webhook request
        $response = $this->postJson('/api/v1/webhooks/instagram', $webhookPayload);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);

        // Verify message was created
        $this->assertDatabaseHas('messages', [
            'sender_id' => 'test_sender_123',
            'platform'  => 'instagram',
        ]);

        // Wait a moment for async processing
        sleep(1);

        // Verify appointment was created
        $message = Message::where('sender_id', 'test_sender_123')->first();
        $this->assertNotNull($message);

        // Check if appointment was created (might be created after AI processing)
        $appointment = Appointment::where('customer_id', 'test_sender_123')->first();

        if ($appointment) {
            $this->assertEquals('Full Vehicle Wrap', $appointment->service_name);
            $this->assertEquals('pending', $appointment->status);
            $this->assertEquals($this->userAgent->id, $appointment->user_agent_id);
        } else {
            // Log for debugging if appointment wasn't created
            Log::info('Appointment not created during test', [
                'message_id'      => $message->id,
                'message_content' => $message->message,
                'user_agent_id'   => $this->userAgent->id,
            ]);
        }
    }

    public function test_webhook_processes_confirmation_message()
    {
        // First, create a message about service inquiry
        $firstMessage = Message::create([
            'sender_id'    => 'test_sender_123',
            'recipient_id' => 'test_recipient_456',
            'message'      => 'I want a full vehicle wrap for tomorrow at 10 AM',
            'platform'     => 'instagram',
            'status'       => 'replied',
            'reply'        => 'Perfect! I can schedule you for a Full Vehicle Wrap for tomorrow at 10:00 AM. The price starts at $2500. Does that sound correct?',
            'sent_at'      => now(),
            'direction'    => 'incoming',
        ]);

        // Now simulate confirmation webhook
        $webhookPayload = [
            'object' => 'instagram',
            'entry'  => [
                [
                    'id'        => '123456789',
                    'time'      => time(),
                    'messaging' => [
                        [
                            'sender'    => ['id' => 'test_sender_123'],
                            'recipient' => ['id' => 'test_recipient_456'],
                            'timestamp' => time(),
                            'message'   => [
                                'mid'  => 'test_message_confirm_' . time(),
                                'text' => 'Yes, that sounds perfect!',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        // Send confirmation webhook
        $response = $this->postJson('/api/v1/webhooks/instagram', $webhookPayload);

        $response->assertStatus(200)
            ->assertJson(['status' => 'success']);

        // Verify confirmation message was created
        $confirmationMessage = Message::where('sender_id', 'test_sender_123')
            ->where('message', 'Yes, that sounds perfect!')
            ->first();

        $this->assertNotNull($confirmationMessage);

        // Check if appointment was created after confirmation
        $appointment = Appointment::where('customer_id', 'test_sender_123')->first();

        if ($appointment) {
            $this->assertEquals('Full Vehicle Wrap', $appointment->service_name);
            $this->assertEquals('pending', $appointment->status);
            $this->assertNotNull($appointment->appointment_date);
            $this->assertNotNull($appointment->appointment_time);
        }
    }
}