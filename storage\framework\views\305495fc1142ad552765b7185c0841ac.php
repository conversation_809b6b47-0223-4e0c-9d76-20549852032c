<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Instagram Authentication - Kortana AI</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body class="bg-gradient-to-br from-purple-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <!-- Header -->
            <div class="text-center mb-6">
                <div
                    class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fab fa-instagram text-2xl text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">Instagram Authentication</h1>
                <p class="text-gray-600 mt-2">Connecting your Instagram account to Kortana AI</p>
            </div>

            <!-- Status Container -->
            <div id="status-container" class="space-y-4">
                <!-- Loading State -->
                <div id="loading-state" class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
                    <p class="text-gray-600">Processing your Instagram authentication...</p>
                </div>

                <!-- Success State -->
                <div id="success-state" class="hidden">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                            <div>
                                <h3 class="text-green-800 font-semibold">Successfully Connected!</h3>
                                <p class="text-green-700 text-sm">Your Instagram account has been connected to Kortana
                                    AI.</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-sm font-medium text-gray-700">Instagram Username:</p>
                            <p class="text-gray-900" id="page-name">Loading...</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-sm font-medium text-gray-700">Token ID:</p>
                            <p class="text-gray-900 font-mono text-sm" id="page-id">Loading...</p>
                        </div>

                        <div class="bg-gray-50 rounded-lg p-3">
                            <p class="text-sm font-medium text-gray-700">Account Type:</p>
                            <p class="text-gray-900 font-mono text-sm" id="ig-account-id">Loading...</p>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <button onclick="closeWindow()"
                            class="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-6 rounded-lg transition-colors">
                            Close Window
                        </button>
                    </div>
                </div>

                <!-- Error State -->
                <div id="error-state" class="hidden">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i>
                            <div>
                                <h3 class="text-red-800 font-semibold">Authentication Failed</h3>
                                <p class="text-red-700 text-sm" id="error-message">An error occurred during
                                    authentication.</p>
                            </div>
                        </div>
                    </div>

                    <div class="text-center space-y-3">
                        <button onclick="retryAuth()"
                            class="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-6 rounded-lg transition-colors">
                            Try Again
                        </button>
                        <br>
                        <button onclick="closeWindow()" class="text-gray-500 hover:text-gray-700 text-sm">
                            Close Window
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Panel (hidden by default) -->
        <div id="debug-panel" class="max-w-md mx-auto mt-4 bg-gray-800 rounded-lg shadow-lg p-4 text-white hidden">
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-semibold">Debug Information</h3>
                <button onclick="toggleDebug()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <pre id="debug-content" class="text-xs overflow-auto max-h-60 bg-gray-900 p-2 rounded"></pre>
        </div>
    </div>

    <script>
        // Debug toggle (press D key to show/hide)
        document.addEventListener('keydown', function(e) {
            if (e.key.toLowerCase() === 'd' && e.ctrlKey) {
                toggleDebug();
            }
        });

        function toggleDebug() {
            const debugPanel = document.getElementById('debug-panel');
            debugPanel.classList.toggle('hidden');
        }

        function updateDebug(data) {
            document.getElementById('debug-content').textContent = JSON.stringify(data, null, 2);
        }

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');

        // Process authentication
        if (error) {
            showError(errorDescription || error);
        } else if (code) {
            processAuth(code);
        } else {
            showError('No authorization code received');
        }

        async function processAuth(code) {
            try {
                console.log('Processing authentication with code:', code.substring(0, 10) + '...');

                const response = await fetch('/auth/instagram/callback?' + new URLSearchParams({
                    code: code,
                    state: urlParams.get('state')
                }), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    }
                });

                const data = await response.json();
                console.log('API response:', data);
                updateDebug(data);

                if (data.status === 'success') {
                    console.log('Authentication successful, showing success state');
                    showSuccess(data.data);
                } else {
                    console.log('Authentication failed:', data.message);
                    showError(data.message || 'Authentication failed');
                }
            } catch (error) {
                console.error('Auth error:', error);
                updateDebug({
                    error: error.toString()
                });
                showError('Network error occurred during authentication');
            }
        }

        function showSuccess(data) {
            document.getElementById('loading-state').classList.add('hidden');
            document.getElementById('success-state').classList.remove('hidden');

            // Update to match controller response fields
            if (data.username) {
                document.getElementById('page-name').textContent = data.username;
            } else {
                document.getElementById('page-name').textContent = 'Not available';
            }

            if (data.token_id) {
                document.getElementById('page-id').textContent = data.token_id;
            } else {
                document.getElementById('page-id').textContent = 'Not available';
            }

            if (data.account_type) {
                document.getElementById('ig-account-id').textContent = data.account_type;
            } else {
                document.getElementById('ig-account-id').textContent = 'Not available';
            }

            // Auto-close after 10 seconds
            setTimeout(() => {
                closeWindow();
            }, 10000);
        }

        function showError(message) {
            document.getElementById('loading-state').classList.add('hidden');
            document.getElementById('error-state').classList.remove('hidden');
            document.getElementById('error-message').textContent = message;
        }

        function retryAuth() {
            window.location.href = '/auth/instagram/';
        }

        function closeWindow() {
            if (window.opener) {
                window.opener.postMessage({
                    type: 'instagram_auth_complete',
                    success: !document.getElementById('success-state').classList.contains('hidden')
                }, '*');
            }
            window.close();
        }

        // Handle case where window can't be closed
        window.addEventListener('beforeunload', function() {
            if (window.opener) {
                window.opener.postMessage({
                    type: 'instagram_auth_complete',
                    success: !document.getElementById('success-state').classList.contains('hidden')
                }, '*');
            }
        });
    </script>
</body>

</html>
<?php /**PATH C:\laragon\www\kortana-ai\resources\views/instagram-auth.blade.php ENDPATH**/ ?>