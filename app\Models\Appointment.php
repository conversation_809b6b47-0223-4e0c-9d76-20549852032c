<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_name',
        'customer_id',
        'customer_phone',
        'customer_email',
        'user_id',
        'user_agent_id',
        'token_id',
        'appointment_date',
        'appointment_time',
        'duration_minutes',
        'service_name',
        'service_key',
        'service_price',
        'service_category',
        'status',
        'notes',
        'metadata',
        'booked_at',
        'confirmed_at',
        'booking_method',
    ];

    protected $casts = [
        'appointment_date' => 'date',
        'appointment_time' => 'datetime:H:i',
        'service_price'    => 'decimal:2',
        'duration_minutes' => 'integer',
        'metadata'         => 'array',
        'booked_at'        => 'datetime',
        'confirmed_at'     => 'datetime',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Get the user that owns this appointment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user agent that owns this appointment
     */
    public function userAgent(): BelongsTo
    {
        return $this->belongsTo(UserAgent::class);
    }

    /**
     * Get the token associated with this appointment
     */
    public function token(): BelongsTo
    {
        return $this->belongsTo(Token::class);
    }

    /**
     * Get the full appointment datetime
     */
    public function getAppointmentDateTimeAttribute(): ?\Carbon\Carbon
    {
        if (!$this->appointment_date || !$this->appointment_time) {
            return null;
        }

        return \Carbon\Carbon::createFromFormat(
            'Y-m-d H:i:s',
            $this->appointment_date->format('Y-m-d') . ' ' . $this->appointment_time->format('H:i:s')
        );
    }

    /**
     * Check if appointment is confirmed
     */
    public function isConfirmed() : bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if appointment is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Confirm the appointment
     */
    public function confirm(): bool
    {
        $this->status       = 'confirmed';
        $this->confirmed_at = now();
        return $this->save();
    }

    /**
     * Cancel the appointment
     */
    public function cancel(): bool
    {
        $this->status = 'cancelled';
        return $this->save();
    }

    /**
     * Get platform information through token relationship
     */
    public function getPlatformAttribute(): ?string
    {
        return $this->token?->platform?->key;
    }

    /**
     * Get platform instance through token relationship
     */
    public function getPlatformInstanceAttribute(): ?object
    {
        return $this->token?->tokenable;
    }
}