<?php

declare (strict_types = 1);

namespace App\Repositories\UserAgent;

use App\Models\UserAgent;
use Illuminate\Database\Eloquent\Collection;

final class UserAgentRepository implements UserAgentRepositoryInterface
{
    public function __construct(
        private readonly UserAgent $model
    ) {}

    /**
     * Find user agent by user ID and agent ID
     */
    public function findByUserAndAgent(int $userId, int $agentId): ?UserAgent
    {
        // dd($userId,  $agentId);
        return $this->model->where('user_id', $userId)
            ->where('agent_id', $agentId)
            ->with('agent')
            ->first();
    }

    /**
     * Create user agent subscription
     */
    public function create(array $data): UserAgent
    {
        $userAgent = $this->model->create($data);
        return $userAgent->load('agent');
    }

    public function findById(int $id): ?UserAgent
    {
        return $this->model->find($id);
    }

    /**
     * Create a user agent from an Agent template
     */
    public function createFromAgent(\App\Models\Agent $agent, \App\Models\User $user, array $attributes = []): UserAgent
    {
        $payload = [
            'user_id'                 => $user->id,
            'agent_id'                => $agent->id,
            'name'                    => $attributes['name'] ?? $agent->name,
            'status'                  => $attributes['status'] ?? 'inactive',
            'personality'             => $attributes['personality'] ?? null,
            'agent_gender'            => $attributes['agent_gender'] ?? 'neutral',
            'customize_configuration' => $attributes['customize_configuration'] ?? ($agent->default_configuration ?: []),
            'prompt_templates'        => $attributes['prompt_templates'] ?? ($agent->default_prompt_templates ?: []),
            'company_details'         => $attributes['company_details'] ?? null,
            'agent_business_data'     => $attributes['agent_business_data'] ?? null,
            'notification_settings'   => $attributes['notification_settings'] ?? null,
            'analytics_settings'      => $attributes['analytics_settings'] ?? null,
            'performance_targets'     => $attributes['performance_targets'] ?? null,
            'escalation_rules'        => $attributes['escalation_rules'] ?? null,
        ];

        /** @var UserAgent $ua */
        $ua = $this->model->create($payload);
        return $ua->fresh();
    }

    /**
     * Update user agent status
     */
    public function updateStatus(UserAgent $userAgent, string $status): UserAgent
    {
        $userAgent->update(['status' => $status]);
        return $userAgent->load('agent');
    }

    /**
     * Update user agent with data
     */
    public function update(UserAgent $userAgent, array $data): UserAgent
    {
        $userAgent->update($data);
        return $userAgent->load('agent');
    }

    /**
     * Check if user is subscribed to agent
     */
    public function isUserSubscribed(int $userId, int $agentId): bool
    {
        return $this->model->where('user_id', $userId)
            ->where('agent_id', $agentId)
            ->exists();
    }

    /**
     * Get user's subscribed agents
     */
    public function getUserSubscriptions(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
            ->with('agent')
            ->get();
    }

    /**
     * Get active user agents for a user
     */
    public function getActiveUserAgents(int $userId): Collection
    {
        return $this->model->where('user_id', $userId)
            ->where('status', 'active')
            ->with('agent')
            ->get();
    }

}
