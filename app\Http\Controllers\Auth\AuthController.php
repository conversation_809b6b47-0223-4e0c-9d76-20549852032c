<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\OtpVerificationRequest;
use App\Http\Requests\Auth\ProfileUpdateRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ResendOtpRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Resources\AuthResource;
use App\Services\Auth\AuthService;
use Illuminate\Http\Request;

final class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(RegisterRequest $request)
    {
        try {
            $result = $this->authService->register($request->validated());

            // Add requires_verification to the user object
            $user                        = $result['user'];
            $user->requires_verification = true;

            // Combine user data and state_code into single data object
            $userData               = (new AuthResource($user))->resolve();
            $userData['state_code'] = $result['state_code'];

            return $this->createdResponse(
                $userData,
                'User registered successfully. Please check your email for verification OTP.'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function login(LoginRequest $request)
    {
        try {
            $result = $this->authService->login(
                $request->email,
                $request->password,
                ['remember' => $request->remember]
            );

            // Check if login returned an error
            if (isset($result['error']) && $result['error']) {
                return $this->errorResponse(
                    $result['message'],
                    $result['code'],
                    $result['errors']
                );
            }

            // Check if email verification is required
            if (isset($result['requires_verification']) && $result['requires_verification']) {

                $user                        = $result['user'];
                $user->requires_verification = true;

                // Combine user data and state_code into single data object
                $userData               = (new AuthResource($user))->resolve();
                $userData['state_code'] = $result['state_code'];

                return $this->successResponse(
                    $userData,
                    'Email not verified. A verification OTP has been sent to your email.'
                );
            }

            // Successful login - combine user data and token into single data object
            $userData          = (new AuthResource($result['user']))->resolve();
            $userData['token'] = $result['token'];

            return $this->successResponse(
                $userData,
                'Login successful'
            );

        } catch (\Exception $e) {
            $code = $e->getCode();

            if ($code === 423) {
                return $this->errorResponse($e->getMessage(), 423);
            }

            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function otpVerify(OtpVerificationRequest $request)
    {
        try {
            $result = $this->authService->otpVerify(
                $request->email,
                $request->otp
            );

            // Check if verification returned an error
            if (isset($result['error']) && $result['error']) {
                return $this->errorResponse(
                    $result['message'],
                    $result['code'],
                    $result['errors']
                );
            }

            // Combine user data and token into single data object
            $userData          = (new AuthResource($result['user']))->resolve();
            $userData['token'] = $result['token'];
            $userData['type']  = $result['type'];

            // Set appropriate message based on verification type
            $message = $result['type'] === 'email_verification'
            ? 'Email verified successfully'
            : 'Password reset OTP verified successfully. You can now reset your password.';

            return $this->successResponse(
                $userData,
                $message
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 400);
        }
    }

    public function logout(Request $request)
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return $this->successResponse(
                null,
                'Successfully logged out'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function user(Request $request)
    {
        try {
            $userData = (new AuthResource($request->user()))->resolve();

            return $this->successResponse(
                $userData,
                'User details retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function updateProfile(ProfileUpdateRequest $request)
    {
        try {
            $user = $this->authService->updateProfile(
                $request->user()->id,
                $request->validated()
            );

            $userData = (new AuthResource($user))->resolve();

            return $this->successResponse(
                $userData,
                'Profile updated successfully'
            );

        } catch (\Exception $e) {
            if ($e->getMessage() === 'User not found') {
                return $this->notFoundResponse('User not found');
            }
            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            $result = $this->authService->forgotPassword($request->email);

            // Check if forgot password returned an error
            if (isset($result['error']) && $result['error']) {
                return $this->errorResponse(
                    $result['message'],
                    $result['code'],
                    $result['errors']
                );
            }

            return $this->successResponse(
                null,
                'If an account exists with this email, you will receive password reset instructions shortly'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        try {
            $result = $this->authService->resetPassword(
                $request->user()->id,
                $request->password
            );

            // Check if reset password returned an error
            if (isset($result['error']) && $result['error']) {
                return $this->errorResponse(
                    $result['message'],
                    $result['code'],
                    $result['errors']
                );
            }

            return $this->successResponse(
                null,
                'Password reset successfully'
            );

        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }

    public function resendOtp(ResendOtpRequest $request)
    {
        try {
            $result = $this->authService->resendOtp($request->email, $request->state_code);

            return $this->successResponse(
                $result,
                'OTP sent successfully to your email'
            );

        } catch (\Exception $e) {
            if ($e->getMessage() === 'User not found') {
                return $this->notFoundResponse('User not found');
            } elseif ($e->getMessage() === 'Email already verified') {
                return $this->errorResponse('Email already verified', 400);
            } elseif ($e->getMessage() === 'Invalid or expired state code') {
                return $this->errorResponse('Invalid or expired state code', 400);
            }
            return $this->serverErrorResponse($e->getMessage());
        }
    }
}