<?php

declare (strict_types = 1);

namespace App\Repositories\Appointment;

use App\Models\Appointment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

final class AppointmentRepository implements AppointmentRepositoryInterface
{
    /**
     * Universal query method for appointments with filters and options
     */
    public function queryAppointments(array $options = []): LengthAwarePaginator | Collection | array | int
    {
        $query = $this->buildUniversalQuery($options);

        return match ($options['type'] ?? 'paginated') {
            'count' => $query->count(),
            default => $query->paginate($options['per_page'] ?? 15),
        };
    }

    /**
     * Find appointment by ID
     */
    public function findAppointment(int $id, int $userId = null): ?Appointment
    {
        $query = Appointment::with(['user', 'userAgent', 'token.platform'])
            ->where('id', $id);

        return $query->when($userId, fn($q) => $q->where('user_id', $userId))->first();
    }

    /**
     * Create a new appointment
     */
    public function createAppointment(array $data): Appointment
    {
        return Appointment::create($data);
    }

    /**
     * Update an appointment
     */
    public function updateAppointment(int $id, array $data): bool
    {
        return Appointment::where('id', $id)->update($data) > 0;
    }

    /**
     * Update appointment status with optional notes
     */
    public function updateAppointmentStatus(int $id, string $status, int $userId = null, ?string $notes = null): bool
    {
        $query = Appointment::where('id', $id);

        if ($userId) {
            $query->where('user_id', $userId);
        }

        $updateData = ['status' => $status];

        // Add timestamp for status changes
        if ($status === 'confirmed') {
            $updateData['confirmed_at'] = now();
        }

        // Add notes if provided
        if ($notes !== null) {
            $updateData['notes'] = $notes;
        }

        return $query->update($updateData) > 0;
    }

    /**
     * Delete an appointment
     */
    public function deleteAppointment(int $id): bool
    {
        return Appointment::where('id', $id)->delete() > 0;
    }

    /**
     * Check if time slot is available
     */
    public function isTimeSlotAvailable(string $date, string $time, int $userAgentId, int $excludeId = null): bool
    {
        $query = Appointment::where('appointment_date', $date)
            ->where('appointment_time', $time)
            ->where('user_agent_id', $userAgentId)
            ->whereNotIn('status', ['cancelled', 'no_show']);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Get available statuses
     */
    public function getAvailableStatuses(): array
    {
        return ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'];
    }

    /**
     * Count bookings overlapping a given time window for a user agent on a date
     */
    public function countOverlappingBookings(int $userAgentId, string $date, string $startTime, string $endTime): int
    {
        // Fetch same-day active appointments and compute overlap in PHP to be DB-agnostic
        $appointments = Appointment::where('user_agent_id', $userAgentId)
            ->whereDate('appointment_date', $date)
            ->whereNotIn('status', ['cancelled', 'no_show'])
            ->get(['appointment_time', 'duration_minutes']);

        $newStart = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $date . ' ' . $startTime);
        $newEnd   = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $date . ' ' . $endTime);

        $count = 0;
        foreach ($appointments as $apt) {
            try {
                $existingStart = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $date . ' ' . (string) $apt->appointment_time);
                $existingEnd   = $existingStart->copy()->addMinutes((int) $apt->duration_minutes);

                if ($existingStart < $newEnd && $existingEnd > $newStart) {
                    $count++;
                }
            } catch (\Throwable $e) {
                // skip malformed times
            }
        }

        return $count;
    }

    /**
     * Build universal query with filters using when() for conditional logic
     */
    private function buildUniversalQuery(array $options = []): Builder
    {
        // return Appointment::query()->latest('id');
        return Appointment::query()
            ->with($this->getRelations($options))
            ->when($options['user_id'] ?? null, fn($q, $userId) => $q->where('user_id', $userId))
            ->when($options['user_agent_id'] ?? null, fn($q, $userAgentId) => $q->where('user_agent_id', $userAgentId))
            ->when($options['status'] ?? null, fn($q, $status) =>
                $status !== 'all' ? $q->where('status', $status) : $q
            )
            ->when($options['date'] ?? null, fn($q, $date) => $q->where('appointment_date', $date))
        // Simplified: no date range/today/upcoming filters
            ->when($options['search'] ?? null, fn($q, $search) =>
                $q->where(function (Builder $subQ) use ($search) {
                    $subQ->where('customer_name', 'like', "%{$search}%")
                        ->orWhere('platform_customer_id', 'like', "%{$search}%")
                        ->orWhere('platform_username', 'like', "%{$search}%")
                        ->orWhere('service_name', 'like', "%{$search}%")
                        ->orWhere('notes', 'like', "%{$search}%");
                })
            )
            ->orderBy('appointment_date', 'desc')
            ->orderBy('appointment_time', 'desc');
    }

    /**
     * Get relations to load based on options
     */
    private function getRelations(array $options): array
    {
        $relations = ['userAgent', 'token.platform'];

        // Only include user relation if not filtering by specific user
        if (!isset($options['user_id'])) {
            $relations[] = 'user';
        }

        return $relations;
    }

    /**
     * Build statistics query
     */
    private function buildStatsQuery(array $options): array
    {
        $query = Appointment::query()
            ->when($options['user_id'] ?? null, fn($q, $userId) => $q->where('user_id', $userId));

        $totalAppointments = $query->count();

        $statusCounts = $query->select('status', \Illuminate\Support\Facades\DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $todayCount     = $query->whereDate('appointment_date', today())->count();
        $upcomingCount  = $query->where('appointment_date', '>', today())->count();
        $thisMonthCount = $query->whereMonth('appointment_date', now()->month)
            ->whereYear('appointment_date', now()->year)
            ->count();

        return [
            'total'         => $totalAppointments,
            'status_counts' => $statusCounts,
            'today'         => $todayCount,
            'upcoming'      => $upcomingCount,
            'this_month'    => $thisMonthCount,
        ];
    }

    /**
     * Build calendar query
     */
    private function buildCalendarQuery(array $options): array
    {
        $month = $options['month'] ?? now()->month;
        $year  = $options['year'] ?? now()->year;

        $date      = \Carbon\Carbon::createFromFormat('Y-m', $year . '-' . $month);
        $startDate = $date->startOfMonth()->toDateString();
        $endDate   = $date->copy()->endOfMonth()->toDateString();

        $appointments = $this->buildUniversalQuery(array_merge($options, [
            'date_from' => $startDate,
            'date_to'   => $endDate,
            'type'      => 'collection',
        ]))->get();

        // Group appointments by date for calendar display
        $calendarData = [];
        foreach ($appointments as $appointment) {
            $dateKey = $appointment->appointment_date->format('Y-m-d');
            if (!isset($calendarData[$dateKey])) {
                $calendarData[$dateKey] = [];
            }
            $calendarData[$dateKey][] = $appointment;
        }

        return $calendarData;
    }
}
