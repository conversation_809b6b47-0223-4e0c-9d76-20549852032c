<?php

declare (strict_types = 1);

namespace Database\Factories;

use App\Models\Platform;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Platform>
 */
final class PlatformFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Platform::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name'        => $this->faker->company() . ' Platform',
            'key'         => $this->faker->unique()->slug(),
            'description' => $this->faker->sentence(),
            'category_id' => \App\Models\PlatformCategory::factory(),
            'enabled'     => $this->faker->boolean(70),
        ];
    }

    /**
     * Indicate that the platform is enabled.
     */
    public function enabled(): static
    {
        return $this->state(fn(array $attributes) => [
            'enabled' => true,
        ]);
    }

    /**
     * Indicate that the platform is connected.
     */
    public function connected(): static
    {
        return $this->state(fn(array $attributes) => [
            'enabled' => true,
        ]);
    }

    /**
     * Indicate that the platform is for social media.
     */
    public function socialMedia(): static
    {
        return $this->state(fn(array $attributes) => [
            'category_id' => \App\Models\PlatformCategory::firstOrCreate(['name' => 'social_media'])->id,
        ]);
    }

    /**
     * Indicate that the platform is for communication.
     */
    public function communication(): static
    {
        return $this->state(fn(array $attributes) => [
            'category_id' => \App\Models\PlatformCategory::firstOrCreate(['name' => 'communication'])->id,
        ]);
    }

    /**
     * Indicate that the platform is for reviews.
     */
    public function reviews(): static
    {
        return $this->state(fn(array $attributes) => [
            'category_id' => \App\Models\PlatformCategory::firstOrCreate(['name' => 'reviews'])->id,
        ]);
    }

    /**
     * Indicate that the platform is for other platforms.
     */
    public function others(): static
    {
        return $this->state(fn(array $attributes) => [
            'category_id' => \App\Models\PlatformCategory::firstOrCreate(['name' => 'others'])->id,
        ]);
    }
}
