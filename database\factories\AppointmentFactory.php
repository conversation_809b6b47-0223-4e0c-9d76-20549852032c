<?php

declare (strict_types = 1);

namespace Database\Factories;

use App\Models\Appointment;
use App\Models\Token;
use App\Models\User;
use App\Models\UserAgent;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Appointment>
 */
final class AppointmentFactory extends Factory
{
    protected $model = Appointment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $appointmentDate = $this->faker->dateTimeBetween('now', '+30 days');
        $appointmentTime = $this->faker->time('H:i');

        return [
            'customer_name'    => $this->faker->name(),
            'customer_id'      => 'customer_' . $this->faker->unique()->numberBetween(1000, 9999),
            'customer_phone'   => $this->faker->phoneNumber(),
            'customer_email'   => $this->faker->email(),
            'user_id'          => User::factory(),
            'user_agent_id'    => UserAgent::factory(),
            'token_id'         => Token::factory(),
            'appointment_date' => $appointmentDate->format('Y-m-d'),
            'appointment_time' => $appointmentTime,
            'duration_minutes' => $this->faker->randomElement([60, 90, 120, 180]),
            'service_name'     => $this->faker->randomElement([
                'Full Vehicle Wrap',
                'Partial Vehicle Wrap',
                'Paint Protection Film',
                'Window Tinting',
                'Design Consultation',
            ]),
            'service_key'      => $this->faker->randomElement([
                'full_vehicle_wrap',
                'partial_vehicle_wrap',
                'paint_protection_film',
                'window_tinting',
                'design_consultation',
            ]),
            'service_price'    => $this->faker->randomFloat(2, 100, 5000),
            'service_category' => $this->faker->randomElement([
                'vehicle_wrapping',
                'protection',
                'tinting',
                'consultation',
            ]),
            'status'           => $this->faker->randomElement([
                'pending',
                'confirmed',
                'in_progress',
                'completed',
                'cancelled',
                'no_show',
            ]),
            'notes'            => $this->faker->optional()->sentence(),
            'metadata'         => [
                'platform'       => 'instagram',
                'booking_source' => 'chatbot',
                'priority'       => $this->faker->randomElement(['low', 'normal', 'high']),
            ],
            'booking_method'   => $this->faker->randomElement(['chatbot', 'manual', 'api']),
            'booked_at'        => \Carbon\Carbon::parse($appointmentDate)->subDays($this->faker->numberBetween(1, 7)),
            'confirmed_at'     => $this->faker->optional(0.6)->dateTime(),
        ];
    }

    /**
     * Indicate that the appointment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn(array $attributes) => [
            'status'       => 'pending',
            'confirmed_at' => null,
        ]);
    }

    /**
     * Indicate that the appointment is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn(array $attributes) => [
            'status'       => 'confirmed',
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Indicate that the appointment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn(array $attributes) => [
            'status'       => 'completed',
            'confirmed_at' => now()->subDays(1),
        ]);
    }

    /**
     * Indicate that the appointment is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'cancelled',
            'notes'  => 'Appointment cancelled',
        ]);
    }

    /**
     * Create appointment for today.
     */
    public function today(): static
    {
        return $this->state(fn(array $attributes) => [
            'appointment_date' => now()->format('Y-m-d'),
            'appointment_time' => $this->faker->time('H:i'),
        ]);
    }

    /**
     * Create appointment for tomorrow.
     */
    public function tomorrow(): static
    {
        return $this->state(fn(array $attributes) => [
            'appointment_date' => now()->addDay()->format('Y-m-d'),
            'appointment_time' => $this->faker->time('H:i'),
        ]);
    }

    /**
     * Create appointment for next week.
     */
    public function nextWeek(): static
    {
        return $this->state(fn(array $attributes) => [
            'appointment_date' => now()->addWeek()->format('Y-m-d'),
            'appointment_time' => $this->faker->time('H:i'),
        ]);
    }
}