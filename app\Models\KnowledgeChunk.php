<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Pgvector\Laravel\HasNeighbors;
use Pgvector\Laravel\Vector;

final class KnowledgeChunk extends Model
{
    use HasNeighbors;

    protected $fillable = [
        'knowledge_file_id',
        'content',
        'embedding',
        'chunk_index',
        'metadata',
    ];

    protected $casts = [
        'embedding' => Vector::class,
        'metadata'  => 'array',
    ];

    public function knowledgeFile(): BelongsTo
    {
        return $this->belongsTo(KnowledgeFile::class);
    }

}
