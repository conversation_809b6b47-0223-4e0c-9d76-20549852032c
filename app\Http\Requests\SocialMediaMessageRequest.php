<?php

declare (strict_types = 1);

namespace App\Http\Requests;

class SocialMediaMessageRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'platform'     => 'required|string|in:facebook,instagram',
            'recipient_id' => 'required|string',
            'message'      => 'required|string|max:2000',
            'media_url'    => 'sometimes|url',
            'media_type'   => 'sometimes|string|in:image,video,audio,file',
        ];
    }
}