<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Traits\HasMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

final class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, HasMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'address',
        'company',
        'timezone',
        'about',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password'          => 'hashed',
        ];
    }

    /**
     * Get the user's full name.
     */
    public function getNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get the user's avatar URL.
     */
    public function getAvatarAttribute(): ?string
    {
        $avatar = $this->getFirstMedia('avatars');
        return $avatar ? $avatar->url : null;
    }

    // /**
    //  * Get the platform tokens for this user.
    //  */
    public function platformTokens(): HasMany
    {
        return $this->hasMany(Token::class);
    }

    /**
     * Get the Instagram tokens for this user through the Token relationship.
     */
    public function instagramTokens()
    {
        return $this->hasMany(Token::class)
            ->where('tokenable_type', InstagramToken::class)
            ->with('tokenable');
    }

    // Add the relationship to UserAgent in the User model
    public function userAgents()
    {
        return $this->hasMany(UserAgent::class);
    }

    /**
     * Get all appointments for this user
     */
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }
}