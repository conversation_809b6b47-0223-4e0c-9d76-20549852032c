<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class SocialMediaMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id'        => $this->id,
            'sender_id' => $this->sender_id,
            'platform'  => $this->platform,
            'content'   => $this->message, // Changed from $this->content to $this->message
            'reply'            => $this->reply,
            'status'           => $this->status,
            'message_type'     => $this->meta_data['message_type'] ?? 'text',
            'sent_at'          => $this->sent_at?->toISOString(),
            'replied_at'       => $this->updated_at?->toISOString(),
            'response_time_ms' => $this->getResponseTimeMs(),
            'metadata'         => $this->formatMetadata(),
            'timestamps'       => [
                'created_at' => $this->created_at?->toISOString(),
                'updated_at' => $this->updated_at?->toISOString(),
            ],
        ];
    }

    /**
     * Get response time in milliseconds
     */
    private function getResponseTimeMs(): ?int
    {
        if (!$this->sent_at || !$this->updated_at || $this->status !== 'replied') {
            return null;
        }

        return $this->updated_at->diffInMilliseconds($this->sent_at);
    }

    /**
     * Format metadata for API response
     */
    private function formatMetadata(): array
    {
        $metadata = $this->meta_data ?? [];

        return [
            'platform'     => $metadata['platform'] ?? $this->platform,
            'message_type' => $metadata['message_type'] ?? 'text',
            'campaign_id'  => $metadata['campaign_id'] ?? null,
            'tags'         => $metadata['tags'] ?? [],
            'priority'     => $metadata['priority'] ?? 'normal',
            'has_media'    => isset($metadata['media_url']),
            'is_reply'     => isset($metadata['reply_to_message_id']),
            'source'       => $metadata['source'] ?? 'webhook',
        ];
    }
}
