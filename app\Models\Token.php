<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

final class Token extends Model
{
    use HasFactory;

    protected $fillable = [
        'platform_id',
        'user_id',
        'tokenable_type',
        'tokenable_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the platform that owns this token.
     */
    public function platform(): BelongsTo
    {
        return $this->belongsTo(Platform::class);
    }

    /**
     * Get the user that owns this token.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the platform-specific token model.
     */
    public function tokenable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get all appointments associated with this token.
     */
    public function appointments(): Has<PERSON>any
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Scope to get tokens for a specific platform.
     */
    public function scopeForPlatform($query, string $platformKey): void
    {
        $query->whereHas('platform', function ($q) use ($platformKey) {
            $q->where('key', $platformKey);
        });
    }

    /**
     * Scope to get active tokens.
     */
    public function scopeActive($query): void
    {
        $query->whereHasMorph('tokenable', [InstagramToken::class], function ($q) {
            $q->where('is_active', true);
        });
    }

    /**
     * Scope to get tokens for a specific user.
     */
    public function scopeForUser($query, int $userId): void
    {
        $query->where('user_id', $userId);
    }

    /**
     * Get the platform-specific token data.
     */
    public function getTokenData(): ?Model
    {
        return $this->tokenable;
    }

    /**
     * Check if the token is active.
     */
    public function isActive(): bool
    {
        $tokenData = $this->getTokenData();

        if (!$tokenData) {
            return false;
        }

        // Check if token has is_active property
        if (property_exists($tokenData, 'is_active')) {
            return $tokenData->is_active;
        }

        // Check if token has status property
        if (property_exists($tokenData, 'status')) {
            return $tokenData->status === 'active';
        }

        return false;
    }

    /**
     * Check if the token is expired.
     */
    public function isExpired(): bool
    {
        $tokenData = $this->getTokenData();

        if (!$tokenData) {
            return true;
        }

        // Check if token has expires_at property
        if (property_exists($tokenData, 'expires_at') && $tokenData->expires_at) {
            return $tokenData->expires_at->isPast();
        }

        return false;
    }
}
