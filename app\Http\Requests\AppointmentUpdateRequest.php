<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class AppointmentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_name'    => 'sometimes|string|max:255',
            'customer_id'      => 'sometimes|string|max:255',
            'customer_phone'   => 'nullable|string|max:20',
            'customer_email'   => 'nullable|email|max:255',
            'user_agent_id'    => 'sometimes|integer|exists:user_agents,id',
            'token_id'         => 'nullable|integer|exists:tokens,id',
            'appointment_date' => 'sometimes|date|after_or_equal:today',
            'appointment_time' => 'sometimes|date_format:H:i',
            'duration_minutes' => 'sometimes|integer|min:15|max:480',
            'service_name'     => 'sometimes|string|max:255',
            'service_key'      => 'nullable|string|max:255',
            'service_price'    => 'nullable|numeric|min:0|max:99999.99',
            'service_category' => 'nullable|string|max:255',
            'status'           => 'sometimes|string|in:pending,confirmed,in_progress,completed,cancelled,no_show',
            'notes'            => 'nullable|string|max:1000',
            'metadata'         => 'nullable|array',
            'booking_method'   => 'sometimes|string|in:chatbot,manual,api,phone,website',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_agent_id.exists'            => 'The selected user agent does not exist.',
            'token_id.exists'                 => 'The selected token does not exist.',
            'appointment_date.after_or_equal' => 'The appointment date must be today or in the future.',
            'appointment_time.date_format'    => 'The appointment time must be in HH:MM format.',
            'duration_minutes.min'            => 'The duration must be at least 15 minutes.',
            'duration_minutes.max'            => 'The duration cannot exceed 8 hours.',
            'service_price.numeric'           => 'The service price must be a valid number.',
            'service_price.min'               => 'The service price cannot be negative.',
            'service_price.max'               => 'The service price cannot exceed 99,999.99.',
            'status.in'                       => 'The status must be one of: pending, confirmed, in_progress, completed, cancelled, no_show.',
            'notes.max'                       => 'The notes cannot exceed 1000 characters.',
            'booking_method.in'               => 'The booking method must be one of: chatbot, manual, api, phone, website.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation for user agent ownership
            if ($this->has('user_agent_id')) {
                $userAgent = \App\Models\UserAgent::find($this->user_agent_id);
                if ($userAgent && $userAgent->user_id !== auth()->id()) {
                    $validator->errors()->add('user_agent_id', 'You can only update appointments for your own agents.');
                }
            }

            // Custom validation for token ownership
            if ($this->has('token_id') && $this->token_id) {
                $token = \App\Models\Token::find($this->token_id);
                if ($token && $token->user_id !== auth()->id()) {
                    $validator->errors()->add('token_id', 'You can only use your own tokens.');
                }
            }
        });
    }
}