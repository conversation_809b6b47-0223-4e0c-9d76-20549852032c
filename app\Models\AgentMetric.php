<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AgentMetric extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'agent_id',
        'date',
        // General metrics
        'total_interactions',
        'unique_users',
        // Conversation metrics
        'message_count',
        'conversation_count',
        'avg_conversation_duration',
        'avg_response_time',
        'abandoned_conversations',
        // Task completion metrics
        'tasks_attempted',
        'tasks_completed',
        'task_completion_rate',
        // Booking specific metrics
        'booking_count',
        'response_count',
        'response_rate',
        'successful_bookings',
        'cancelled_bookings',
        'rescheduled_bookings',
        // Sales specific metrics
        'leads_generated',
        'qualified_leads',
        'conversions',
        'conversion_rate',
        'revenue_generated',
        // Marketing specific metrics
        'campaign_interactions',
        'content_shares',
        'link_clicks',
        'engagement_rate',
        // Customer satisfaction
        'satisfaction_score',
        'positive_feedback_count',
        'negative_feedback_count',
        // Error metrics
        'error_count',
        'fallback_responses',
        'error_rate',
        // Additional data
        'additional_metrics',
        'peak_hours',
        'popular_topics',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date'                      => 'date',
        'avg_conversation_duration' => 'decimal:2',
        'avg_response_time'         => 'decimal:2',
        'task_completion_rate'      => 'decimal:2',
        'response_rate'             => 'decimal:2',
        'conversion_rate'           => 'decimal:2',
        'revenue_generated'         => 'decimal:2',
        'engagement_rate'           => 'decimal:2',
        'satisfaction_score'        => 'decimal:2',
        'error_rate'                => 'decimal:2',
        'additional_metrics'        => 'json',
        'peak_hours'                => 'json',
        'popular_topics'            => 'json',
    ];

    /**
     * Get the user agent that owns the metric.
     */
    public function userAgent(): BelongsTo
    {
        return $this->belongsTo(UserAgent::class, 'agent_id');
    }

    /**
     * Scope a query to only include metrics for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->whereDate('date', $date);
    }

    /**
     * Scope a query to only include metrics for today.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', now()->toDateString());
    }

    /**
     * Scope a query to only include metrics for the last X days.
     */
    public function scopeLastDays($query, $days)
    {
        return $query->whereDate('date', '>=', now()->subDays($days)->toDateString());
    }

    /**
     * Calculate response rate based on booking and response counts.
     */
    public function calculateResponseRate(): float
    {
        if ($this->booking_count === 0) {
            return 0;
        }

        return round(($this->response_count / $this->booking_count) * 100, 2);
    }

    /**
     * Calculate task completion rate based on attempted and completed tasks.
     */
    public function calculateTaskCompletionRate(): float
    {
        if ($this->tasks_attempted === 0) {
            return 0;
        }

        return round(($this->tasks_completed / $this->tasks_attempted) * 100, 2);
    }

    /**
     * Calculate conversion rate based on leads and conversions.
     */
    public function calculateConversionRate(): float
    {
        if ($this->leads_generated === 0) {
            return 0;
        }

        return round(($this->conversions / $this->leads_generated) * 100, 2);
    }

    /**
     * Calculate error rate based on total interactions and errors.
     */
    public function calculateErrorRate(): float
    {
        if ($this->total_interactions === 0) {
            return 0;
        }

        return round(($this->error_count / $this->total_interactions) * 100, 2);
    }
}