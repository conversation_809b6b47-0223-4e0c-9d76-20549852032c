<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agent_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_agent_id')->constrained('user_agents')->onDelete('cascade');
            $table->date('date');

            // General metrics
            $table->integer('total_interactions')->default(0);
            $table->integer('unique_users')->default(0);

            // Conversation metrics
            $table->integer('message_count')->default(0);
            $table->integer('conversation_count')->default(0);
            $table->decimal('avg_conversation_duration', 10, 2)->default(0); // in seconds
            $table->decimal('avg_response_time', 10, 2)->default(0); // in seconds
            $table->integer('abandoned_conversations')->default(0);

            // Task completion metrics
            $table->integer('tasks_attempted')->default(0);
            $table->integer('tasks_completed')->default(0);
            $table->decimal('task_completion_rate', 5, 2)->default(0);

            // Booking specific metrics (for appointment booking agents)
            $table->integer('booking_count')->default(0);
            $table->integer('response_count')->default(0);
            $table->decimal('response_rate', 5, 2)->default(0);
            $table->integer('successful_bookings')->default(0);
            $table->integer('cancelled_bookings')->default(0);
            $table->integer('rescheduled_bookings')->default(0);

            // Sales specific metrics (for sales agents)
            $table->integer('leads_generated')->default(0);
            $table->integer('qualified_leads')->default(0);
            $table->integer('conversions')->default(0);
            $table->decimal('conversion_rate', 5, 2)->default(0);
            $table->decimal('revenue_generated', 10, 2)->default(0);

            // Marketing specific metrics (for marketing agents)
            $table->integer('campaign_interactions')->default(0);
            $table->integer('content_shares')->default(0);
            $table->integer('link_clicks')->default(0);
            $table->decimal('engagement_rate', 5, 2)->default(0);

            // Customer satisfaction
            $table->decimal('satisfaction_score', 3, 2)->default(0); // 0-5 scale
            $table->integer('positive_feedback_count')->default(0);
            $table->integer('negative_feedback_count')->default(0);

            // Error metrics
            $table->integer('error_count')->default(0);
            $table->integer('fallback_responses')->default(0);
            $table->decimal('error_rate', 5, 2)->default(0);

            // Additional data
            $table->json('additional_metrics')->nullable();
            $table->json('peak_hours')->nullable();
            $table->json('popular_topics')->nullable();

            $table->timestamps();

            // Unique constraint to prevent duplicate metrics for the same user agent and date
            $table->unique(['user_agent_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agent_metrics');
    }
};
