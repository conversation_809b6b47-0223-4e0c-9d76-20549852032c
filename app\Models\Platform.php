<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Platform extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'key',
        'description',
        'category_id',
        'enabled',
    ];

    protected $casts = [
        'enabled' => 'boolean',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'key';
    }

    /**
     * Scope a query to only include enabled platforms.
     */
    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope a query to only include platforms by key.
     */
    public function scopeByKey(Builder $query, string $key): Builder
    {
        return $query->where('key', $key);
    }

    /**
     * Scope a query to only include platforms by category.
     */
    public function scopeByCategory(Builder $query, int $categoryId): Builder
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Check if platform is enabled by superadmin.
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Check if platform is connected for a specific user.
     */
    public function isConnectedForUser(int $userId): bool
    {
        return $this->platformTokens()
            ->forUser($userId)
            ->active()
            ->exists();
    }

    /**
     * Get active token for a specific user.
     */
    public function getActiveTokenForUser(int $userId): ?Token
    {
        return $this->platformTokens()
            ->forUser($userId)
            ->active()
            ->with('tokenable')
            ->first();
    }

    /**
     * Get Instagram token for a specific user (for backward compatibility).
     */
    public function getInstagramTokenForUser(int $userId): ?InstagramToken
    {
        if ($this->key !== 'instagram') {
            return null;
        }

        $token = $this->platformTokens()
            ->forUser($userId)
            ->active()
            ->whereHasMorph('tokenable', [InstagramToken::class])
            ->with('tokenable')
            ->first();

        return $token?->tokenable;
    }

    /**
     * Get the platform tokens for this platform.
     */
    public function platformTokens(): HasMany
    {
        return $this->hasMany(Token::class);
    }

    /**
     * Get the platform category for this platform.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PlatformCategory::class, 'category_id');
    }

    /**
     * Get category name for this platform.
     */
    public function getCategoryName(): ?string
    {
        return $this->category?->name;
    }

    /**
     * Get category description for this platform.
     */
    public function getCategoryDescription(): ?string
    {
        return $this->category?->description;
    }

    /**
     * Get platform status with additional context for a specific user.
     */
    public function getStatusWithContextForUser(int $userId): array
    {
        $status = [
            'enabled'              => $this->enabled,
            'connected'            => $this->isConnectedForUser($userId),
            'category'             => $this->getCategoryName(),
            'category_description' => $this->getCategoryDescription(),
            'description'          => $this->description,
        ];

        $activeToken = $this->getActiveTokenForUser($userId);
        if ($activeToken) {
            $status['token_status']                = $activeToken->status;
            $status['token_expires_at']            = $activeToken->expires_at?->format('Y-m-d H:i:s');
            $status['token_days_until_expiration'] = $activeToken->days_until_expiration;
            $status['username']                    = $activeToken->username;
            $status['account_type']                = $activeToken->account_type;
        }

        return $status;
    }

    /**
     * Get platform summary for API responses for a specific user.
     */
    public function toApiArrayForUser(int $userId): array
    {
        $data = [
            'id'                   => $this->id,
            'name'                 => $this->name,
            'key'                  => $this->key,
            'category'             => $this->getCategoryName(),
            'category_description' => $this->getCategoryDescription(),
            'description'          => $this->description,
            'enabled'              => $this->enabled,
            'connected'            => $this->isConnectedForUser($userId),
            'created_at'           => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at'           => $this->updated_at->format('Y-m-d H:i:s'),
        ];

        // Add token information if connected
        $activeToken = $this->getActiveTokenForUser($userId);
        if ($activeToken) {
            $data['token'] = [
                'status'                => $activeToken->status,
                'expires_at'            => $activeToken->expires_at?->format('Y-m-d H:i:s'),
                'days_until_expiration' => $activeToken->days_until_expiration,
                'username'              => $activeToken->username,
                'account_type'          => $activeToken->account_type,
            ];
        }

        return $data;
    }

    /**
     * Get platform summary for API responses (backward compatibility).
     */
    public function toApiArray(): array
    {
        $data = [
            'id'                   => $this->id,
            'name'                 => $this->name,
            'key'                  => $this->key,
            'category'             => $this->getCategoryName(),
            'category_description' => $this->getCategoryDescription(),
            'description'          => $this->description,
            'enabled'              => $this->enabled,
            'connected'            => false, // Default to false since we need user context
            'created_at'           => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at'           => $this->updated_at->format('Y-m-d H:i:s'),
        ];

        return $data;
    }
}
