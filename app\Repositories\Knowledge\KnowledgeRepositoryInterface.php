<?php

declare (strict_types = 1);

namespace App\Repositories\Knowledge;

use App\Models\KnowledgeChunk;
use App\Models\KnowledgeFile;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface KnowledgeRepositoryInterface
{
    // File operations
    public function createFile(array $data): KnowledgeFile;

    public function findFileById(int $id): ?KnowledgeFile;

    public function updateFile(KnowledgeFile $file, array $data): KnowledgeFile;

    public function deleteFile(KnowledgeFile $file): bool;

    public function getCompletedFiles(): Collection;

    public function getFilesByType(string $type): Collection;

    public function getAllFiles(int $perPage = 15): LengthAwarePaginator;

    // Chunk operations
    public function createChunk(array $data): KnowledgeChunk;

    public function createChunks(array $chunks): Collection;

    public function findChunkById(int $id): ?KnowledgeChunk;

    public function getChunksForFile(int $fileId): Collection;

    public function findSimilarChunks(array $embedding, int $limit = 5): Collection;

    public function findSimilarChunksWithThreshold(
        array $embedding,
        float $threshold = 0.8,
        int $limit = 5
    ): Collection;

    public function findSimilarChunksForUserAgent(int $userAgentId, array $embedding, int $limit = 5): Collection;

    public function getChunksWithFiles(): Collection;

    public function deleteChunksForFile(int $fileId): bool;

    public function updateChunkEmbedding(KnowledgeChunk $chunk, array $embedding): KnowledgeChunk;

    public function getChunkCountForFile(int $fileId): int;

    // File status operations
    public function markFileAsCompleted(KnowledgeFile $file): KnowledgeFile;

    public function markFileAsFailed(KnowledgeFile $file, string $errorMessage): KnowledgeFile;

    // Statistics
    public function getKnowledgeStats(): array;
}
