<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InstagramToken;
use App\Models\Message;
use App\Services\ChatbotService;
use App\Services\InstagramService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class InstagramWebhookController extends Controller
{
    public function __construct(
        private readonly InstagramService $instagramService,
        private readonly ChatbotService $chatbotService
    ) {}

    /**
     * Handle webhook verification (GET request)
     */
    public function verify(Request $request): JsonResponse | \Illuminate\Http\Response
    {
        $mode      = $request->query('hub_mode');
        $token     = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        $verifyToken = config('services.instagram.webhook_verify_token');

        if ($mode === 'subscribe' && $token === $verifyToken) {
            Log::info('Instagram webhook verified successfully');
            return response($challenge, 200);
        }

        Log::warning('Instagram webhook verification failed', [
            'mode'           => $mode,
            'token_provided' => $token,
            'token_expected' => $verifyToken,
        ]);

        return response()->json(['error' => 'Forbidden'], 403);
    }

    /**
     * Handle incoming webhook messages (POST request)
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            $data = $request->all();

            Log::info('Instagram webhook received', [
                'method'          => $request->method(),
                'object'          => $data['object'] ?? 'unknown',
                'entry_count'     => count($data['entry'] ?? []),
                'x_hub_signature' => $request->header('X-Hub-Signature-256') ? 'present' : 'missing',
            ]);

            if (app()->environment(['local', 'development'])) {
                Log::debug('Instagram webhook full payload', [
                    'data' => $data,
                ]);
            }

            if (!isset($data['object']) || $data['object'] !== 'instagram') {
                Log::info('Non-Instagram webhook received', ['object' => $data['object'] ?? 'unknown']);
                return response()->json(['status' => 'ignored']);
            }

            if (!$this->validateWebhookSignature($request)) {
                Log::warning('Invalid webhook signature');
                return response()->json(['status' => 'invalid_signature'], 401);
            }

            foreach ($data['entry'] ?? [] as $entry) {
                $instagramAccountId = $entry['id'] ?? null;

                Log::info('Processing Instagram webhook entry', [
                    'entry_id'      => $instagramAccountId,
                    'timestamp'     => $entry['time'] ?? null,
                    'has_messaging' => isset($entry['messaging']),
                    'has_changes'   => isset($entry['changes']),
                    'fields'        => isset($entry['changes'])
                    ? array_column($entry['changes'], 'field')
                    : [],
                ]);

                $token = $this->findTokenForAccount($instagramAccountId);

                if (isset($entry['messaging'])) {
                    foreach ($entry['messaging'] as $messaging) {
                        if (isset($messaging['message']['is_echo']) && $messaging['message']['is_echo'] === true) {
                            Log::info('Ignoring echo message from our bot', [
                                'message_id' => $messaging['message']['mid'] ?? 'unknown',
                            ]);
                            continue;
                        }

                        // Skip processing if it's a read receipt
                        if (isset($messaging['read'])) {
                            Log::info('Received read receipt', [
                                'message_id' => $messaging['read']['mid'] ?? 'unknown',
                            ]);
                            continue;
                        }

                        // Check if we've already processed this message
                        $messageId = $messaging['message']['mid'] ?? null;
                        if ($messageId && $this->isMessageAlreadyProcessed($messageId)) {
                            Log::info('Skipping already processed message', [
                                'message_id' => $messageId,
                            ]);
                            continue;
                        }

                        if (isset($messaging['message']['text'])) {
                            Log::info('WEBHOOK: Processing text message', [
                                'sender_id'    => $messaging['sender']['id'] ?? null,
                                'recipient_id' => $messaging['recipient']['id'] ?? null,
                                'message'      => $messaging['message']['text'],
                            ]);
                            $this->processMessage($messaging, $token);
                        } else if (isset($messaging['reaction'])) {
                            $this->processReaction($messaging, $token);
                        } else if (isset($messaging['postback'])) {
                            $this->processPostback($messaging, $token);
                        } else {
                            Log::info('Unknown Instagram message format', [
                                'sender_id'    => $messaging['sender']['id'] ?? null,
                                'recipient_id' => $messaging['recipient']['id'] ?? null,
                                'keys'         => array_keys($messaging),
                            ]);
                        }
                    }
                }

                if (isset($entry['changes'])) {
                    foreach ($entry['changes'] as $change) {
                        $field = $change['field'] ?? '';
                        Log::info('Processing Instagram webhook change', [
                            'field'      => $field,
                            'account_id' => $instagramAccountId,
                            'has_token'  => $token ? 'yes' : 'no',
                        ]);

                        if ($field === 'comments') {
                            $this->processComment($change, $instagramAccountId, $token);
                        } else if ($field === 'mentions') {
                            Log::info('Instagram mention received', [
                                'change'     => $change,
                                'account_id' => $instagramAccountId,
                            ]);
                        } else {
                            Log::info('Unknown Instagram webhook field', [
                                'field'      => $field,
                                'account_id' => $instagramAccountId,
                                'change'     => $change,
                            ]);
                        }
                    }
                }
            }

            return response()->json(['status' => 'success']);

        } catch (Exception $e) {
            Log::error('Instagram webhook processing failed', [
                'error' => $e->getMessage(),
            ]);

            return response()->json(['status' => 'error'], 200);
        }
    }

    /**
     * Check if a message has already been processed to prevent duplicates
     */
    private function isMessageAlreadyProcessed(string $messageId): bool
    {
        $cacheKey = 'instagram_processed_message_' . $messageId;

        // If the message ID exists in cache, it's been processed
        if (Cache::has($cacheKey)) {
            return true;
        }

        // Check if it exists in the database
        $exists = Message::where('message_id', $messageId)
            ->where('platform', 'instagram')
            ->exists();

        if ($exists) {
            // Store in cache to prevent future database lookups
            Cache::put($cacheKey, true, now()->addDays(1));
            return true;
        }

        // Mark this message as processed
        Cache::put($cacheKey, true, now()->addDays(1));
        return false;
    }

    /**
     * Find the token associated with an Instagram account ID
     */
    private function findTokenForAccount(?string $instagramAccountId): ?InstagramToken
    {
        if (!$instagramAccountId) {
            return null;
        }

        // Find token by using either app_scope_id or ig_id since user_id is now stored in tokens table
        return InstagramToken::where(function ($query) use ($instagramAccountId) {
            $query->where('ig_id', $instagramAccountId);
        })
            ->where('is_active', true)
            ->first();
    }

    /**
     * Validate webhook signature if app secret is configured
     */
    private function validateWebhookSignature(Request $request): bool
    {
        $appSecret = config('services.instagram.app_secret');

        if (empty($appSecret)) {
            return true;
        }

        $signature = $request->header('X-Hub-Signature-256');

        if (empty($signature) && app()->environment(['local', 'development', 'testing'])) {
            return true;
        }

        if (empty($signature)) {
            return false;
        }

        $payload           = $request->getContent();
        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $appSecret);

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Process incoming Instagram message
     */
    private function processMessage(array $messaging, ?InstagramToken $token = null): void
    {
        try {
            $senderId    = $messaging['sender']['id'] ?? null;
            $recipientId = $messaging['recipient']['id'] ?? null;
            $messageText = $messaging['message']['text'] ?? '';
            $timestamp   = $messaging['timestamp'] ?? time();
            $messageId   = $messaging['message']['mid'] ?? null;

            if (!$senderId || !$recipientId) {
                Log::warning('Missing sender or recipient ID in Instagram message', [
                    'messaging' => $messaging,
                ]);
                return;
            }

            if (!$token) {
                $token = $this->instagramService->findAccountForMessage($senderId, $recipientId);
            }

            Log::info('Processing Instagram message', [
                'sender_id'      => $senderId,
                'recipient_id'   => $recipientId,
                'message_length' => strlen($messageText),
                'timestamp'      => $timestamp,
            ]);

            $messageData = [
                'sender_id'    => $senderId,
                'recipient_id' => $recipientId,
                'message'      => $messageText, // Changed from 'content' to 'message'
                'timestamp'    => $timestamp,
                'platform'     => 'instagram',
                'message_id'   => $messageId, // This should store the Instagram 'mid'
                'meta_data'    => [
                    'raw_messaging' => $messaging,
                    'token_id'      => $token?->id,
                    'user_id'       => $token?->getUserId(),
                    'username'      => $token?->username,
                ],
            ];

            Log::info('WEBHOOK: Calling chatbot service for text message', [
                'message_data' => $messageData,
            ]);

            $result = $this->chatbotService->processMessage($messageData);

            Log::info('WEBHOOK: Chatbot service completed for text message', [
                'message_id' => $result->id,
                'reply'      => $result->reply,
            ]);

        } catch (Exception $e) {
            Log::error('Instagram message processing failed', [
                'error'     => $e->getMessage(),
                'messaging' => $messaging,
            ]);
        }
    }

    /**
     * Process reaction to a message
     */
    private function processReaction(array $messaging, ?InstagramToken $token = null): void
    {
        try {
            $senderId    = $messaging['sender']['id'] ?? null;
            $recipientId = $messaging['recipient']['id'] ?? null;
            $timestamp   = $messaging['timestamp'] ?? time();
            $messageId   = $messaging['reaction']['mid'] ?? null;
            $action      = $messaging['reaction']['action'] ?? null;
            $reaction    = $messaging['reaction']['reaction'] ?? null;

            if (!$token) {
                $token = $this->instagramService->findAccountForMessage($senderId, $recipientId);
            }

            Log::info('Instagram reaction received', [
                'sender_id'    => $senderId,
                'recipient_id' => $recipientId,
                'message_id'   => $messageId,
                'action'       => $action,
                'reaction'     => $reaction,
            ]);

        } catch (Exception $e) {
            Log::error('Instagram reaction processing failed', [
                'error'     => $e->getMessage(),
                'messaging' => $messaging,
            ]);
        }
    }

    /**
     * Process postback (button clicks)
     */
    private function processPostback(array $messaging, ?InstagramToken $token = null): void
    {
        try {
            $senderId    = $messaging['sender']['id'] ?? null;
            $recipientId = $messaging['recipient']['id'] ?? null;
            $timestamp   = $messaging['timestamp'] ?? time();
            $messageId   = $messaging['postback']['mid'] ?? null;
            $title       = $messaging['postback']['title'] ?? null;
            $payload     = $messaging['postback']['payload'] ?? null;

            if (!$token) {
                $token = $this->instagramService->findAccountForMessage($senderId, $recipientId);
            }

            $messageData = [
                'sender_id'    => $senderId,
                'recipient_id' => $recipientId,
                'message'      => $payload, // Changed from 'content' to 'message'
                'timestamp'    => $timestamp,
                'platform'     => 'instagram',
                'message_id'   => $messageId,
                'meta_data'    => [
                    'raw_messaging' => $messaging,
                    'is_postback'   => true,
                    'title'         => $title,
                    'token_id'      => $token?->id,
                    'user_id'       => $token?->getUserId(),
                    'username'      => $token?->username,
                ],
            ];

            $this->chatbotService->processMessage($messageData);

        } catch (Exception $e) {
            Log::error('Instagram postback processing failed', [
                'error'     => $e->getMessage(),
                'messaging' => $messaging,
            ]);
        }
    }

    /**
     * Process comment on Instagram post
     */
    private function processComment(array $change, ?string $instagramAccountId, ?InstagramToken $token = null): void
    {
        try {
            $value            = $change['value'] ?? [];
            $commentId        = $value['id'] ?? $value['comment_id'] ?? null;
            $fromId           = $value['from']['id'] ?? null;
            $username         = $value['from']['username'] ?? null;
            $text             = $value['text'] ?? null;
            $mediaId          = $value['media']['id'] ?? null;
            $mediaProductType = $value['media']['media_product_type'] ?? null;

            if (!$token && $instagramAccountId) {
                // Find token using either app_scope_id or ig_id
                $token = InstagramToken::where(function ($query) use ($instagramAccountId) {
                    $query->where('ig_id', $instagramAccountId);
                })
                    ->where('is_active', true)
                    ->first();
            }

            Log::info('Instagram comment received', [
                'comment_id'  => $commentId,
                'from_id'     => $fromId,
                'username'    => $username,
                'text_length' => strlen($text ?? ''),
                'media_id'    => $mediaId,
            ]);

        } catch (Exception $e) {
            Log::error('Instagram comment processing failed', [
                'error'  => $e->getMessage(),
                'change' => $change,
            ]);
        }
    }
}
