<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'             => $this->id,
            'customer'       => [
                'name'    => $this->customer_name,
                'phone'   => $this->customer_phone,
                'email'   => $this->customer_email,
                'address' => $this->customer_address,
            ],
            'platform'       => [
                'customer_id' => $this->platform_customer_id,
                'username'    => $this->platform_username,
                'name'        => $this->platform_name,
            ],
            'appointment'    => [
                'date'             => $this->appointment_date?->format('Y-m-d'),
                'time'             => $this->appointment_time?->format('H:i'),
                'datetime'         => $this->appointment_date_time?->format('Y-m-d H:i:s'),
                'duration_minutes' => $this->duration_minutes,
            ],
            'service'        => [
                'name'     => $this->service_name,
                'key'      => $this->service_key,
                'price'    => $this->service_price ? (float) $this->service_price : null,
                'category' => $this->service_category,
            ],
            'status'         => $this->status,
            'booking'        => [
                'method'       => $this->booking_method,
                'booked_at'    => $this->booked_at?->format('Y-m-d H:i:s'),
                'confirmed_at' => $this->confirmed_at?->format('Y-m-d H:i:s'),
            ],
            // conversation_id removed
            'notes'          => $this->notes,
            'metadata'       => $this->metadata,
            'user'           => $this->whenLoaded('user', function () {
                return [
                    'id'    => $this->user->id,
                    'name'  => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),
            'user_agent'     => $this->whenLoaded('userAgent', function () {
                return [
                    'id'     => $this->userAgent->id,
                    'name'   => $this->userAgent->name,
                    'status' => $this->userAgent->status,
                ];
            }),
            // Eager-loaded nested relation check: token + token.platform
            'token_platform' => $this->when(
                $this->relationLoaded('token') && $this->token && $this->token->relationLoaded('platform'),
                function () {
                    return [
                        'id'   => $this->token->platform->id ?? null,
                        'name' => $this->token->platform->name ?? null,
                        'key'  => $this->token->platform->key ?? null,
                    ];
                }
            ),
            // 'token'       => $this->whenLoaded('token', function () {
            //     return [
            //         'id'          => $this->token->id,
            //         'platform_id' => $this->token->platform_id,
            //     ];
            // }),
            'created_at'     => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at'     => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
