<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Kortana AI - API Documentation</title>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --secondary: #6366f1;
            --bg-dark: #0f172a;
            --bg-darker: #0a0f1c;
            --bg-light: #1e293b;
            --text-light: #e2e8f0;
            --text-muted: #94a3b8;
            --border-color: #334155;
            --code-bg: #1a2234;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Instrument Sans', sans-serif;
            background-color: var(--bg-dark);
            color: var(--text-light);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            background-color: var(--bg-darker);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            background: linear-gradient(to right, #38bdf8, #818cf8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 1.5rem;
        }

        nav a {
            color: var(--text-light);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }

        nav a:hover {
            color: var(--primary);
        }

        .hero {
            padding: 4rem 2rem;
            text-align: center;
            background-color: var(--bg-darker);
            margin-bottom: 2rem;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        h2 {
            font-size: 1.8rem;
            margin: 2rem 0 1rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        h3 {
            font-size: 1.3rem;
            margin: 1.5rem 0 1rem;
            color: var(--primary);
        }

        p {
            margin-bottom: 1rem;
        }

        .endpoint {
            background-color: var(--bg-light);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--primary);
        }

        .endpoint-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .method {
            background-color: var(--primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .method.get {
            background-color: #10b981;
        }

        .method.post {
            background-color: #f59e0b;
        }

        .method.put {
            background-color: #6366f1;
        }

        .method.delete {
            background-color: #ef4444;
        }

        .url {
            font-family: monospace;
            font-size: 1rem;
            color: #a5f3fc;
        }

        .description {
            margin-bottom: 1rem;
        }

        pre {
            background-color: var(--code-bg);
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            margin: 1rem 0;
            font-family: monospace;
        }

        code {
            font-family: monospace;
            color: #a5f3fc;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th,
        td {
            text-align: left;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--bg-darker);
            font-weight: 600;
        }

        .back-to-home {
            display: inline-block;
            margin-top: 2rem;
            color: var(--primary);
            text-decoration: none;
        }

        .back-to-home:hover {
            text-decoration: underline;
        }

        .required {
            color: #ef4444;
            font-size: 0.75rem;
            margin-left: 0.25rem;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            height: 100vh;
            background-color: var(--bg-darker);
            padding: 6rem 1rem 2rem;
            overflow-y: auto;
            border-right: 1px solid var(--border-color);
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 0.5rem;
        }

        .sidebar-nav a {
            color: var(--text-light);
            text-decoration: none;
            display: block;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .sidebar-nav a:hover {
            background-color: var(--bg-light);
        }

        .sidebar-nav .section-title {
            font-weight: 600;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }

        .main-content {
            margin-left: 250px;
            padding-top: 2rem;
        }

        .section-description {
            background-color: rgba(59, 130, 246, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .section-notes {
            background-color: rgba(16, 185, 129, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin: 2rem 0;
        }

        .api-intro {
            max-width: 800px;
            margin: 0 auto 3rem;
        }

        .api-intro p {
            font-size: 1.1rem;
        }

        .api-features {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .api-feature {
            flex: 1 1 250px;
            background-color: var(--bg-light);
            border-radius: 8px;
            padding: 1.5rem;
            border-top: 3px solid var(--primary);
        }

        .api-feature h3 {
            margin-top: 0;
        }

        .response-object {
            margin-left: 1rem;
        }

        .response-property {
            color: #10b981;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding: 1rem;
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>

<body>
    <header>
        <div class="logo">Kortana AI</div>
        <nav>
            <ul>
                <li><a href="/">Home</a></li>
                <li><a href="/api/documentation">API Docs</a></li>
                <li><a href="/privacy-policy">Privacy Policy</a></li>
            </ul>
        </nav>
    </header>

    <div class="sidebar">
        <ul class="sidebar-nav">
            <li><a href="#introduction">Introduction</a></li>
            <li><a href="#getting-started">Getting Started</a></li>
            <li class="section-title">Authentication</li>
            <li><a href="#authentication">Overview</a></li>
            <li><a href="#register">Register</a></li>
            <li><a href="#login">Login</a></li>
            <li><a href="#verify-otp">Verify OTP</a></li>
            <li><a href="#logout">Logout</a></li>
            <li><a href="#forgot-password">Forgot Password</a></li>
            <li class="section-title">Chatbot</li>
            <li><a href="#chatbot">Overview</a></li>
            <li><a href="#send-message">Send Message</a></li>
            <li><a href="#conversations">Conversations</a></li>
            <li><a href="#chatbot-config">Configuration</a></li>
            <li class="section-title">Knowledge Base</li>
            <li><a href="#knowledge-base">Overview</a></li>
            <li><a href="#upload-file">Upload Document</a></li>
            <li><a href="#search">Semantic Search</a></li>
            <li><a href="#ask">Ask Questions</a></li>
            <li class="section-title">Social Media</li>
            <li><a href="#social-media">Overview</a></li>
            <li><a href="#instagram-auth">Instagram Auth</a></li>
            <li><a href="#send-social-message">Send Messages</a></li>
            <li><a href="#webhooks">Webhooks</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="hero">
                <h1>Kortana AI API Documentation</h1>
                <p>Comprehensive guide to integrating with our AI-powered services</p>
            </div>

            <div class="api-intro" id="introduction">
                <h2>Introduction</h2>
                <p>
                    Welcome to the Kortana AI API documentation. Our API provides access to powerful AI capabilities
                    including
                    chatbot services, knowledge base management, and social media integrations. This documentation will
                    help you
                    understand how to authenticate and interact with our API endpoints.
                </p>

                <div class="api-features">
                    <div class="api-feature">
                        <h3>AI Chatbot</h3>
                        <p>Intelligent conversational AI that can be customized with your knowledge base to provide
                            domain-specific responses.</p>
                    </div>
                    <div class="api-feature">
                        <h3>Knowledge Base</h3>
                        <p>Upload documents, perform semantic search, and ask questions based on your organization's
                            knowledge.</p>
                    </div>
                    <div class="api-feature">
                        <h3>Social Media</h3>
                        <p>Integrate with Facebook and Instagram to send and receive messages through your business
                            accounts.</p>
                    </div>
                </div>
            </div>

            <div id="getting-started">
                <h2>Getting Started</h2>
                <p>
                    To use the Kortana AI API, you'll need to:
                </p>
                <ol>
                    <li>Register for an account</li>
                    <li>Obtain an authentication token</li>
                    <li>Include the token in your API requests</li>
                </ol>

                <h3>Base URL</h3>
                <p>All API endpoints use the following base URL:</p>
                <pre><code>https://api.kortana-ai.example</code></pre>

                <h3>API Versioning</h3>
                <p>
                    Our API uses versioning to ensure backward compatibility. The current version is <code>v1</code>,
                    which is
                    included in the endpoint paths (e.g., <code>/api/v1/auth/login</code>).
                </p>

                <h3>Response Format</h3>
                <p>
                    All API responses follow a consistent JSON format:
                </p>
                <pre><code>{
  "success": true|false,       // Boolean indicating if the request was successful
  "message": "Status message", // Human-readable message
  "code": 200,                 // HTTP status code
  "data": { ... }              // Response data (if applicable)
}</code></pre>

                <h3>Error Handling</h3>
                <p>
                    When an error occurs, the API returns a JSON response with <code>success: false</code> and an
                    appropriate
                    HTTP status code. For validation errors, the response includes an <code>errors</code> object with
                    field-specific error messages.
                </p>
                <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "email": [
      "The email field is required."
    ],
    "password": [
      "The password field is required."
    ]
  }
}</code></pre>
            </div>

            @include('api-documentation.auth')
            @include('api-documentation.chatbot')
            @include('api-documentation.knowledge')
            @include('api-documentation.social-media')

            <a href="/" class="back-to-home">← Back to Home</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight the current section in the sidebar
            const sections = document.querySelectorAll('h2[id]');
            const navLinks = document.querySelectorAll('.sidebar-nav a');

            window.addEventListener('scroll', function() {
                let current = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    if (pageYOffset >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>

</html>
