<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AgentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Get the authenticated user's user agent for this agent
        $userAgent = $this->userAgents->where('user_id', auth('sanctum')->id())->first();

        // Get merged configuration and prompt templates
        $finalConfig  = [];
        $finalPrompts = [];

        if ($userAgent) {
            // Get default configurations from the agent
            $defaultConfig  = is_string($this->default_configuration) ? json_decode($this->default_configuration, true) : $this->default_configuration;
            $defaultPrompts = is_string($this->default_prompt_templates) ? json_decode($this->default_prompt_templates, true) : $this->default_prompt_templates;

            // Get user's customized configurations
            $customConfig  = is_string($userAgent->customize_configuration) ? json_decode($userAgent->customize_configuration, true) : $userAgent->customize_configuration;
            $customPrompts = is_string($userAgent->prompt_templates) ? json_decode($userAgent->prompt_templates, true) : $userAgent->prompt_templates;

            // Merge configurations (custom overrides default)
            $finalConfig  = array_merge($defaultConfig ?? [], $customConfig ?? []);
            $finalPrompts = array_merge($defaultPrompts ?? [], $customPrompts ?? []);
        }

        // Full detailed response for show endpoint
        return [
            'id'                   => $this->id,
            'name'                 => $this->name,
            'key'                  => $this->key,
            'description'          => $this->description,
            'default_capabilities' => is_string($this->default_capabilities) ? json_decode($this->default_capabilities, true) : $this->default_capabilities,
            'conversation_flows'   => is_string($this->conversation_flows) ? json_decode($this->conversation_flows, true) : $this->conversation_flows,
            'is_active'            => $this->is_active,
            'is_subscribed'        => $this->isSubscribedByAuthUser(),
            'configuration'        => $finalConfig, // Merged configuration at agent level
            'prompt_templates'     => $finalPrompts, // Merged prompt templates at agent level
            'user_agent'           => $userAgent ? [
                'id'                    => $userAgent->id,
                'name'                  => $userAgent->name,
                'status'                => $userAgent->status,
                'personality'           => $userAgent->personality,
                'agent_gender'          => $userAgent->agent_gender,
                'company_details'       => is_string($userAgent->company_details) ? json_decode($userAgent->company_details, true) : $userAgent->company_details,
                'agent_business_data'   => is_string($userAgent->agent_business_data) ? json_decode($userAgent->agent_business_data, true) : $userAgent->agent_business_data,
                'notification_settings' => is_string($userAgent->notification_settings) ? json_decode($userAgent->notification_settings, true) : $userAgent->notification_settings,
                'analytics_settings'    => is_string($userAgent->analytics_settings) ? json_decode($userAgent->analytics_settings, true) : $userAgent->analytics_settings,
                'performance_targets'   => is_string($userAgent->performance_targets) ? json_decode($userAgent->performance_targets, true) : $userAgent->performance_targets,
                'escalation_rules'      => is_string($userAgent->escalation_rules) ? json_decode($userAgent->escalation_rules, true) : $userAgent->escalation_rules,
                'knowledge_files'       => $userAgent->knowledgeFiles->map(function ($knowledgeFile) {
                    return [
                        'id'                  => $knowledgeFile->id,
                        'file_name'           => $knowledgeFile->file_name,
                        'original_name'       => $knowledgeFile->original_name,
                        'type'                => $knowledgeFile->type,
                        'file_size'           => $knowledgeFile->file_size,
                        'human_readable_size' => $knowledgeFile->human_readable_size,
                        'mime_type'           => $knowledgeFile->mime_type,
                        'status'              => $knowledgeFile->status,
                        'uploaded_by'         => $knowledgeFile->uploadedBy ? [
                            'id'         => $knowledgeFile->uploadedBy->id,
                            'first_name' => $knowledgeFile->uploadedBy->first_name,
                            'last_name'  => $knowledgeFile->uploadedBy->last_name,
                            'email'      => $knowledgeFile->uploadedBy->email,
                        ] : null,
                        'knowledge_chunks'    => $knowledgeFile->chunks->map(function ($chunk) {
                            return [
                                'id'              => $chunk->id,
                                'content_preview' => substr($chunk->content, 0, 200) . '...',
                                'content_length'  => strlen($chunk->content),
                                'chunk_index'     => $chunk->chunk_index,
                                'metadata'        => is_string($chunk->metadata) ? json_decode($chunk->metadata, true) : $chunk->metadata,
                                'created_at'      => $chunk->created_at,
                            ];
                        }),
                        'chunks_count'        => $knowledgeFile->chunks->count(),
                        'created_at'          => $knowledgeFile->created_at,
                        'updated_at'          => $knowledgeFile->updated_at,
                    ];
                }),
                'created_at'            => $userAgent->created_at,
                'updated_at'            => $userAgent->updated_at,
            ] : null,
            'created_at'           => $this->created_at,
            'updated_at'           => $this->updated_at,
        ];
    }

    /**
     * Check if the authenticated user has subscribed to this agent
     */
    private function isSubscribedByAuthUser(): bool
    {
        $user = auth('sanctum')->user();

        if (!$user) {
            return false;
        }

        return $this->userAgents()
            ->where('user_id', $user->id)->where('status', 'active')
            ->exists();
    }
}