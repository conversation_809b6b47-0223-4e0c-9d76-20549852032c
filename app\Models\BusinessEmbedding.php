<?php

declare (strict_types = 1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Pgvector\Laravel\HasNeighbors;
use Pgvector\Laravel\Vector;

final class BusinessEmbedding extends Model
{
    use HasNeighbors;

    protected $fillable = [
        'user_agent_id',
        'content_type',
        'content_key',
        'content',
        'embedding',
        'metadata',
        'content_hash',
    ];

    protected $casts = [
        'embedding' => Vector::class,
        'metadata'  => 'array',
    ];

    public function userAgent(): BelongsTo
    {
        return $this->belongsTo(UserAgent::class);
    }

    public static function generateContentHash(array $data): string
    {
        return md5(json_encode($data));
    }
}
