<?php

declare (strict_types = 1);

namespace App\Services\Auth;

use App\Jobs\SendEmail;
use App\Models\AuthLog;
use App\Repositories\User\UserRepositoryInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use PragmaRX\Google2FA\Google2FA;

final class AuthService
{
    protected UserRepositoryInterface $repository;
    protected Google2FA $google2fa;

    public function __construct(UserRepositoryInterface $repository)
    {
        $this->repository = $repository;
        $this->google2fa  = new Google2FA();
    }

    public function register(array $data)
    {
        $user = $this->repository->create($data);

        // Add avatar if provided
        if (isset($data['avatar']) && $data['avatar'] instanceof UploadedFile) {
            $user->addMedia($data['avatar'], 'avatars');
        }

        // Generate and send OTP with state code
        $otpData = $this->generateOtpWithState($user->id);
        $this->sendWelcomeEmail($user, $otpData['otp']);

        return [
            'user'       => $user,
            'state_code' => $otpData['state_code'],
            // Don't generate token until email is verified
        ];
    }

    public function login(string $email, string $password, array $metadata = [])
    {
        $cacheKey = 'login_attempts:' . $email;
        $attempts = Cache::get($cacheKey, 0);

        if ($attempts >= 5) {
            $this->logAuthActivity('login', null, ['email' => $email], 'blocked');
            throw new \Exception('Account temporarily locked. Please try again later.', 423);
        }

        $user = $this->repository->findByEmail($email);

        if (!$user || !Hash::check($password, $user->password)) {
            Cache::put($cacheKey, $attempts + 1, now()->addMinutes(15));
            $this->logAuthActivity('login', $user?->id, ['email' => $email], 'failed');

            // Return structured error data instead of throwing exception
            return [
                'error'   => true,
                'message' => 'Invalid credentials',
                'code'    => 401,
                'errors'  => [
                    'email'    => ['The email address or password is incorrect.'],
                    'password' => ['The email address or password is incorrect.'],
                ],
            ];
        }

        Cache::forget($cacheKey);
        $this->logAuthActivity('login', $user->id);

        // Check if email is verified
        if (!$user->email_verified_at) {
            // Generate and send OTP with state code
            $otpData = $this->generateOtpWithState($user->id);
            $this->sendVerificationEmail($user, $otpData['otp']);

            // Return user without token, indicating verification needed
            return [
                'user'                  => $user,
                'requires_verification' => true,
                'state_code'            => $otpData['state_code'],
            ];
        }

        // Handle remember me functionality
        $tokenExpiration = isset($metadata['remember']) && $metadata['remember']
        ? now()->addMonths(6)
        : now()->addDay();

        // Generate token with device info and expiration
        $token = $user->createToken(
            request()->userAgent(),
            ['*'],
            $tokenExpiration
        )->plainTextToken;

        return [
            'user'  => $user,
            'token' => $token,
        ];
    }

    public function otpVerify(string $email, string $otp)
    {
        $user = $this->repository->findByEmail($email);

        if (!$user) {
            return [
                'error'   => true,
                'message' => 'User not found',
                'code'    => 404,
                'errors'  => [
                    'email' => ['The email address is not registered.'],
                ],
            ];
        }

        // Check both email verification and password reset OTPs
        $emailVerificationOtp = cache()->get('email_verification_' . $user->id);
        $passwordResetOtp     = cache()->get('password_reset_' . $user->id);

        $isEmailVerification = $emailVerificationOtp && $otp === $emailVerificationOtp;
        $isPasswordReset     = $passwordResetOtp && $otp === $passwordResetOtp;

        if (!$isEmailVerification && !$isPasswordReset) {
            return [
                'error'   => true,
                'message' => 'Invalid or expired OTP',
                'code'    => 400,
                'errors'  => [
                    'otp' => ['The OTP is invalid or has expired. Please request a new one.'],
                ],
            ];
        }

        // Handle email verification
        if ($isEmailVerification) {
            $this->repository->verifyEmail($user->id);
            cache()->forget('email_verification_' . $user->id);

            // Generate token after successful verification
            $token = $user->createToken(
                request()->userAgent(),
                ['*'],
                now()->addDay()
            )->plainTextToken;

            return [
                'user'  => $user,
                'token' => $token,
                'type'  => 'email_verification',
            ];
        }

        // Handle password reset
        if ($isPasswordReset) {
            cache()->forget('password_reset_' . $user->id);

            // Generate token for password reset (user can now reset their password)
            $token = $user->createToken(
                request()->userAgent(),
                ['*'],
                now()->addMinutes(30) // Shorter token for password reset
            )->plainTextToken;

            return [
                'user'  => $user,
                'token' => $token,
                'type'  => 'password_reset',
            ];
        }
    }

    public function forgotPassword(string $email)
    {
        $user = $this->repository->findByEmail($email);

        if (!$user) {
            return [
                'error'   => true,
                'message' => 'User not found',
                'code'    => 404,
                'errors'  => [
                    'email' => ['The email address is not registered.'],
                ],
            ];
        }

        // Generate and send OTP
        $otp = $this->generateOtp($user->id);

        // Send password reset OTP email
        $this->sendPasswordResetOtpEmail($user, $otp);

        return true;
    }

    public function resetPassword(int $userId, string $password)
    {
        $user = $this->repository->findById($userId);

        if (!$user) {
            return [
                'error'   => true,
                'message' => 'User not found',
                'code'    => 404,
                'errors'  => [
                    'user' => ['User not found.'],
                ],
            ];
        }

        $this->repository->resetPassword($userId, $password);
        $this->sendPasswordChangedEmail($user);

        return true;
    }

    public function resendOtp(string $email, string $stateCode)
    {
        $user = $this->repository->findByEmail($email);

        if (!$user) {
            throw new \Exception('User not found', 404);
        }

        if ($user->email_verified_at) {
            throw new \Exception('Email already verified', 400);
        }

        // Verify state code
        $cachedStateCode = cache()->get('state_code_' . $user->id);
        if (!$cachedStateCode || $stateCode !== $cachedStateCode) {
            throw new \Exception('Invalid or expired state code', 400);
        }

        // Generate new OTP with new state code
        $otpData = $this->generateOtpWithState($user->id);

        // Send verification email with new OTP
        $this->sendVerificationEmail($user, $otpData['otp']);

        return [
            'state_code' => $otpData['state_code'],
        ];
    }

    public function updateProfile(int $userId, array $data)
    {
        $user = $this->repository->findById($userId);

        if (!$user) {
            throw new \Exception('User not found', 404);
        }
        // Handle avatar upload if provided
        if (isset($data['avatar']) && $data['avatar'] instanceof UploadedFile) {
            // Clear previous avatars
            $user->clearMediaCollection('avatars');
            // Add new avatar
            $user->addMedia($data['avatar'], 'avatars');
            unset($data['avatar']);
        }

        // Update user data
        $this->repository->update($userId, $data);

        return $this->repository->findById($userId);
    }

    protected function generateOtp(int $userId): string
    {
        // Generate a base32 encoded secret
        $secret = $this->google2fa->generateSecretKey();

        // Get the current OTP using the secret
        $otp = $this->google2fa->getCurrentOtp($secret);

        // Store in cache with different key for password reset
        $cacheKey = request()->is('*/forgot-password*')
        ? 'password_reset_' . $userId
        : 'email_verification_' . $userId;

        cache()->put($cacheKey, $otp, now()->addMinutes(10));

        return $otp;
    }

    protected function generateOtpWithState(int $userId): array
    {
        // Generate OTP
        $otp = $this->generateOtp($userId);

        // Generate state code (6-digit random number)
        $stateCode = str_pad((string) random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store state code in cache for 10 minutes
        cache()->put('state_code_' . $userId, $stateCode, now()->addMinutes(10));

        return [
            'otp'        => $otp,
            'state_code' => $stateCode,
        ];
    }

    protected function sendWelcomeEmail($user, $otp)
    {
        SendEmail::dispatch([
            'subject'    => 'Welcome to ' . config('app.name'),
            'to'         => $user->email,
            'email_body' => view('emails.auth.welcome', [
                'details' => [
                    'name' => $user->name,
                    'otp'  => $otp,
                ],
            ])->render(),
        ]);
    }

    protected function sendVerificationEmail($user, $otp)
    {
        SendEmail::dispatch([
            'subject'    => 'Email Verification OTP',
            'to'         => $user->email,
            'email_body' => view('emails.auth.verify-email', [
                'details' => [
                    'name' => $user->name,
                    'otp'  => $otp,
                ],
            ])->render(),
        ]);
    }

    protected function sendPasswordResetOtpEmail($user, $otp)
    {
        SendEmail::dispatch([
            'subject'    => 'Reset Password OTP',
            'to'         => $user->email,
            'email_body' => view('emails.auth.reset-password-otp', [
                'details' => [
                    'name' => $user->name,
                    'otp'  => $otp,
                ],
            ])->render(),
        ]);
    }

    protected function sendPasswordChangedEmail($user)
    {
        SendEmail::dispatch([
            'subject'    => 'Password Changed Successfully',
            'to'         => $user->email,
            'email_body' => view('emails.auth.password-changed', [
                'details' => [
                    'name' => $user->name,
                ],
            ])->render(),
        ]);
    }

    protected function logAuthActivity(string $action, $userId = null, array $metadata = [], string $status = 'success'): void
    {
        app(\App\Repositories\AuthLog\AuthLogRepositoryInterface::class)->create([
            'user_id'    => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'action'     => $action,
            'status'     => $status,
            'metadata'   => $metadata,
        ]);
    }

    protected function validatePasswordStrength(string $password): bool
    {
        $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/';
        return (bool) preg_match($pattern, $password);
    }
}