<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('knowledge_files', function (Blueprint $table) {
            $table->id();

            // File Information
            $table->string('file_name');
            $table->string('original_name');

            // Ownership
            $table->unsignedBigInteger('uploaded_by');
            $table->foreignId('user_agent_id')->nullable()->constrained()->onDelete('cascade');

            // File Details
            $table->enum('type', ['pdf', 'txt', 'doc', 'docx', 'csv', 'json'])->default('txt');
            $table->string('file_path');
            $table->unsignedBigInteger('file_size');
            $table->string('mime_type');

            // Processing Status
            $table->enum('status', ['processing', 'completed', 'failed'])->default('processing');

            // Timestamps
            $table->timestamps();

            // Indexes for performance
            $table->index(['type', 'status']);
            $table->index('uploaded_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('knowledge_files');
    }
};
