<?php

declare (strict_types = 1);

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

trait ApiResponseTrait
{
    /**
     * Return a successful response
     */
    protected function successResponse(
        mixed $data = null,
        string $message = 'Success',
        int $statusCode = 200,
        ?array $meta = null
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
            'code'    => $statusCode,
        ];

        if ($data !== null) {
            $response['data'] = $data instanceof JsonResource || $data instanceof ResourceCollection
            ? $data->resolve()
            : $data;
        }

        if ($meta !== null) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error response
     */
    protected function errorResponse(
        string $message = 'An error occurred',
        int $statusCode = 400,
        ?array $errors = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'code'    => $statusCode,
            'errors'  => $errors ?? [],
        ];

        return response()->json($response, $statusCode);
    }

    /**
     * Return a validation error response
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'Validation failed'
    ): JsonResponse {
        return response()->json([
            'success' => false,
            'message' => $message,
            'code'    => 422,
            'errors'  => $errors,
        ], 422);
    }

    /**
     * Return a not found response
     */
    protected function notFoundResponse(string $message = 'Resource not found'): JsonResponse
    {
        return $this->errorResponse($message, 404, []);
    }

    /**
     * Return an unauthorized response
     */
    protected function unauthorizedResponse(string $message = 'Unauthorized'): JsonResponse
    {
        return $this->errorResponse($message, 401);
    }

    /**
     * Return a forbidden response
     */
    protected function forbiddenResponse(string $message = 'Forbidden'): JsonResponse
    {
        return $this->errorResponse($message, 403, []);
    }

    /**
     * Return a server error response
     */
    protected function serverErrorResponse(string $message = 'Internal server error'): JsonResponse
    {
        return $this->errorResponse($message, 500, []);
    }

    /**
     * Return a created response
     */
    protected function createdResponse(
        mixed $data = null,
        string $message = 'Resource created successfully'
    ): JsonResponse {
        return $this->successResponse($data, $message, 201);
    }

    /**
     * Return a no content response
     */
    protected function noContentResponse(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'No content',
            'code'    => 204,
        ], 204);
    }

    /**
     * Return a paginated response
     */
    protected function paginatedResponse(
        $paginator,
        string $message = 'Success',
        ?string $resourceClass = null
    ): JsonResponse {
        $items = $paginator->items();

        // Transform items if resource class is provided
        if ($resourceClass && class_exists($resourceClass)) {
            $items = $resourceClass::collection($items)->resolve();
        }

        // Calculate next_page
        $nextPage = $paginator->currentPage() < $paginator->lastPage()
        ? $paginator->currentPage() + 1
        : null;

        return response()->json([
            'success' => true,
            'message' => $message,
            'code'    => 200,
            'data'    => [
                'items'    => $items,
                'paginate' => [
                    'current_page' => $paginator->currentPage(),
                    'last_page'    => $paginator->lastPage(),
                    'per_page'     => $paginator->perPage(),
                    'total'        => $paginator->total(),
                    'from'         => $paginator->firstItem(),
                    'to'           => $paginator->lastItem(),
                    'next_page'    => $nextPage,
                ],
            ],
        ]);
    }

    /**
     * Return a response with custom status
     */
    protected function customResponse(
        array $data,
        int $statusCode = 200
    ): JsonResponse {
        if (!isset($data['code'])) {
            $data['code'] = $statusCode;
        }

        return response()->json($data, $statusCode);
    }

    /**
     * Return a response for asynchronous operations
     */
    protected function asyncResponse(
        string $message = 'Request accepted for processing',
        ?array $data = null
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
            'code'    => 202,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, 202);
    }

    /**
     * Return a rate limit exceeded response
     */
    protected function rateLimitResponse(
        string $message = 'Rate limit exceeded',
        ?int $retryAfter = null
    ): JsonResponse {
        $response = response()->json([
            'success' => false,
            'message' => $message,
            'code'    => 429,
            'errors'  => [],
        ], 429);

        if ($retryAfter !== null) {
            $response->header('Retry-After', $retryAfter);
        }

        return $response;
    }
}