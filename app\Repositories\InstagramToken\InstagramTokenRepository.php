<?php

declare (strict_types = 1);

namespace App\Repositories\InstagramToken;

use App\Models\InstagramToken;

final class InstagramTokenRepository implements InstagramTokenRepositoryInterface
{
    public function __construct(
        private readonly InstagramToken $model
    ) {}

    public function findActiveByPageOrIgId(?string $pageId, ?string $igId): ?InstagramToken
    {
        $query = $this->model->where('is_active', true);

        if ($pageId) {
            $query->where(function ($q) use ($pageId) {
                $q->where('page_id', $pageId)
                    ->orWhere('app_scope_id', $pageId);
            });
        }

        if ($igId) {
            $query->orWhere('ig_id', $igId);
        }

        return $query->first();
    }

    public function findAnyActive(): ?InstagramToken
    {
        return $this->model->where('is_active', true)->first();
    }
}
