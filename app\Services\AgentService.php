<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\Agent;
use App\Models\AgentMetric;
use App\Models\UserAgent;
use App\Repositories\Agent\AgentRepositoryInterface;
use App\Repositories\Knowledge\KnowledgeRepositoryInterface;
use App\Repositories\UserAgent\UserAgentRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

final class AgentService
{
    public function __construct(
        private readonly AgentRepositoryInterface $agentRepository,
        private readonly UserAgentRepositoryInterface $userAgentRepository,
        private readonly KnowledgeRepositoryInterface $knowledgeRepository,
        private readonly BusinessEmbeddingService $businessEmbeddingService
    ) {}

    public function getAllAgents(array $filters = []): LengthAwarePaginator
    {
        return $this->agentRepository->getPaginated(
            perPage: $filters['per_page'] ?? 15,
            filters: $filters
        );
    }

    public function getAllAgentsWithoutPagination(array $filters = []): Collection
    {
        return $this->agentRepository->getAllWithFilters($filters);
    }

    public function getAgentById(string $id): ?Agent
    {
        return $this->agentRepository->getWithUserAgentsCount($id);
    }

    public function updateAgentStatus(string $id, bool $isActive, ?string $reason = null): ?Agent
    {
        $agent = $this->agentRepository->findById($id);

        if (!$agent) {
            return null;
        }

        return $this->agentRepository->updateStatus($agent, $isActive);
    }

    public function getAgentMetrics(string $id, ?string $dateFrom = null, ?string $dateTo = null): ?array
    {
        $agent = $this->agentRepository->findById($id);

        if (!$agent) {
            return null;
        }

        $dateFrom = $dateFrom ?? Carbon::now()->subDays(30)->toDateString();
        $dateTo   = $dateTo ?? Carbon::now()->toDateString();

        $userAgentIds = $agent->userAgents()->pluck('id');

        $metrics = AgentMetric::whereIn('user_agent_id', $userAgentIds)
            ->whereBetween('date', [$dateFrom, $dateTo])
            ->orderBy('date')
            ->get();

        $aggregatedMetrics = $metrics->groupBy('date')->map(function ($dayMetrics) {
            return [
                'date'                    => $dayMetrics->first()->date,
                'total_conversations'     => $dayMetrics->sum('total_conversations'),
                'total_messages_sent'     => $dayMetrics->sum('total_messages_sent'),
                'total_messages_received' => $dayMetrics->sum('total_messages_received'),
                'response_rate'           => $dayMetrics->avg('response_rate'),
                'average_response_time'   => $dayMetrics->avg('average_response_time'),
                'tasks_completed'         => $dayMetrics->sum('tasks_completed'),
                'tasks_failed'            => $dayMetrics->sum('tasks_failed'),
                'user_satisfaction_score' => $dayMetrics->avg('user_satisfaction_score'),
                'bookings_made'           => $dayMetrics->sum('bookings_made'),
                'sales_completed'         => $dayMetrics->sum('sales_completed'),
                'leads_generated'         => $dayMetrics->sum('leads_generated'),
            ];
        })->values();

        return [
            'total_user_agents'  => $agent->userAgents()->count(),
            'active_user_agents' => $agent->userAgents()->where('status', 'active')->count(),
            'date_range'         => [
                'from' => $dateFrom,
                'to'   => $dateTo,
            ],
            'metrics'            => $aggregatedMetrics,
        ];
    }

    public function searchAgents(string $query): Collection
    {
        return $this->agentRepository->search($query);
    }

    public function getActiveAgents(): Collection
    {
        return $this->agentRepository->getActive();
    }

    public function subscribeUserToAgent(string $agentId, int $userId, array $data = []): ?UserAgent
    {
        $agent = $this->agentRepository->findById($agentId);

        if (!$agent) {
            return null;
        }

        if (!$agent->is_active) {
            throw new \Exception('This agent is currently not available');
        }

        $existingUserAgent = $this->userAgentRepository->findByUserAndAgent($userId, (int) $agentId);

        if ($existingUserAgent) {
            return $this->userAgentRepository->updateStatus($existingUserAgent, 'active');
        }

        $subscriptionData = [
            'user_id'      => $userId,
            'agent_id'     => (int) $agentId,
            'name'         => $data['name'],
            'status'       => 'active',
            'personality'  => $data['personality'],
            'agent_gender' => $data['agent_gender'],
        ];

        return $this->userAgentRepository->create($subscriptionData);
    }

    public function unsubscribeUserFromAgent(string $agentId, int $userId): bool
    {
        $agent = $this->agentRepository->findById($agentId);

        if (!$agent) {
            return false;
        }

        $userAgent = $this->userAgentRepository->findByUserAndAgent($userId, (int) $agentId);

        if (!$userAgent) {
            return false;
        }

        $this->userAgentRepository->updateStatus($userAgent, 'inactive');
        return true;
    }

    public function isUserSubscribedToAgent(string $agentId, int $userId): bool
    {
        return $this->userAgentRepository->isUserSubscribed($userId, (int) $agentId);
    }

    public function updateUserAgent(int $userId, int $agentId, array $data): UserAgent
    {
        $userAgent = $this->userAgentRepository->findByUserAndAgent($userId, $agentId);

        // dd($userAgent);

        if (!$userAgent) {
            throw new \Exception('User agent subscription not found');
        }

        // Handle JSON fields with proper merging
        $jsonFields = ['customize_configuration', 'prompt_templates', 'company_details', 'agent_business_data', 'notification_settings', 'analytics_settings', 'performance_targets', 'escalation_rules'];

        foreach ($jsonFields as $field) {
            if (isset($data[$field]) && is_array($data[$field])) {
                // Get existing data and merge with new data
                $existingData = $userAgent->{$field} ?? [];
                if (is_string($existingData)) {
                    $existingData = json_decode($existingData, true) ?? [];
                }

                // Merge arrays (new data overwrites existing keys)
                $mergedData   = array_merge($existingData, $data[$field]);
                $data[$field] = json_encode($mergedData);

                Log::debug("Merged JSON field: $field", [
                    'existing' => $existingData,
                    'incoming' => $data[$field],
                    'merged'   => $mergedData,
                ]);
            }
        }

        $updatedUserAgent = $this->userAgentRepository->update($userAgent, $data);

        // Trigger business data embedding if business-related data was updated
        $businessFields         = ['company_details', 'agent_business_data'];
        $hasBusinessDataChanges = collect($businessFields)->some(fn($field) => isset($data[$field]));

        if ($hasBusinessDataChanges) {
            try {
                // Refresh the model to get the latest data
                $updatedUserAgent->refresh();

                Log::info('Triggering business data embedding update', [
                    'user_agent_id'       => $updatedUserAgent->id,
                    'updated_fields'      => array_intersect(array_keys($data), $businessFields),
                    'company_details'     => $updatedUserAgent->company_details,
                    'agent_business_data' => $updatedUserAgent->agent_business_data,
                ]);

                $this->businessEmbeddingService->embedUserAgentBusinessData($updatedUserAgent);

                // Count embeddings after update
                $embeddingCount = $updatedUserAgent->businessEmbeddings()->count();

                Log::info('Business data embeddings updated successfully', [
                    'user_agent_id'   => $updatedUserAgent->id,
                    'updated_fields'  => array_intersect(array_keys($data), $businessFields),
                    'embedding_count' => $embeddingCount,
                ]);
            } catch (\Exception $e) {
                // Log the error but don't fail the update
                Log::error('Failed to update business embeddings', [
                    'user_agent_id' => $updatedUserAgent->id,
                    'error'         => $e->getMessage(),
                    'trace'         => $e->getTraceAsString(),
                ]);
            }
        }

        return $updatedUserAgent;
    }

    /**
     * Upload knowledge files for a user agent
     */
    public function uploadKnowledgeFiles(UserAgent $userAgent, array $files, int $uploadedBy): array
    {
        $uploadedFiles = [];

        foreach ($files as $file) {
            try {
                $uploadedFile    = $this->uploadSingleKnowledgeFile($userAgent, $file, $uploadedBy);
                $uploadedFiles[] = $uploadedFile;
            } catch (\Exception $e) {
                // Log the error but continue with other files
                Log::error('Failed to upload knowledge file', [
                    'user_agent_id' => $userAgent->id,
                    'file_name'     => $file->getClientOriginalName(),
                    'error'         => $e->getMessage(),
                ]);

                $uploadedFiles[] = [
                    'file_name' => $file->getClientOriginalName(),
                    'status'    => 'failed',
                    'error'     => $e->getMessage(),
                ];
            }
        }

        return $uploadedFiles;
    }

    /**
     * Store knowledge files without processing (fast upload)
     */
    public function storeKnowledgeFilesOnly(UserAgent $userAgent, array $files, int $uploadedBy, bool $processImmediately = false): array
    {
        $storedFiles = [];

        foreach ($files as $file) {
            try {
                $storedFile    = $this->storeSingleKnowledgeFileOnly($userAgent, $file, $uploadedBy, $processImmediately);
                $storedFiles[] = $storedFile;
            } catch (\Exception $e) {
                // Log the error but continue with other files
                Log::error('Failed to store knowledge file', [
                    'user_agent_id' => $userAgent->id,
                    'file_name'     => $file->getClientOriginalName(),
                    'error'         => $e->getMessage(),
                ]);

                $storedFiles[] = [
                    'file_name' => $file->getClientOriginalName(),
                    'status'    => 'failed',
                    'error'     => $e->getMessage(),
                ];
            }
        }

        return $storedFiles;
    }

    /**
     * Store a single knowledge file and queue for processing
     */
    private function storeSingleKnowledgeFileOnly(UserAgent $userAgent, UploadedFile $file, int $uploadedBy, bool $processImmediately = false): array
    {
        // Validate file
        $this->validateKnowledgeFile($file);

        // Generate unique filename
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('knowledge', $fileName, 'private');

        // Create file record with 'processing' status
        $knowledgeFile = $this->knowledgeRepository->createFile([
            'file_name'     => $fileName,
            'original_name' => $file->getClientOriginalName(),
            'uploaded_by'   => $uploadedBy,
            'user_agent_id' => $userAgent->id,
            'type'          => $this->getFileType($file),
            'file_path'     => $filePath,
            'file_size'     => $file->getSize(),
            'mime_type'     => $file->getMimeType(),
            'status'        => 'processing',
        ]);

        // Process immediately or queue for background processing
        if ($processImmediately) {
            // Process immediately with timeout protection
            try {
                set_time_limit(120); // 2 minutes max
                app(\App\Services\KnowledgeService::class)->processFile($knowledgeFile);
                Log::info('Knowledge file processed immediately', [
                    'file_id'   => $knowledgeFile->id,
                    'file_name' => $knowledgeFile->original_name,
                ]);
            } catch (\Exception $e) {
                Log::error('Immediate knowledge file processing failed', [
                    'file_id' => $knowledgeFile->id,
                    'error'   => $e->getMessage(),
                ]);
                // Mark as failed
                $knowledgeFile->update(['status' => 'failed']);
            }
        } else {
            // Dispatch job for background processing
            \App\Jobs\ProcessKnowledgeFile::dispatch($knowledgeFile)->onQueue('knowledge');
        }

        Log::info('Knowledge file queued for processing', [
            'file_id'       => $knowledgeFile->id,
            'file_name'     => $knowledgeFile->original_name,
            'user_agent_id' => $userAgent->id,
        ]);

        return [
            'id'          => $knowledgeFile->id,
            'file_name'   => $knowledgeFile->original_name,
            'status'      => $knowledgeFile->status,
            'upload_time' => $knowledgeFile->created_at,
        ];
    }

    /**
     * Upload a single knowledge file
     */
    private function uploadSingleKnowledgeFile(UserAgent $userAgent, UploadedFile $file, int $uploadedBy): array
    {
        // Validate file
        $this->validateKnowledgeFile($file);

        // Generate unique filename
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('knowledge', $fileName, 'private');

        // Create file record
        $knowledgeFile = $this->knowledgeRepository->createFile([
            'file_name'     => $fileName,
            'original_name' => $file->getClientOriginalName(),
            'uploaded_by'   => $uploadedBy,
            'user_agent_id' => $userAgent->id,
            'type'          => $this->getFileType($file),
            'file_path'     => $filePath,
            'file_size'     => $file->getSize(),
            'mime_type'     => $file->getMimeType(),
            'status'        => 'processing',
        ]);

        // Process file asynchronously (in a real app, this would be a queued job)
        try {
            // Increase time limit for knowledge file processing
            set_time_limit(300); // 5 minutes
            app(KnowledgeService::class)->processFile($knowledgeFile);
        } catch (\Exception $e) {
            // Mark as failed if processing fails
            $this->knowledgeRepository->markFileAsFailed($knowledgeFile, $e->getMessage());
            Log::error('Knowledge file processing failed', [
                'user_agent_id' => $userAgent->id,
                'file_name'     => $file->getClientOriginalName(),
                'error'         => $e->getMessage(),
            ]);
        }

        return [
            'id'          => $knowledgeFile->id,
            'file_name'   => $knowledgeFile->original_name,
            'status'      => $knowledgeFile->status,
            'type'        => $knowledgeFile->type,
            'size'        => $this->formatFileSize($knowledgeFile->file_size),
            'uploaded_at' => $knowledgeFile->created_at,
        ];
    }

    /**
     * Validate knowledge file
     */
    private function validateKnowledgeFile(UploadedFile $file): void
    {
        $allowedMimeTypes = [
            'text/plain',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/csv',
            'application/json',
        ];

        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            throw new \Exception('Unsupported file type: ' . $file->getMimeType());
        }

        if ($file->getSize() > 10 * 1024 * 1024) { // 10MB
            throw new \Exception('File size too large. Maximum size is 10MB.');
        }
    }

    /**
     * Get file type from uploaded file
     */
    private function getFileType(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());

        return match ($extension) {
            'pdf' => 'pdf',
            'txt' => 'txt',
            'doc' => 'doc',
            'docx' => 'docx',
            'csv' => 'csv',
            'json' => 'json',
            default => 'txt',
        };
    }

    /**
     * Format file size in human readable format
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

}
