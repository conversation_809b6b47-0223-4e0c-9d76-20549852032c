<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\KnowledgeFile;
use App\Repositories\Knowledge\KnowledgeRepositoryInterface;
use Exception;
use GeminiAPI\Client as GeminiClient;
use GeminiAPI\Resources\ModelName;
use GeminiAPI\Resources\Parts\TextPart;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

final class KnowledgeService
{
    private const CHUNK_SIZE    = 1000; // characters per chunk
    private const CHUNK_OVERLAP = 200; // overlap between chunks

    private ?GeminiClient $geminiClient;
    private bool $useOllama;

    public function __construct(
        private readonly KnowledgeRepositoryInterface $knowledgeRepository,
        private readonly OllamaService $ollamaService
    ) {
        $llmModel = config('services.llm_model', 'gemini');

        // Initialize services based on LLM_MODEL configuration
        $this->initializeServices($llmModel);

        Log::info('KnowledgeService initialized', [
            'llm_model'         => $llmModel,
            'use_ollama'        => $this->useOllama,
            'ollama_available'  => $this->ollamaService->isAvailable(),
            'gemini_configured' => $this->geminiClient !== null,
        ]);
    }

    /**
     * Initialize embedding services based on LLM model configuration
     */
    private function initializeServices(string $llmModel): void
    {
        $llmModel = strtolower($llmModel);

        switch ($llmModel) {
            case 'ollama':
                $this->useOllama    = $this->ollamaService->isAvailable();
                $this->geminiClient = $this->initializeGeminiAsBackup();

                if (!$this->useOllama) {
                    Log::warning('Ollama requested but not available, falling back to Gemini');
                }
                break;

            case 'gemini':
            default:
                $this->useOllama    = false;
                $this->geminiClient = $this->initializeGemini();
                break;
        }
    }

    /**
     * Initialize Gemini client
     */
    private function initializeGemini(): ?GeminiClient
    {
        $geminiApiKey = config('services.gemini.api_key');

        if (!$geminiApiKey) {
            Log::error('Gemini API key not configured. Please set GEMINI_API_KEY in your .env file.');
            return null;
        }

        return new GeminiClient($geminiApiKey);
    }

    /**
     * Initialize Gemini as backup service
     */
    private function initializeGeminiAsBackup(): ?GeminiClient
    {
        $geminiApiKey = config('services.gemini.api_key');

        if (!$geminiApiKey) {
            Log::warning('Gemini API key not configured - no fallback available for Ollama');
            return null;
        }

        return new GeminiClient($geminiApiKey);
    }

    /**
     * Upload and process knowledge file
     */
    public function uploadFile(UploadedFile $file, int $uploadedBy): KnowledgeFile
    {
        try {
            DB::beginTransaction();

            // Validate file
            $this->validateFile($file);

            // Store file
            $fileName = $this->generateUniqueFileName($file);
            $filePath = $file->storeAs('knowledge', $fileName, 'private');

            // Create file record
            $knowledgeFile = $this->knowledgeRepository->createFile([
                'file_name'     => $fileName,
                'original_name' => $file->getClientOriginalName(),
                'uploaded_by'   => $uploadedBy,
                'type'          => $this->getFileType($file),
                'file_path'     => $filePath,
                'file_size'     => $file->getSize(),
                'mime_type'     => $file->getMimeType(),
                'status'        => 'processing',
            ]);

            DB::commit();

            // Process file asynchronously (in a real app, this would be a queued job)
            $this->processFile($knowledgeFile);

            return $knowledgeFile;

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to upload knowledge file', [
                'error'     => $e->getMessage(),
                'file_name' => $file->getClientOriginalName(),
            ]);

            throw $e;
        }
    }

    /**
     * Process uploaded file: extract text, chunk, and embed
     */
    public function processFile(KnowledgeFile $file): void
    {
        try {
            // Check file size limits (50MB max)
            $maxFileSize = 50 * 1024 * 1024; // 50MB in bytes
            if ($file->file_size > $maxFileSize) {
                throw new Exception("File size ({$file->file_size} bytes) exceeds maximum allowed size ({$maxFileSize} bytes)");
            }

            // Set memory limit based on file size
            $memoryLimitMB = max(512, min(2048, ($file->file_size / 1024 / 1024) * 20)); // 20x file size, min 512MB, max 2GB
            ini_set('memory_limit', $memoryLimitMB . 'M');

            Log::info('Starting file processing', [
                'file_id'              => $file->id,
                'file_size'            => $file->file_size,
                'memory_limit'         => $memoryLimitMB . 'M',
                'initial_memory_usage' => memory_get_usage(true),
            ]);

            // Extract text from file
            $text = $this->extractTextFromFile($file);

            // Check extracted text size
            $textLength    = strlen($text);
            $maxTextLength = 5 * 1024 * 1024; // 5MB of text max
            if ($textLength > $maxTextLength) {
                Log::warning('Text content is very large, truncating', [
                    'file_id'         => $file->id,
                    'original_length' => $textLength,
                    'max_length'      => $maxTextLength,
                ]);
                $text = substr($text, 0, $maxTextLength);
            }

            // Split text into chunks
            $chunks = $this->chunkText($text);

            // Process chunks with embeddings
            $this->processChunks($file, $chunks);

            // Mark file as completed
            $this->knowledgeRepository->markFileAsCompleted($file);

            Log::info('Successfully processed knowledge file', [
                'file_id'            => $file->id,
                'chunks_created'     => count($chunks),
                'final_memory_usage' => memory_get_usage(true),
            ]);

        } catch (Exception $e) {
            $this->knowledgeRepository->markFileAsFailed($file, $e->getMessage());

            Log::error('Failed to process knowledge file', [
                'file_id'      => $file->id,
                'error'        => $e->getMessage(),
                'memory_usage' => memory_get_usage(true),
            ]);

            throw $e;
        }
    }

    /**
     * Extract text content from various file types
     */
    private function extractTextFromFile(KnowledgeFile $file): string
    {
        $filePath = Storage::disk('private')->path($file->file_path);

        switch ($file->type) {
            case 'txt':
                return file_get_contents($filePath);

            case 'pdf':
                return $this->extractTextFromPDF($filePath);

            case 'doc':
            case 'docx':
                return $this->extractTextFromWord($filePath);

            default:
                throw new Exception("Unsupported file type: {$file->type}");
        }
    }

    /**
     * Extract text from PDF using smalot/pdfparser
     */
    private function extractTextFromPDF(string $filePath): string
    {
        try {
            $parser = new \Smalot\PdfParser\Parser();
            $pdf    = $parser->parseFile($filePath);
            $text   = $pdf->getText();

            if (empty(trim($text))) {
                throw new Exception('No text content found in PDF');
            }

            return $text;
        } catch (Exception $e) {
            throw new Exception("Failed to extract text from PDF: " . $e->getMessage());
        }
    }

    /**
     * Extract text from Word documents using phpoffice/phpword
     */
    private function extractTextFromWord(string $filePath): string
    {
        try {
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($filePath);
            $text    = '';

            foreach ($phpWord->getSections() as $section) {
                $text .= $this->extractTextFromElements($section->getElements());
            }

            if (empty(trim($text))) {
                throw new Exception('No text content found in Word document');
            }

            return $text;
        } catch (Exception $e) {
            throw new Exception("Failed to extract text from Word document: " . $e->getMessage());
        }
    }

    /**
     * Recursively extract text from Word document elements
     */
    private function extractTextFromElements(array $elements): string
    {
        $text = '';

        foreach ($elements as $element) {
            // Handle different element types
            if ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                foreach ($element->getElements() as $subElement) {
                    if ($subElement instanceof \PhpOffice\PhpWord\Element\Text) {
                        $text .= $subElement->getText() . ' ';
                    }
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\TextBreak) {
                $text .= "\n";
            } elseif (method_exists($element, 'getElements')) {
                // Handle containers like tables, lists, etc.
                $text .= $this->extractTextFromElements($element->getElements());
            } elseif (method_exists($element, 'getText')) {
                // Fallback for other text elements
                $text .= $element->getText() . ' ';
            }
        }

        return $text;
    }

    /**
     * Split text into overlapping chunks
     */
    private function chunkText(string $text): array
    {
        $text = trim($text);
        if (empty($text)) {
            throw new Exception('No text content found in file');
        }

        // Set memory limit for large files
        ini_set('memory_limit', '1G');

        $chunks     = [];
        $textLength = strlen($text);
        $position   = 0;
        $chunkIndex = 0;
        $maxChunks  = 10000; // Safety limit to prevent infinite loops

        Log::info('Starting text chunking', [
            'text_length'   => $textLength,
            'chunk_size'    => self::CHUNK_SIZE,
            'chunk_overlap' => self::CHUNK_OVERLAP,
        ]);

        while ($position < $textLength && $chunkIndex < $maxChunks) {
            $chunkEnd = min($position + self::CHUNK_SIZE, $textLength);

            // Try to break at sentence or paragraph boundary
            if ($chunkEnd < $textLength) {
                $substring   = substr($text, $position, self::CHUNK_SIZE);
                $lastPeriod  = strrpos($substring, '.');
                $lastNewline = strrpos($substring, "\n");

                $breakPoint = max($lastPeriod, $lastNewline);
                // Only use breakpoint if it's in the last 30% of the chunk and gives us reasonable chunk size
                if ($breakPoint !== false && $breakPoint > self::CHUNK_SIZE * 0.7 && $breakPoint < self::CHUNK_SIZE) {
                    $chunkEnd = $position + $breakPoint + 1;
                }
            }

            $chunkLength = $chunkEnd - $position;
            $chunk       = trim(substr($text, $position, $chunkLength));

            if (!empty($chunk)) {
                $chunks[] = [
                    'content'     => $chunk,
                    'chunk_index' => $chunkIndex++,
                    'metadata'    => [
                        'start_position' => $position,
                        'end_position'   => $chunkEnd,
                        'length'         => strlen($chunk),
                    ],
                ];
            }

            // Calculate next position with overlap, but ensure minimum advancement
            $nextPosition = $chunkEnd - self::CHUNK_OVERLAP;

            // Ensure we always advance by at least 100 characters to prevent infinite loops
            $minAdvancement = min(100, self::CHUNK_SIZE / 10);
            if ($nextPosition <= $position + $minAdvancement) {
                $nextPosition = $position + $minAdvancement;
            }

            $position = $nextPosition;

            // Free memory every 100 chunks
            if ($chunkIndex % 100 === 0) {
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                Log::info('Chunking progress', [
                    'chunks_created' => $chunkIndex,
                    'position'       => $position,
                    'memory_usage'   => memory_get_usage(true),
                ]);
            }
        }

        if ($chunkIndex >= $maxChunks) {
            Log::warning('Chunking stopped at safety limit', [
                'max_chunks'  => $maxChunks,
                'text_length' => $textLength,
                'position'    => $position,
            ]);
        }

        Log::info('Text chunking completed', [
            'total_chunks'       => count($chunks),
            'final_memory_usage' => memory_get_usage(true),
        ]);

        return $chunks;
    }

    /**
     * Process chunks: generate embeddings and store
     */
    private function processChunks(KnowledgeFile $file, array $chunks): void
    {
        $totalChunks    = count($chunks);
        $batchSize      = 50; // Process in batches to manage memory
        $processedCount = 0;

        Log::info('Starting chunk processing', [
            'file_id'      => $file->id,
            'total_chunks' => $totalChunks,
            'batch_size'   => $batchSize,
        ]);

        // Process chunks in batches
        for ($i = 0; $i < $totalChunks; $i += $batchSize) {
            $batch = array_slice($chunks, $i, $batchSize);

            foreach ($batch as $chunkData) {
                try {
                    // Generate embedding for chunk
                    $embedding = $this->generateEmbedding($chunkData['content']);

                    // Store chunk with embedding
                    $this->knowledgeRepository->createChunk([
                        'content'           => $chunkData['content'],
                        'embedding'         => $embedding,
                        'knowledge_file_id' => $file->id,
                        'chunk_index'       => $chunkData['chunk_index'],
                        'metadata'          => $chunkData['metadata'],
                    ]);

                    $processedCount++;

                } catch (Exception $e) {
                    Log::warning('Failed to process chunk', [
                        'file_id'     => $file->id,
                        'chunk_index' => $chunkData['chunk_index'],
                        'error'       => $e->getMessage(),
                    ]);

                    // Continue processing other chunks even if one fails
                    continue;
                }
            }

            // Clear memory and log progress after each batch
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            Log::info('Chunk processing batch completed', [
                'file_id'          => $file->id,
                'processed_count'  => $processedCount,
                'total_chunks'     => $totalChunks,
                'memory_usage'     => memory_get_usage(true),
                'progress_percent' => round(($processedCount / $totalChunks) * 100, 2),
            ]);

            // Small delay to prevent overwhelming the system
            usleep(100000); // 0.1 second
        }

        Log::info('Chunk processing completed', [
            'file_id'            => $file->id,
            'processed_count'    => $processedCount,
            'total_chunks'       => $totalChunks,
            'final_memory_usage' => memory_get_usage(true),
        ]);
    }

    /**
     * Generate embedding using Ollama (preferred) or Gemini (fallback)
     * Made public to allow reuse by other services and seeders
     */
    public function generateEmbedding(string $text): array
    {
        // Try Ollama first if enabled and available
        if ($this->useOllama) {
            try {
                return $this->generateOllamaEmbedding($text);
            } catch (Exception $e) {
                Log::warning('Ollama embedding failed, falling back to Gemini', [
                    'error' => $e->getMessage(),
                    'text'  => substr($text, 0, 100),
                ]);
                // Continue to Gemini fallback
            }
        }

        // Use Gemini as fallback or primary
        return $this->generateGeminiEmbedding($text);
    }

    /**
     * Generate embedding using Gemini
     */
    private function generateGeminiEmbedding(string $text): array
    {
        if (!$this->geminiClient) {
            throw new Exception('Gemini API key not configured. Please set GEMINI_API_KEY in your .env file.');
        }

        try {
            $response = $this->geminiClient->embeddingModel(ModelName::TEXT_EMBEDDING_004)
                ->embedContent(new TextPart($text));

            return $response->embedding->values;
        } catch (Exception $e) {
            Log::error('Failed to generate Gemini embedding', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);
            throw new Exception('Failed to generate embedding: ' . $e->getMessage());
        }
    }

    /**
     * Generate embedding using Ollama
     */
    private function generateOllamaEmbedding(string $text): array
    {
        try {
            return $this->ollamaService->generateEmbedding($text);
        } catch (Exception $e) {
            Log::error('Failed to generate Ollama embedding', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);
            throw $e;
        }
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): void
    {
        $allowedTypes = ['pdf', 'txt', 'doc', 'docx'];
        $maxSize      = 10 * 1024 * 1024; // 10MB

        if ($file->getSize() > $maxSize) {
            throw new Exception('File size exceeds maximum allowed size of 10MB');
        }

        $fileType = $this->getFileType($file);
        if (!in_array($fileType, $allowedTypes)) {
            throw new Exception('Unsupported file type. Allowed types: ' . implode(', ', $allowedTypes));
        }
    }

    /**
     * Determine file type from extension
     */
    private function getFileType(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());

        $typeMap = [
            'pdf'  => 'pdf',
            'txt'  => 'txt',
            'doc'  => 'doc',
            'docx' => 'docx',
        ];

        return $typeMap[$extension] ?? 'txt';
    }

    /**
     * Generate unique file name
     */
    private function generateUniqueFileName(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $baseName  = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $timestamp = now()->format('Y-m-d_H-i-s');
        $hash      = substr(md5($baseName . $timestamp), 0, 8);

        return "{$baseName}_{$timestamp}_{$hash}.{$extension}";
    }

    /**
     * Delete knowledge file and all associated chunks
     */
    public function deleteFile(KnowledgeFile $file): bool
    {
        try {
            DB::beginTransaction();

            // Delete file from storage
            Storage::disk('private')->delete($file->file_path);

            // Delete chunks (cascade will handle this, but explicit is better)
            $this->knowledgeRepository->deleteChunksForFile($file->id);

            // Delete file record
            $this->knowledgeRepository->deleteFile($file);

            DB::commit();

            Log::info('Successfully deleted knowledge file', [
                'file_id'   => $file->id,
                'file_name' => $file->original_name,
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete knowledge file', [
                'file_id' => $file->id,
                'error'   => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Search knowledge base for similar content using pgvector
     */
    public function searchKnowledge(string $query, int $limit = 5): array
    {
        try {
            // Generate embedding for query
            $embedding = $this->generateEmbedding($query);

            // Find similar chunks using pgvector
            $similarChunks = $this->knowledgeRepository->findSimilarChunks($embedding, $limit);

            return $similarChunks->map(function ($chunk) {
                return [
                    'content'     => $chunk->content,
                    'file_name'   => $chunk->knowledgeFile->original_name,
                    'distance'    => $chunk->neighbor_distance,
                    'chunk_index' => $chunk->chunk_index,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error('Failed to search knowledge base', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Get knowledge base statistics
     */
    public function getStats(): array
    {
        return $this->knowledgeRepository->getKnowledgeStats();
    }

    /**
     * Search comprehensive context for chatbot including both knowledge files and business data
     */
    public function searchChatbotContext(\App\Models\UserAgent $userAgent, string $query, int $limit = 10): array
    {
        try {
            $allResults = [];

            // Search knowledge chunks (uploaded files)
            $knowledgeResults = $this->searchKnowledgeForUserAgent($userAgent, $query, $limit);
            foreach ($knowledgeResults as $result) {
                $allResults[] = [
                    'content'     => $result['content'],
                    'source'      => 'knowledge_file',
                    'source_name' => $result['file_name'],
                    'distance'    => $result['distance'],
                    'chunk_index' => $result['chunk_index'],
                    'type'        => 'uploaded_document',
                ];
            }

            // Search business embeddings (company/service data)
            $businessEmbeddingService = app(\App\Services\BusinessEmbeddingService::class);
            $businessResults          = $businessEmbeddingService->searchBusinessContext($userAgent, $query, $limit);
            foreach ($businessResults as $result) {
                $allResults[] = [
                    'content'      => $result['content'],
                    'source'       => 'business_data',
                    'source_name'  => $result['content_type'] . '/' . $result['content_key'],
                    'distance'     => $result['distance'],
                    'content_type' => $result['content_type'],
                    'content_key'  => $result['content_key'],
                    'type'         => 'business_information',
                ];
            }

            // Sort all results by distance (most relevant first)
            usort($allResults, function ($a, $b) {
                return $a['distance'] <=> $b['distance'];
            });

            // Return top results
            return array_slice($allResults, 0, $limit);

        } catch (Exception $e) {
            Log::error('Failed to search chatbot context', [
                'user_agent_id' => $userAgent->id,
                'query'         => $query,
                'error'         => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Search knowledge base for a specific user agent using pgvector
     */
    public function searchKnowledgeForUserAgent(\App\Models\UserAgent $userAgent, string $query, int $limit = 5): array
    {
        try {
            // Generate embedding for query
            $embedding = $this->generateEmbedding($query);

            // Find similar chunks for this user agent's knowledge files using pgvector
            $similarChunks = $this->knowledgeRepository->findSimilarChunksForUserAgent($userAgent->id, $embedding, $limit);

            return $similarChunks->map(function ($chunk) {
                return [
                    'content'     => $chunk->content,
                    'file_name'   => $chunk->knowledgeFile->original_name,
                    'distance'    => $chunk->neighbor_distance,
                    'chunk_index' => $chunk->chunk_index,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error('Failed to search knowledge base for user agent', [
                'user_agent_id' => $userAgent->id,
                'query'         => $query,
                'error'         => $e->getMessage(),
            ]);

            return [];
        }
    }
}