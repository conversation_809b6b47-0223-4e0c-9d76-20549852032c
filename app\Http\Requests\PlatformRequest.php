<?php

declare (strict_types = 1);

namespace App\Http\Requests;

use App\Models\Platform;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class PlatformRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $platform   = $this->route('platform');
        $platformId = $platform instanceof Platform ? $platform->id : null;

        return [
            'name'        => ['required', 'string', 'max:255'],
            'key'         => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9_-]+$/',
                Rule::unique('platforms', 'key')->ignore($platformId),
            ],
            'category'    => ['required', 'string', 'in:social_media,communication,reviews,others'],
            'platform'    => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'enabled'     => ['boolean'],
            'webhook_url' => ['nullable', 'url', 'max:500'],
            'auth_url'    => ['nullable', 'url', 'max:500'],
            'priority'    => ['integer', 'min:0', 'max:100'],
            'sort_order'  => ['integer', 'min:0'],
            'config'      => ['nullable', 'array'],
            'credentials' => ['nullable', 'array'],
            'metadata'    => ['nullable', 'array'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required'      => 'Platform name is required.',
            'key.required'       => 'Platform key is required.',
            'key.regex'          => 'Platform key must contain only lowercase letters, numbers, hyphens, and underscores.',
            'key.unique'         => 'Platform key already exists.',
            'category.required'  => 'Platform category is required.',
            'category.in'        => 'Invalid platform category.',
            'webhook_url.url'    => 'Webhook URL must be a valid URL.',
            'auth_url.url'       => 'Auth URL must be a valid URL.',
            'priority.integer'   => 'Priority must be a number.',
            'priority.min'       => 'Priority must be at least 0.',
            'priority.max'       => 'Priority cannot exceed 100.',
            'sort_order.integer' => 'Sort order must be a number.',
            'sort_order.min'     => 'Sort order must be at least 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name'        => 'platform name',
            'key'         => 'platform key',
            'category'    => 'platform category',
            'platform'    => 'platform',
            'description' => 'description',
            'enabled'     => 'enabled status',
            'webhook_url' => 'webhook URL',
            'auth_url'    => 'auth URL',
            'priority'    => 'priority',
            'sort_order'  => 'sort order',
            'config'      => 'configuration',
            'credentials' => 'credentials',
            'metadata'    => 'metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure key is lowercase and properly formatted
        if ($this->has('key')) {
            $this->merge([
                'key' => strtolower(trim($this->key)),
            ]);
        }

        // Ensure category is lowercase
        if ($this->has('category')) {
            $this->merge([
                'category' => strtolower(trim($this->category)),
            ]);
        }

        // Ensure platform is lowercase if provided
        if ($this->has('platform') && $this->platform) {
            $this->merge([
                'platform' => strtolower(trim($this->platform)),
            ]);
        }
    }
}