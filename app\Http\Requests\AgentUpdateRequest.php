<?php

declare (strict_types = 1);

namespace App\Http\Requests;

final class AgentUpdateRequest extends AgentIdRequest
{
    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        parent::prepareForValidation();

        // Convert JSON strings to arrays for validation
        $jsonFields = [
            'customize_configuration',
            'prompt_templates',
            'company_details',
            'agent_business_data',
            'notification_settings',
            'analytics_settings',
            'performance_targets',
            'escalation_rules',
        ];

        $data = $this->all();

        foreach ($jsonFields as $field) {
            if (isset($data[$field]) && is_string($data[$field])) {
                $decoded = json_decode($data[$field], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $data[$field] = $decoded;
                }
            }
        }

        $this->merge($data);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name'                    => 'sometimes|string|max:255',
            'status'                  => 'sometimes|in:active,inactive,maintenance',
            'personality'             => 'sometimes|in:friendly,professional,casual,formal,helpful,enthusiastic',
            'agent_gender'            => 'sometimes|in:male,female',
            'customize_configuration' => 'sometimes|array',
            'prompt_templates'        => 'sometimes|array',
            'company_details'         => 'sometimes|array',
            'agent_business_data'     => 'sometimes|array',
            'notification_settings'   => 'sometimes|array',
            'analytics_settings'      => 'sometimes|array',
            'performance_targets'     => 'sometimes|array',
            'escalation_rules'        => 'sometimes|array',
            'knowledge_files'         => 'sometimes|array',
            'knowledge_files.*'       => 'file|mimes:pdf,txt,doc,docx,csv,json|max:10240', // 10MB max
        ]);
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'name.string'                   => 'The name must be a string.',
            'name.max'                      => 'The name may not be greater than 255 characters.',
            'status.in'                     => 'The status must be one of: active, inactive, maintenance.',
            'personality.in'                => 'The personality must be one of: friendly, professional, casual, formal, helpful, enthusiastic.',
            'agent_gender.in'               => 'The agent gender must be one of: male, female.',
            'customize_configuration.array' => 'The customize configuration must be an array.',
            'prompt_templates.array'        => 'The prompt templates must be an array.',
            'company_details.array'         => 'The company details must be an array.',
            'agent_business_data.array'     => 'The agent business data must be an array.',
            'notification_settings.array'   => 'The notification settings must be an array.',
            'analytics_settings.array'      => 'The analytics settings must be an array.',
            'performance_targets.array'     => 'The performance targets must be an array.',
            'escalation_rules.array'        => 'The escalation rules must be an array.',
            'knowledge_files.array'         => 'The knowledge files must be an array.',
            'knowledge_files.*.file'        => 'Each knowledge file must be a valid file.',
            'knowledge_files.*.mimes'       => 'Each knowledge file must be a file of type: pdf, txt, doc, docx, csv, json.',
            'knowledge_files.*.max'         => 'Each knowledge file may not be greater than 10MB.',
        ]);
    }
}
