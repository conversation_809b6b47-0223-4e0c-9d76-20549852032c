<?php

declare (strict_types = 1);

namespace App\Repositories\BusinessEmbedding;

use App\Models\BusinessEmbedding;
use App\Models\UserAgent;
use App\Repositories\BusinessEmbedding\BusinessEmbeddingRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Pgvector\Laravel\Distance;

final class BusinessEmbeddingRepository implements BusinessEmbeddingRepositoryInterface
{
    /**
     * Create a new business embedding.
     */
    public function create(array $data): BusinessEmbedding
    {
        return BusinessEmbedding::create($data);
    }

    /**
     * Update or create a business embedding.
     */
    public function updateOrCreate(array $attributes, array $values): BusinessEmbedding
    {
        return BusinessEmbedding::updateOrCreate($attributes, $values);
    }

    /**
     * Find business embedding by ID.
     */
    public function findById(int $id): ?BusinessEmbedding
    {
        return BusinessEmbedding::find($id);
    }

    /**
     * Find business embedding by user agent and content details.
     */
    public function findByUserAgentAndContent(
        int $userAgentId,
        string $contentType,
        string $contentKey
    ): ?BusinessEmbedding {
        return BusinessEmbedding::where([
            'user_agent_id' => $userAgentId,
            'content_type'  => $contentType,
            'content_key'   => $contentKey,
        ])->first();
    }

    /**
     * Check if embedding exists with same content hash.
     */
    public function existsWithHash(
        int $userAgentId,
        string $contentType,
        string $contentKey,
        string $contentHash
    ): bool {
        return BusinessEmbedding::where([
            'user_agent_id' => $userAgentId,
            'content_type'  => $contentType,
            'content_key'   => $contentKey,
            'content_hash'  => $contentHash,
        ])->exists();
    }

    /**
     * Get all business embeddings for a user agent.
     */
    public function getByUserAgent(UserAgent $userAgent): Collection
    {
        return BusinessEmbedding::where('user_agent_id', $userAgent->id)->get();
    }

    /**
     * Get business embeddings count for a user agent.
     */
    public function getCountByUserAgent(UserAgent $userAgent): int
    {
        return BusinessEmbedding::where('user_agent_id', $userAgent->id)->count();
    }

    /**
     * Delete all business embeddings for a user agent.
     */
    public function deleteByUserAgent(UserAgent $userAgent): bool
    {
        return BusinessEmbedding::where('user_agent_id', $userAgent->id)->delete() !== false;
    }

    /**
     * Find similar embeddings using vector similarity search.
     */
    public function findSimilar(
        int $userAgentId,
        array $embedding,
        int $limit = 5
    ): Collection {
        return BusinessEmbedding::where('user_agent_id', $userAgentId)
            ->nearestNeighbors('embedding', $embedding, Distance::Cosine)
            ->limit($limit)
            ->get();
    }

    /**
     * Get business embeddings grouped by content type for a user agent.
     */
    public function getGroupedByContentType(UserAgent $userAgent): Collection
    {
        return BusinessEmbedding::where('user_agent_id', $userAgent->id)
            ->selectRaw('content_type, COUNT(*) as count')
            ->groupBy('content_type')
            ->get();
    }

    /**
     * Get total embeddings count.
     */
    public function getTotalCount(): int
    {
        return BusinessEmbedding::count();
    }

    /**
     * Get total user agents with embeddings count.
     */
    public function getTotalUserAgentsCount(): int
    {
        return BusinessEmbedding::distinct('user_agent_id')->count();
    }

    /**
     * Get business embedding statistics.
     */
    public function getStatistics(): array
    {
        $summary = BusinessEmbedding::selectRaw('
            content_type,
            COUNT(*) as count,
            AVG(CHAR_LENGTH(content)) as avg_content_length,
            COUNT(DISTINCT user_agent_id) as unique_user_agents
        ')
            ->groupBy('content_type')
            ->orderBy('count', 'desc')
            ->get();

        $totalEmbeddings = $this->getTotalCount();
        $totalUserAgents = $this->getTotalUserAgentsCount();

        return [
            'total_embeddings'   => $totalEmbeddings,
            'total_user_agents'  => $totalUserAgents,
            'by_content_type'    => $summary->toArray(),
            'avg_per_user_agent' => $totalUserAgents > 0 ? round($totalEmbeddings / $totalUserAgents, 2) : 0,
        ];
    }
}