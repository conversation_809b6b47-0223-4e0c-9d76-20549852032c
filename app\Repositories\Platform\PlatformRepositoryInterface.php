<?php

declare (strict_types = 1);

namespace App\Repositories\Platform;

use App\Models\Platform;
use Illuminate\Database\Eloquent\Collection;

interface PlatformRepositoryInterface
{
    /**
     * Get all platforms.
     */
    public function getAll(): Collection;

    /**
     * Get platforms by category ID.
     */
    public function getByCategoryId(int $categoryId): Collection;

    /**
     * Get platforms by category name.
     */
    public function getByCategoryName(string $categoryName): Collection;

    /**
     * Get active platforms (enabled and connected).
     */
    public function getActive(): Collection;

    /**
     * Get enabled platforms.
     */
    public function getEnabled(): Collection;

    /**
     * Find platform by key.
     */
    public function findByKey(string $key): ?Platform;

    /**
     * Enable a platform.
     */
    public function enable(Platform $platform): bool;

    /**
     * Disable a platform.
     */
    public function disable(Platform $platform): bool;

    /**
     * Get platform statistics.
     */
    public function getStatistics(): array;

    /**
     * Get platforms grouped by category.
     */
    public function getGroupedByCategory(): Collection;

    /**
     * Get enabled platforms grouped by category.
     */
    public function getEnabledGroupedByCategory(): Collection;
}