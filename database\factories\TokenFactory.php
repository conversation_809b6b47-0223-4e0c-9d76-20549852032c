<?php

declare (strict_types = 1);

namespace Database\Factories;

use App\Models\Platform;
use App\Models\Token;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Token>
 */
final class TokenFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Token::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'user_id'        => User::factory(),
            'platform_id'    => Platform::factory(),
            'tokenable_type' => 'App\Models\InstagramToken',
            'tokenable_id'   => 1, // Default value for testing
        ];
    }

}
