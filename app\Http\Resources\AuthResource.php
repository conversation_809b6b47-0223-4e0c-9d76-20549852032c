<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class AuthResource extends JsonResource
{
    protected ?string $token;

    public function __construct($resource, ?string $token = null)
    {
        parent::__construct($resource);
        $this->token = $token;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id'                => $this->id,
            'first_name'        => $this->first_name,
            'last_name'         => $this->last_name,
            'name'              => $this->name, // Accessor from User model
            'email'             => $this->email,
            'phone'             => $this->phone,
            'address'           => $this->address,
            'company'           => $this->company,
            'timezone'          => $this->timezone,
            'about'             => $this->about,
            'avatar'            => $this->avatar, // Accessor from User model
            'email_verified_at' => $this->email_verified_at ? $this->email_verified_at->format('Y-m-d H:i:s') : null,
            'created_at'        => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at'        => $this->updated_at->format('Y-m-d H:i:s'),
        ];

        // Include requires_verification flag if it exists
        if (isset($this->requires_verification)) {
            $data['requires_verification'] = $this->requires_verification;
        }

        if ($this->token) {
            $data['token'] = $this->token;
        }

        return $data;
    }
}