<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\Message;
use App\Repositories\Message\MessageRepositoryInterface;
use App\Services\InstagramService;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

final class ConversationService
{
    public function __construct(
        private readonly MessageRepositoryInterface $messageRepository,
        private readonly InstagramService $instagramService
    ) {
    }

    /**
     * Create a new message record
     */
    public function createMessage(array $messageData): Message
    {
        return $this->messageRepository->create([
            'sender_id'    => $messageData['sender_id'],
            'recipient_id' => $messageData['recipient_id'] ?? null,
            'message_id'   => $messageData['message_id'] ?? null, // Store Instagram mid
            'message'      => $messageData['message'],
            'platform'     => $messageData['platform'] ?? 'instagram',
            'status'       => 'pending',
            'sent_at'      => $this->convertTimestamp($messageData['timestamp'] ?? null),
            'timestamp'    => $this->convertTimestamp($messageData['timestamp'] ?? null), // Webhook timestamp
            'direction'    => 'incoming',
            'meta_data'    => $messageData['meta_data'] ?? [],
        ]);
    }

    /**
     * Convert Instagram timestamp to Carbon instance
     */
    private function convertTimestamp($timestamp): \Carbon\Carbon
    {
        if (!$timestamp) {
            return now();
        }

        // Instagram sends timestamp in milliseconds, convert to seconds
        if (is_numeric($timestamp) && $timestamp > 9999999999) {
            return \Carbon\Carbon::createFromTimestamp($timestamp / 1000);
        }

        // If already in seconds
        if (is_numeric($timestamp)) {
            return \Carbon\Carbon::createFromTimestamp($timestamp);
        }

        // Fallback to current time
        return now();
    }

    /**
     * Get conversation context for maintaining chat history
     */
    public function getConversationContext(string $senderId): array
    {
        $messages = $this->messageRepository->getConversationHistory($senderId, 10);

        $context = [];
        foreach ($messages as $msg) {
            $context[] = [
                'role'    => 'user',
                'content' => $msg->message,
            ];

            if ($msg->reply) {
                $context[] = [
                    'role'    => 'assistant',
                    'content' => $msg->reply,
                ];
            }
        }

        return $context;
    }

    /**
     * Mark message as typing
     */
    public function markAsTyping(Message $message): void
    {
        $this->messageRepository->markAsTyping($message);
    }

    /**
     * Mark message as replied
     */
    public function markAsReplied(Message $message, string $response): Message
    {
        return $this->messageRepository->markAsReplied($message, $response);
    }

    /**
     * Send message to Instagram user
     */
    public function sendMessage(string $recipientId, string $message, ?string $pageId = null): bool
    {
        try {
            // Use InstagramService which handles token management and refresh automatically
            return $this->instagramService->sendMessage($recipientId, $message, $pageId);

        } catch (Exception $e) {
            Log::error('Exception sending message through InstagramService', [
                'recipient_id' => $recipientId,
                'page_id'      => $pageId,
                'error'        => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Rate limiting to prevent spam
     */
    public function checkRateLimit(string $senderId): void
    {
        $key          = "rate_limit:chatbot:{$senderId}";
        $currentCount = Cache::get($key, 0);

        if ($currentCount >= 1) { // 1 message per second
            throw new Exception('Rate limit exceeded');
        }

        Cache::put($key, $currentCount + 1, 1); // 1 second
    }

}