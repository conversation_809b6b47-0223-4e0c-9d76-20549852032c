<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\UserAgent;
use Exception;
use Illuminate\Support\Facades\Log;

final class UserAgentContextService
{
    /**
     * Get user agent for this conversation with enhanced mapping logic and full data loading
     */
    public function getUserAgentForConversation(?string $recipientId): ?UserAgent
    {
        try {
            // Enhanced logic to find the most relevant user agent with all related data

            // First, try to find user agent by recipient_id mapping (Instagram business account)
            if ($recipientId) {
                $userAgent = UserAgent::whereHas('user.instagramTokens.tokenable', function ($query) use ($recipientId) {
                    $query->where('ig_id', $recipientId)
                        ->orWhere('page_id', $recipientId);
                })->where('status', 'active')
                    ->with([
                        'agent.userAgents',
                        'knowledgeFiles.chunks',
                        'businessEmbeddings',
                        'user.instagramTokens.tokenable',
                    ])
                    ->first();

                if ($userAgent) {
                    Log::info('Found user agent by recipient mapping', [
                        'user_agent_id'             => $userAgent->id,
                        'user_agent_name'           => $userAgent->name,
                        'recipient_id'              => $recipientId,
                        'has_business_data'         => !is_null($userAgent->agent_business_data),
                        'has_company_details'       => !is_null($userAgent->company_details),
                        'knowledge_files_count'     => $userAgent->knowledgeFiles->count(),
                        'business_embeddings_count' => $userAgent->businessEmbeddings->count(),
                    ]);
                    return $userAgent;
                }
            }

            // Fallback: Get the most comprehensive active user agent based on data richness
            $authUserId = 1;
            $userAgent  = app(\App\Repositories\UserAgent\UserAgentRepositoryInterface::class)
                ->getActiveUserAgents(userId: $authUserId)
                ->sortByDesc(fn($ua) => ($ua->knowledgeFiles?->count() ?? 0) + ($ua->businessEmbeddings?->count() ?? 0))
                ->first();

            if ($userAgent) {
                Log::info('Found user agent by comprehensive data fallback', [
                    'user_agent_id'             => $userAgent->id,
                    'user_agent_name'           => $userAgent->name,
                    'personality'               => $userAgent->personality,
                    'agent_gender'              => $userAgent->agent_gender,
                    'has_business_data'         => !is_null($userAgent->agent_business_data),
                    'has_company_details'       => !is_null($userAgent->company_details),
                    'knowledge_files_count'     => $userAgent->knowledge_files_count ?? 0,
                    'business_embeddings_count' => $userAgent->business_embeddings_count ?? 0,
                    'has_prompt_templates'      => !is_null($userAgent->prompt_templates),
                    'has_escalation_rules'      => !is_null($userAgent->escalation_rules),
                ]);
                return $userAgent;
            }

            // Third fallback: any active user agent with minimal requirements
            $authUserId = 1;
            $userAgent  = app(\App\Repositories\UserAgent\UserAgentRepositoryInterface::class)
                ->getActiveUserAgents(userId: $authUserId)
                ->sortByDesc('updated_at')
                ->first();

            if ($userAgent) {
                Log::info('Found user agent by minimal requirements fallback', [
                    'user_agent_id'   => $userAgent->id,
                    'user_agent_name' => $userAgent->name,
                ]);
                return $userAgent;
            }

            Log::warning('No active user agent found for conversation', [
                'recipient_id' => $recipientId,
            ]);
            return null;

        } catch (Exception $e) {
            Log::error('Failed to get user agent for conversation', [
                'recipient_id' => $recipientId,
                'error'        => $e->getMessage(),
                'trace'        => $e->getTraceAsString(),
            ]);
            return null;
        }
    }
}