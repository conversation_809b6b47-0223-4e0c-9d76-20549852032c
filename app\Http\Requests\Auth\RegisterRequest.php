<?php

declare (strict_types = 1);

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;

final class RegisterRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name'  => ['required', 'string', 'max:255'],
            'email'      => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone'      => ['sometimes', 'string', 'max:20', 'nullable'],
            'address'    => ['sometimes', 'string', 'nullable'],
            'company'    => ['sometimes', 'string', 'max:255', 'nullable'],
            'timezone'   => ['sometimes', 'string', 'max:255', 'nullable'],
            'about'      => ['sometimes', 'string', 'nullable'],
            'avatar'     => ['sometimes', 'file', 'image', 'max:5120'], // 5MB max
            'password'   => [
                'required',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols(),
            ],
        ];
    }
}