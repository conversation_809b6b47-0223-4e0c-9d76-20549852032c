<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_embeddings', function (Blueprint $table) {
            $table->id();

            // Relationships
            $table->foreignId('user_agent_id')->constrained()->onDelete('cascade');

            // Content identification
            $table->string('content_type'); // 'company_details', 'business_services', 'business_hours', 'policies', etc.
            $table->string('content_key')->nullable(); // specific key within the content (e.g., 'car_wrap', 'contact_info')

            // Content data
            $table->text('content'); // The actual text content that was embedded
            $table->json('metadata')->nullable(); // Additional metadata about the content

            // Content hash for change detection
            $table->string('content_hash'); // MD5 hash of original data to detect changes

            // Vector embedding
            $table->vector('embedding', 768);

            // Timestamps
            $table->timestamps();

            // Indexes
            $table->index(['user_agent_id', 'content_type']);
            $table->index(['content_type', 'content_key']);
            $table->unique(['user_agent_id', 'content_type', 'content_key']); // Prevent duplicates
        });

        // Create vector index for similarity search
        DB::statement('CREATE INDEX business_embeddings_embedding_idx ON business_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_embeddings');
    }
};
