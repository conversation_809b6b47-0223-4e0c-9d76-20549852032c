<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();

            // Customer information
            $table->string('customer_name')->nullable(); // Can be null if not collected
            $table->string('customer_phone')->nullable();
            $table->string('customer_email')->nullable();
            $table->string('customer_address')->nullable(); // Customer's physical address

            // Platform-specific customer information
            $table->string('platform_customer_id')->nullable(); // Instagram user ID, Facebook user ID, etc.
            $table->string('platform_username')->nullable(); // Instagram username, Facebook username, etc.
            $table->string('platform_name')->nullable(); // 'instagram', 'facebook', 'website', etc.

            // Relationships
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade'); // Owner of the appointment
            $table->foreignId('user_agent_id')->constrained('user_agents')->onDelete('cascade');
            $table->foreignId('token_id')->nullable()->constrained('tokens')->onDelete('set null'); // Links to platform token

            // Appointment details
            $table->date('appointment_date');
            $table->time('appointment_time');
            $table->integer('duration_minutes')->default(120); // Service duration

            // Service information
            $table->string('service_name'); // Name of the service
            $table->string('service_key')->nullable(); // Key from business_services
            $table->decimal('service_price', 8, 2)->nullable(); // Price at time of booking
            $table->string('service_category')->nullable(); // Service category

            // Appointment status and metadata
            $table->enum('status', ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('pending');
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Store additional appointment details

            // Booking details
            $table->timestamp('booked_at')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->string('booking_method')->default('chatbot'); // chatbot, manual, api

            $table->timestamps();

            // Indexes for performance
            $table->index(['appointment_date', 'appointment_time']);
            $table->index(['user_id', 'appointment_date']);
            $table->index(['user_agent_id', 'appointment_date']);
            $table->index(['platform_customer_id', 'token_id']);
            $table->index(['status', 'appointment_date']);
            $table->index(['token_id', 'appointment_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
