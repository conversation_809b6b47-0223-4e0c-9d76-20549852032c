<?php

declare (strict_types = 1);

namespace App\Repositories\BusinessEmbedding;

use App\Models\BusinessEmbedding;
use App\Models\UserAgent;
use Illuminate\Database\Eloquent\Collection;

interface BusinessEmbeddingRepositoryInterface
{
    /**
     * Create a new business embedding.
     */
    public function create(array $data): BusinessEmbedding;

    /**
     * Update or create a business embedding.
     */
    public function updateOrCreate(array $attributes, array $values): BusinessEmbedding;

    /**
     * Find business embedding by ID.
     */
    public function findById(int $id): ?BusinessEmbedding;

    /**
     * Find business embedding by user agent and content details.
     */
    public function findByUserAgentAndContent(
        int $userAgentId,
        string $contentType,
        string $contentKey
    ): ?BusinessEmbedding;

    /**
     * Check if embedding exists with same content hash.
     */
    public function existsWithHash(
        int $userAgentId,
        string $contentType,
        string $contentKey,
        string $contentHash
    ): bool;

    /**
     * Get all business embeddings for a user agent.
     */
    public function getByUserAgent(UserAgent $userAgent): Collection;

    /**
     * Get business embeddings count for a user agent.
     */
    public function getCountByUserAgent(UserAgent $userAgent): int;

    /**
     * Delete all business embeddings for a user agent.
     */
    public function deleteByUserAgent(UserAgent $userAgent): bool;

    /**
     * Find similar embeddings using vector similarity search.
     */
    public function findSimilar(
        int $userAgentId,
        array $embedding,
        int $limit = 5
    ): Collection;

    /**
     * Get business embeddings grouped by content type for a user agent.
     */
    public function getGroupedByContentType(UserAgent $userAgent): Collection;

    /**
     * Get total embeddings count.
     */
    public function getTotalCount(): int;

    /**
     * Get total user agents with embeddings count.
     */
    public function getTotalUserAgentsCount(): int;

    /**
     * Get business embedding statistics.
     */
    public function getStatistics(): array;
}