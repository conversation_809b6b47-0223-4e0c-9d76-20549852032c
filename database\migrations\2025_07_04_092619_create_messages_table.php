<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();

            // Message identification and webhook fields
            $table->string('message_id')->nullable()->index();
            $table->string('sender_id')->index();
            $table->string('recipient_id')->nullable();

            // Message content and metadata
            $table->text('message'); // Renamed from 'content' for webhook consistency
            $table->enum('status', ['pending', 'replied', 'typing'])->default('pending');
            $table->text('reply')->nullable();
            $table->json('meta_data')->nullable();

            // Platform and direction information
            $table->string('platform')->default('instagram');
            $table->enum('direction', ['incoming', 'outgoing'])->default('incoming');

            // Timestamps
            $table->timestamp('sent_at');
            $table->timestamp('timestamp')->nullable(); // Webhook timestamp
            $table->timestamps();

            // Indexes for efficient queries
            $table->index(['sender_id', 'created_at']);
            $table->index(['recipient_id']);
            $table->index(['direction']);
            $table->index(['platform', 'status']);
            $table->index(['platform', 'sender_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
