<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\Message;
use App\Services\Chatbot\MessageProcessor;
use Exception;
use Illuminate\Support\Facades\Log;

final class ChatbotService
{
    public function __construct(
        private readonly MessageProcessor $processor
    ) {
        Log::info('ChatbotService initialized');
    }

    /**
     * Process incoming Instagram message and generate AI response
     */
    public function processMessage(array $messageData): Message
    {
        try {
            Log::info('=== CHATBOT: Starting message processing (new pipeline) ===', [
                'sender_id' => $messageData['sender_id'] ?? 'unknown',
                'platform'  => $messageData['platform'] ?? 'unknown',
            ]);

            return $this->processor->process($messageData);

        } catch (Exception $e) {
            Log::error('CHATBOT: Failed to process message', [
                'error'        => $e->getMessage(),
                'message_data' => $messageData,
                'trace'        => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }
    // The following utility methods were intentionally removed from ChatbotService
    // and centralized in MessageProcessor + supporting services to enforce a
    // schema-driven, agent-agnostic pipeline based on the fixed JSON structure.
}
