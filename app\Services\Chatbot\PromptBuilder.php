<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

/**
 * Builds a strictly formatted prompt using the normalized agent profile and conversation context.
 * No hard-coded assumptions about agent type; reads capabilities and business config to tailor rules.
 */
final class PromptBuilder
{
    public function build(array $profile, string $userMessage, array $conversation, array $knowledge, array $businessContext, array $appointmentIntent): string
    {
        $p         = $profile['prompts'] ?? [];
        $config    = $profile['config'] ?? [];
        $company   = $profile['company'] ?? [];
        $business  = $profile['business'] ?? [];
        $agentName = $profile['name'] ?? 'Assistant';
        $tone      = $profile['personality'] ?? 'professional';
        $gender    = $profile['gender'] ?? 'neutral';

        // Start with either provided system prompt or a safe default
        $system = $p['system_prompt'] ?? "You are {$agentName}, a {$tone} " . ($gender !== 'neutral' ? "{$gender} " : '') . "assistant for the company. You help with services, pricing, and scheduling. Keep answers concise for messaging.";

        // Platform instructions and safeguards
        $platform = $p['platform_instructions'] ?? 'Keep responses concise for messaging platforms.';
        $system .= ' ' . $platform;
        $system .= ' Do not include role labels like Customer: or Assistant: in replies.';

        // Config-derived constraints
        if (!empty($config['response_time_target'])) {
            $system .= " Respond promptly within {$config['response_time_target']} seconds when possible.";
        }
        if (!empty($config['booking_confirmation_required'])) {
            $system .= ' Always ask for explicit user confirmation before finalizing bookings.';
        }

        // Customer information collection instructions
        if (!empty($config['customer_info_collection_settings'])) {
            $customerInfo = $config['customer_info_collection_settings'];
            $system .= "\n\nCUSTOMER INFORMATION COLLECTION - CRITICAL RULES:";
            $system .= "\n- NEVER book appointments without collecting ALL required customer information first";
            $system .= "\n- ALWAYS ask for missing customer information before proceeding with booking";
            $system .= "\n- If customer tries to book without providing required info, politely request it first";

            if ($customerInfo['name'] ?? false) {
                $system .= "\n- Collect customer's full name (REQUIRED before booking)";
            }
            if ($customerInfo['phone'] ?? false) {
                $system .= "\n- Collect phone number (REQUIRED before booking)";
            }
            if ($customerInfo['email'] ?? false) {
                $system .= "\n- Collect email address (REQUIRED before booking)";
            }
            if ($customerInfo['address'] ?? false) {
                $system .= "\n- Collect address (REQUIRED before booking)";
            }

            $system .= "\n- Only proceed with appointment booking after ALL required information is collected";
            $system .= "\n- If any required information is missing, ask for it and wait for response";
        }

        // CRITICAL: NO HALLUCINATION RULES
        $system .= "\n\nCRITICAL: NO HALLUCINATION RULES:\n" .
            "- NEVER make up prices, times, or specific details\n" .
            "- ONLY use information explicitly provided by the user or in the business context\n" .
            "- If you don't know a price, say 'I don't have pricing information for that service'\n" .
            "- If you don't know duration, say 'I don't have timing information for that service'\n" .
            "- NEVER fabricate specific dates or times\n" .
            "- ONLY use dates/times explicitly provided by the user\n" .
            "- Ask for preferences if missing\n" .
            "- Do not calculate or guess days\n";

        // Company and business information
        $system .= "\n\nCOMPANY CONTEXT:\n" . $this->summarizeCompany($company);
        if (!empty($businessContext)) {
            $system .= "\nRELEVANT BUSINESS CONTEXT:\n" . $this->listBullets($businessContext, 'content');
        }
        if (!empty($knowledge)) {
            $system .= "\nRELEVANT KNOWLEDGE:\n" . $this->listBullets($knowledge, 'content');
        }

        // Service information vs booking guidance
        $system .= "\n\nSERVICE INFORMATION vs BOOKING GUIDANCE:";
        $system .= "\n- When customers ask about services (what is, tell me about, more details), provide helpful information first";
        $system .= "\n- Only ask for booking details when customers express clear intent to book (want to book, schedule appointment, etc.)";
        $system .= "\n- Let customers learn about services before pushing for appointments";
        $system .= "\n- Natural flow: Information → Interest → Booking Intent → Customer Details";

        // Appointment section if intent
        if (!empty($appointmentIntent['detected']) || (!empty($appointmentIntent['confidence']) && $appointmentIntent['confidence'] >= 0.3)) {
            $system .= "\n\nAPPOINTMENT CONTEXT:\n" .
                ($p['appointment_booking'] ?? 'Assist with scheduling without fabricating dates/times. Request explicit confirmation.');

            // Customer information collection for appointments
            if (!empty($config['customer_info_collection_settings'])) {
                $system .= "\n\nCUSTOMER INFO COLLECTION:";
                $system .= "\n- Be natural and conversational when collecting information";
                $system .= "\n- Ask for missing information when needed for appointment booking";
                $system .= "\n- Only book appointments when you have all required information";
                $system .= "\n- Use the customer's name once you know it to make it personal";
                $system .= "\n- After getting name, ask 'Hi [Name], how can I help?'";
                $system .= "\n- When they express booking intent, ask for preferred date/time";
                $system .= "\n- Only after getting date/time, check if you need phone/email/address";
                $system .= "\n- Ask for missing info naturally: 'I'll need your [phone/email/address] to confirm the booking'";
            }
        }

        // Optional conversation style
        if (!empty($p['conversation_style'])) {
            $system .= "\n\nCONVERSATION STYLE:\n" . $p['conversation_style'];
        }

        $customerRole  = $p['customer_role'] ?? 'Customer';
        $assistantRole = $p['assistant_role'] ?? 'Assistant';
        $full          = $system . "\n\nCONVERSATION:\n";

        foreach ($conversation as $msg) {
            $role = ($msg['role'] ?? 'user') === 'user' ? $customerRole : $assistantRole;
            $full .= "{$role}: " . ($msg['content'] ?? '') . "\n";
        }

        $full .= "{$customerRole}: {$userMessage}\n{$assistantRole}: ";
        return $full;
    }

    private function summarizeCompany(array $company): string
    {
        $parts = [];
        if (!empty($company['name'])) {
            $parts[] = 'Name: ' . $company['name'];
        }
        if (!empty($company['type'])) {
            $parts[] = 'Type: ' . $company['type'];
        }
        if (!empty($company['description'])) {
            $parts[] = 'Description: ' . $company['description'];
        }
        return implode("\n", $parts) . "\n";
    }

    private function listBullets(array $rows, string $key): string
    {
        $out = '';
        foreach ($rows as $r) {
            $value = is_array($r) ? ($r[$key] ?? '') : (string) $r;
            if ($value !== '') {
                $out .= '- ' . $value . "\n";
            }
        }
        return $out;
    }
}
