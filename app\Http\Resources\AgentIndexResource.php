<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AgentIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'            => $this->id,
            'name'          => $this->name,
            'key'           => $this->key,
            'description'   => $this->description,
            'is_active'     => $this->is_active,
            'is_subscribed' => $this->isSubscribedByAuthUser(),
        ];
    }

    /**
     * Check if the authenticated user has subscribed to this agent
     */
    private function isSubscribedByAuthUser(): bool
    {
        $user = auth('sanctum')->user();

        if (!$user) {
            return false;
        }

        return $this->userAgents()
            ->where('user_id', $user->id)->where('status', 'active')
            ->exists();
    }
}