<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Agent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'key',
        'description',
        'default_capabilities',
        'default_configuration',
        'default_prompt_templates',
        'conversation_flows',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'default_capabilities'     => 'json',
        'default_configuration'    => 'json',
        'default_prompt_templates' => 'json',
        'conversation_flows'       => 'json',
        'is_active'                => 'boolean',
    ];

    /**
     * Get the user agents for this agent type.
     */
    public function userAgents(): HasMany
    {
        return $this->hasMany(UserAgent::class);
    }

    /**
     * Scope a query to only include active agents.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include inactive agents.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope a query to filter by agent key.
     */
    public function scopeOfKey($query, $key)
    {
        return $query->where('key', $key);
    }

    // Removed: creation logic moved to repository/service per conventions
}
