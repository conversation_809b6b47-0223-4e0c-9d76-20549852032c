<?php

declare (strict_types = 1);

namespace App\Repositories\UserAgent;

use App\Models\UserAgent;
use Illuminate\Database\Eloquent\Collection;

interface UserAgentRepositoryInterface
{
    /**
     * Find user agent by user ID and agent ID
     */
    public function findByUserAndAgent(int $userId, int $agentId): ?UserAgent;

    /**
     * Find by primary key
     */
    public function findById(int $id): ?UserAgent;

    /**
     * Create from an Agent template
     */
    public function createFromAgent(\App\Models\Agent $agent, \App\Models\User $user, array $attributes = []): UserAgent;

    /**
     * Create user agent subscription
     */
    public function create(array $data): UserAgent;

    /**
     * Update user agent status
     */
    public function updateStatus(UserAgent $userAgent, string $status): UserAgent;

    /**
     * Update user agent with data
     */
    public function update(UserAgent $userAgent, array $data): UserAgent;

    /**
     * Check if user is subscribed to agent
     */
    public function isUserSubscribed(int $userId, int $agentId): bool;

    /**
     * Get user's subscribed agents
     */
    public function getUserSubscriptions(int $userId): Collection;

    /**
     * Get active user agents for a user
     */
    public function getActiveUserAgents(int $userId): Collection;

}