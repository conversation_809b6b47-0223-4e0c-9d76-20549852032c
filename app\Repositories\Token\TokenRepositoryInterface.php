<?php

declare (strict_types = 1);

namespace App\Repositories\Token;

use App\Models\Token;
use Illuminate\Database\Eloquent\Collection;

interface TokenRepositoryInterface
{
    public function create(array $data): Token;

    public function findByPlatformAndUser(int $platformId, int $userId): ?Token;

    public function getByPlatformAndUser(int $platformId, int $userId): Collection;

    /**
     * Return an active token for given user and platform where tokenable is active
     */
    public function getActiveTokenForUserAndPlatform(int $userId, int $platformId): ?Token;
}
