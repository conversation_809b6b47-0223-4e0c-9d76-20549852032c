<h2 id="knowledge-base">Knowledge Base</h2>
<p>
    Kortana AI's Knowledge Base API allows you to upload, manage, and query documents to power your AI assistant with
    domain-specific knowledge.
</p>

<div class="section-description">
    <p>The Knowledge Base supports:</p>
    <ul>
        <li>Document upload and processing</li>
        <li>Text chunking and embedding</li>
        <li>Semantic search</li>
        <li>Question answering based on your documents</li>
    </ul>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/knowledge/upload</span>
    </div>
    <div class="description">Upload a document to the knowledge base</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>multipart/form-data</td>
                <td>Required for file upload</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>file <span class="required">*</span></td>
                <td>file</td>
                <td>Document to upload (PDF, DOCX, TXT, MD)</td>
            </tr>
            <tr>
                <td>name</td>
                <td>string</td>
                <td>Custom name for the document</td>
            </tr>
            <tr>
                <td>description</td>
                <td>string</td>
                <td>Description of the document</td>
            </tr>
            <tr>
                <td>chunk_size</td>
                <td>integer</td>
                <td>Size of text chunks (default: 1000)</td>
            </tr>
            <tr>
                <td>chunk_overlap</td>
                <td>integer</td>
                <td>Overlap between chunks (default: 200)</td>
            </tr>
            <tr>
                <td>tags</td>
                <td>array</td>
                <td>Array of tags to categorize the document</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "File uploaded successfully",
  "code": 201,
  "data": {
    "id": 1,
    "name": "company_policy.pdf",
    "original_name": "policy_document_2023.pdf",
    "description": "Company policy document",
    "file_path": "knowledge/user_1/company_policy.pdf",
    "file_size": 1024000,
    "mime_type": "application/pdf",
    "chunks_count": 15,
    "user_id": 1,
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:30:45"
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Unauthorized (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Unauthenticated",
  "code": 401
}</code></pre>

    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "file": [
      "The file field is required.",
      "The file must be a file of type: pdf, docx, txt, md."
    ],
    "chunk_size": [
      "The chunk size must be between 100 and 2000."
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/knowledge/files</span>
    </div>
    <div class="description">Get a list of uploaded knowledge files</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Query Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>page</td>
                <td>integer</td>
                <td>Page number for pagination</td>
            </tr>
            <tr>
                <td>per_page</td>
                <td>integer</td>
                <td>Number of files per page</td>
            </tr>
            <tr>
                <td>search</td>
                <td>string</td>
                <td>Search term for file name or description</td>
            </tr>
            <tr>
                <td>tags</td>
                <td>string</td>
                <td>Comma-separated list of tags to filter by</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Files retrieved successfully",
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "company_policy.pdf",
      "description": "Company policy document",
      "file_size": 1024000,
      "chunks_count": 15,
      "created_at": "2023-08-15 14:30:45"
    },
    {
      "id": 2,
      "name": "product_manual.pdf",
      "description": "Product user manual",
      "file_size": 2048000,
      "chunks_count": 32,
      "created_at": "2023-08-14 10:15:22"
    }
  ],
  "pagination": {
    "current_page": 1,
    "last_page": 3,
    "per_page": 10,
    "total": 25,
    "from": 1,
    "to": 10
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/knowledge/files/{id}</span>
    </div>
    <div class="description">Get details of a specific knowledge file</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Path Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>id <span class="required">*</span></td>
                <td>integer</td>
                <td>ID of the knowledge file</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "File details retrieved successfully",
  "code": 200,
  "data": {
    "id": 1,
    "name": "company_policy.pdf",
    "original_name": "policy_document_2023.pdf",
    "description": "Company policy document",
    "file_path": "knowledge/user_1/company_policy.pdf",
    "file_size": 1024000,
    "mime_type": "application/pdf",
    "chunks_count": 15,
    "user_id": 1,
    "tags": ["policy", "hr", "guidelines"],
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:30:45",
    "chunks": [
      {
        "id": 1,
        "content": "This is the first chunk of the document...",
        "metadata": {
          "page": 1,
          "position": 0
        }
      },
      {
        "id": 2,
        "content": "This is the second chunk of the document...",
        "metadata": {
          "page": 1,
          "position": 1
        }
      }
    ]
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "File not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method delete">DELETE</span>
        <span class="url">/api/v1/knowledge/files/{id}</span>
    </div>
    <div class="description">Delete a knowledge file</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Path Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>id <span class="required">*</span></td>
                <td>integer</td>
                <td>ID of the knowledge file to delete</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "File deleted successfully",
  "code": 200
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "File not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/knowledge/search</span>
    </div>
    <div class="description">Search the knowledge base using semantic search</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>application/json</td>
                <td>Request body format</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>query <span class="required">*</span></td>
                <td>string</td>
                <td>Search query text</td>
            </tr>
            <tr>
                <td>file_ids</td>
                <td>array</td>
                <td>Array of file IDs to search within (optional)</td>
            </tr>
            <tr>
                <td>limit</td>
                <td>integer</td>
                <td>Maximum number of results to return (default: 5)</td>
            </tr>
            <tr>
                <td>min_score</td>
                <td>float</td>
                <td>Minimum similarity score threshold (0.0-1.0)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Search results retrieved successfully",
  "code": 200,
  "data": {
    "results": [
      {
        "chunk_id": 42,
        "file_id": 1,
        "file_name": "company_policy.pdf",
        "content": "Employees are entitled to 20 days of paid vacation per year...",
        "metadata": {
          "page": 5,
          "position": 3
        },
        "score": 0.92
      },
      {
        "chunk_id": 17,
        "file_id": 1,
        "file_name": "company_policy.pdf",
        "content": "Vacation days can be carried over to the next year with manager approval...",
        "metadata": {
          "page": 5,
          "position": 4
        },
        "score": 0.87
      }
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/knowledge/ask</span>
    </div>
    <div class="description">Ask a question based on your knowledge base</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>application/json</td>
                <td>Request body format</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>question <span class="required">*</span></td>
                <td>string</td>
                <td>Question to ask</td>
            </tr>
            <tr>
                <td>file_ids</td>
                <td>array</td>
                <td>Array of file IDs to search within (optional)</td>
            </tr>
            <tr>
                <td>context_limit</td>
                <td>integer</td>
                <td>Maximum number of context chunks to use (default: 5)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Question answered successfully",
  "code": 200,
  "data": {
    "answer": "Employees are entitled to 20 days of paid vacation per year, which can be carried over to the next year with manager approval.",
    "sources": [
      {
        "chunk_id": 42,
        "file_id": 1,
        "file_name": "company_policy.pdf",
        "content": "Employees are entitled to 20 days of paid vacation per year...",
        "score": 0.92
      },
      {
        "chunk_id": 17,
        "file_id": 1,
        "file_name": "company_policy.pdf",
        "content": "Vacation days can be carried over to the next year with manager approval...",
        "score": 0.87
      }
    ]
  }
}</code></pre>
</div>

<div class="section-notes">
    <h3>Knowledge Base Notes</h3>
    <ul>
        <li>Supported file formats: PDF, DOCX, TXT, MD</li>
        <li>Maximum file size: 10MB</li>
        <li>Text extraction and chunking happen asynchronously</li>
        <li>Semantic search uses vector embeddings for better results</li>
        <li>Question answering combines retrieval-augmented generation (RAG) with LLM</li>
        <li>File storage is secure and isolated per user</li>
    </ul>
</div>
