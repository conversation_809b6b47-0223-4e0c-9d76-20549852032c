<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\UserAgent;

/**
 * Lightweight, schema-aware intent detector that leverages agent business_services
 * and generic booking/sales/support intents without hard-coding a single agent type.
 */
final class IntentDetector
{
    public function detect(string $message, ?UserAgent $userAgent = null): array
    {
        $lower = strtolower(trim($message));

        $intent = [
            'type'              => 'general', // appointment|sales|support|marketing|consultation|general
            'detected'          => false,
            'confidence'        => 0.0,
            'service_mentioned' => false,
            'specific_services' => [],
            'keywords_found'    => [],
            'urgency'           => 'normal',
        ];

        $bookingWords = ['book', 'booking', 'schedule', 'appointment', 'availability', 'available', 'time', 'slot', 'calendar'];
        foreach ($bookingWords as $w) {
            if (str_contains($lower, $w)) {
                $intent['type']     = 'appointment';
                $intent['detected'] = true;
                $intent['confidence'] += 0.2;
                $intent['keywords_found'][] = $w;
            }
        }

        // Urgency
        foreach (['urgent', 'asap', 'today', 'now', 'emergency'] as $u) {
            if (str_contains($lower, $u)) {
                $intent['urgency'] = 'high';
                $intent['confidence'] += 0.2;
                $intent['keywords_found'][] = $u;
            }
        }

        // Service detection via userAgent business_services, flexible for all agents
        if ($userAgent) {
            $business = $this->jsonToArray($userAgent->agent_business_data);
            $services = $business['business_services'] ?? [];
            foreach ($services as $key => $svc) {
                $name     = strtolower((string) ($svc['name'] ?? $key));
                $category = strtolower((string) ($svc['category'] ?? ''));
                $aliases  = array_map(fn($a) => strtolower((string) $a), (array) ($svc['aliases'] ?? []));

                $matches = [$name, $category, ...$aliases];
                foreach ($matches as $m) {
                    if ($m !== '' && str_contains($lower, $m)) {
                        $intent['service_mentioned']   = true;
                        $intent['specific_services'][] = [
                            'key'      => $key,
                            'name'     => $svc['name'] ?? $key,
                            'price'    => $svc['price'] ?? null,
                            'duration' => $svc['duration'] ?? null,
                            'category' => $svc['category'] ?? null,
                        ];
                        $intent['confidence'] += 0.15;
                        break;
                    }
                }
            }
        }

        // If nothing indicates appointment but agent type implies sales/support etc., set generic intent
        if ($intent['confidence'] === 0.0) {
            $intent['detected'] = false;
            $intent['type']     = 'general';
        }

        $intent['confidence'] = min($intent['confidence'], 1.0);
        return $intent;
    }

    private function jsonToArray(mixed $data): array
    {
        if (is_array($data)) {
            return $data;
        }
        if (is_string($data) && $data !== '') {
            $decoded = json_decode($data, true);
            return is_array($decoded) ? $decoded : [];
        }
        return [];
    }
}
