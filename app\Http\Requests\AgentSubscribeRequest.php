<?php

declare (strict_types = 1);

namespace App\Http\Requests;

final class AgentSubscribeRequest extends AgentIdRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name'         => 'required|string|max:255',
            'agent_gender' => 'required|in:male,female,neutral',
            'personality'  => 'required|in:friendly,professional,casual,formal,helpful,enthusiastic',
        ]);
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'name.required'         => 'The agent name is required.',
            'name.string'           => 'The agent name must be a string.',
            'name.max'              => 'The agent name may not be greater than 255 characters.',
            'agent_gender.required' => 'The agent gender is required.',
            'agent_gender.in'       => 'The agent gender must be one of: male, female, neutral.',
            'personality.required'  => 'The personality is required.',
            'personality.in'        => 'The personality must be one of: friendly, professional, casual, formal, helpful, enthusiastic.',
        ]);
    }
}
