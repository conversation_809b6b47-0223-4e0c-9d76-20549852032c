<?php

declare (strict_types = 1);

namespace App\Repositories\SocialMedia;

use App\Models\Message;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

final class SocialMediaRepository implements SocialMediaRepositoryInterface
{
    /**
     * Get messages by platform
     */
    public function getMessagesByPlatform(string $platform, int $perPage = 15): LengthAwarePaginator
    {
        return Message::where('platform', $platform)
            ->orderBy('sent_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get conversation history by platform
     */
    public function getConversationHistoryByPlatform(
        string $senderId,
        string $platform,
        int $limit = 20
    ): Collection {
        return Message::where('sender_id', $senderId)
            ->where('platform', $platform)
            ->orderBy('sent_at', 'desc')
            ->limit($limit)
            ->get()
            ->reverse()
            ->values();
    }

    /**
     * Get platform statistics
     */
    public function getPlatformStats(string $platform): array
    {
        $messages = Message::where('platform', $platform);

        return [
            'total_messages'      => $messages->count(),
            'replied_messages'    => $messages->where('status', 'replied')->count(),
            'pending_messages'    => $messages->where('status', 'pending')->count(),
            'failed_messages'     => $messages->where('status', 'failed')->count(),
            // conversation_id removed
            'unique_senders'      => $messages->distinct('sender_id')->count(),
            'today_messages'      => $messages->whereDate('sent_at', today())->count(),
            'this_week_messages'  => $messages->whereBetween('sent_at', [
                now()->startOfWeek(),
                now()->endOfWeek(),
            ])->count(),
            'this_month_messages' => $messages->whereMonth('sent_at', now()->month)->count(),
        ];
    }

    /**
     * Get active conversations by platform
     */
    public function getActiveConversationsByPlatform(string $platform, int $limit = 10): Collection
    {
        return Message::where('platform', $platform)
            ->where('sent_at', '>=', now()->subDays(7))
            ->select('sender_id')
            ->selectRaw('MAX(sent_at) as last_message_at')
            ->selectRaw('COUNT(*) as message_count')
            ->groupBy('sender_id')
            ->orderBy('last_message_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get pending messages by platform
     */
    public function getPendingMessagesByPlatform(string $platform): Collection
    {
        return Message::where('platform', $platform)
            ->where('status', 'pending')
            ->orderBy('sent_at', 'asc')
            ->get();
    }

    /**
     * Get failed messages by platform
     */
    public function getFailedMessagesByPlatform(string $platform): Collection
    {
        return Message::where('platform', $platform)
            ->where('status', 'failed')
            ->orderBy('sent_at', 'desc')
            ->get();
    }

    /**
     * Get messages by sender on platform
     */
    public function getMessagesBySenderOnPlatform(
        string $senderId,
        string $platform,
        int $perPage = 15
    ): LengthAwarePaginator {
        return Message::where('sender_id', $senderId)
            ->where('platform', $platform)
            ->orderBy('sent_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get conversation participants by platform
     */
    // Deprecated: participants by conversation_id removed

    /**
     * Get platform engagement metrics
     */
    public function getPlatformEngagementMetrics(string $platform): array
    {
        $messages      = Message::where('platform', $platform);
        $totalMessages = $messages->count();

        if ($totalMessages === 0) {
            return [
                'response_rate'         => 0,
                'average_response_time' => 0,
                'engagement_score'      => 0,
            ];
        }

        $repliedMessages = $messages->where('status', 'replied')->count();
        $responseRate    = ($repliedMessages / $totalMessages) * 100;

        // Calculate average response time (in minutes)
        $avgResponseTime = Message::where('platform', $platform)
            ->where('status', 'replied')
            ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, sent_at, updated_at)) as avg_response_time')
            ->value('avg_response_time') ?? 0;

        // Engagement score based on response rate and speed
        $engagementScore = ($responseRate * 0.7) + (max(0, 100 - $avgResponseTime) * 0.3);

        return [
            'response_rate'         => round($responseRate, 2),
            'average_response_time' => round($avgResponseTime, 2),
            'engagement_score'      => round($engagementScore, 2),
        ];
    }

    /**
     * Get hourly message distribution by platform
     */
    public function getHourlyMessageDistribution(string $platform): array
    {
        $distribution = Message::where('platform', $platform)
            ->selectRaw('HOUR(sent_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        // Fill missing hours with 0
        $result = [];
        for ($i = 0; $i < 24; $i++) {
            $result[$i] = $distribution[$i] ?? 0;
        }

        return $result;
    }

    /**
     * Get weekly message trends by platform
     */
    public function getWeeklyMessageTrends(string $platform, int $weeks = 4): array
    {
        $trends    = [];
        $startDate = now()->subWeeks($weeks);

        for ($i = 0; $i < $weeks; $i++) {
            $weekStart = $startDate->copy()->addWeeks($i)->startOfWeek();
            $weekEnd   = $weekStart->copy()->endOfWeek();

            $count = Message::where('platform', $platform)
                ->whereBetween('sent_at', [$weekStart, $weekEnd])
                ->count();

            $trends[$weekStart->format('Y-m-d')] = $count;
        }

        return $trends;
    }
}