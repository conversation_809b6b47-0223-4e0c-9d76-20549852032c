<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AppointmentResource;
use App\Services\AppointmentService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

final class AppointmentsController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly AppointmentService $appointmentService
    ) {
    }

    /**
     * Universal GET endpoint for all appointment queries
     * Handles: list, search, filter by status/platform/date, stats, calendar
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status'   => 'sometimes|string|in:pending,confirmed,in_progress,completed,cancelled,no_show',
                'date'     => 'sometimes|date_format:Y-m-d',
                'search'   => 'sometimes|string|max:255',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'page'     => 'sometimes|integer|min:1',
            ]);

            $options = [
                'user_only' => true,
                'per_page'  => (int) ($validated['per_page'] ?? 15),
            ];

            foreach (['status', 'date', 'search'] as $key) {
                if (isset($validated[$key]) && $validated[$key] !== '') {
                    $options[$key] = $validated[$key];
                }
            }

            // dd($options);

            $result = $this->appointmentService->queryAppointments($options);

            // dd($result->toArray());

            return $this->paginatedResponse($result, 'Appointments retrieved successfully', AppointmentResource::class);

        } catch (\Exception $e) {
            Log::error('Appointments query failed', [
                'error'   => $e->getMessage(),
                'params'  => $request->all(),
                'user_id' => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to retrieve appointments', 500);
        }
    }

    /**
     * Create appointment
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'customer_name'        => 'nullable|string|max:255',
            'customer_phone'       => 'nullable|string|max:20',
            'customer_email'       => 'nullable|email|max:255',
            'customer_address'     => 'nullable|string|max:500',
            'platform_customer_id' => 'nullable|string|max:255',
            'platform_username'    => 'nullable|string|max:255',
            'platform_name'        => 'nullable|string|max:100',
            'user_agent_id'        => 'required|integer|exists:user_agents,id',
            'token_id'             => 'nullable|integer|exists:tokens,id',
            'appointment_date'     => 'required|date|after_or_equal:today',
            'appointment_time'     => 'required|date_format:H:i',
            'duration_minutes'     => 'sometimes|integer|min:15|max:480',
            'service_name'         => 'required|string|max:255',
            'service_key'          => 'nullable|string|max:255',
            'service_price'        => 'nullable|numeric|min:0|max:99999.99',
            'service_category'     => 'nullable|string|max:255',
            'notes'                => 'nullable|string|max:1000',
            'metadata'             => 'nullable|array',
        ]);

        try {

            // Remove null token_id to avoid foreign key constraint issues
            if (isset($validated['token_id']) && is_null($validated['token_id'])) {
                unset($validated['token_id']);
            }

            $appointment = $this->appointmentService->createAppointment($validated);

            return $this->successResponse(
                new AppointmentResource($appointment),
                'Appointment created successfully',
                201
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            Log::error('Appointment creation failed', [
                'error'   => $e->getMessage(),
                'data'    => $validated ?? [],
                'user_id' => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to create appointment', 500);
        }
    }

    /**
     * Show specific appointment
     */
    public function show(int $id): JsonResponse
    {
        try {
            $appointment = $this->appointmentService->findAppointment($id, true);

            if (!$appointment) {
                return $this->errorResponse('Appointment not found', 404);
            }

            return $this->successResponse(
                new AppointmentResource($appointment),
                'Appointment retrieved successfully'
            );

        } catch (\Exception $e) {
            Log::error('Appointment retrieval failed', [
                'error'          => $e->getMessage(),
                'appointment_id' => $id,
                'user_id'        => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to retrieve appointment', 500);
        }
    }

    /**
     * Update appointment
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'customer_name'        => 'sometimes|string|max:255',
                'customer_phone'       => 'nullable|string|max:20',
                'customer_email'       => 'nullable|email|max:255',
                'customer_address'     => 'nullable|string|max:500',
                'platform_customer_id' => 'nullable|string|max:255',
                'platform_username'    => 'nullable|string|max:255',
                'platform_name'        => 'nullable|string|max:100',
                'appointment_date'     => 'sometimes|date|after_or_equal:today',
                'appointment_time'     => 'sometimes|date_format:H:i',
                'duration_minutes'     => 'sometimes|integer|min:15|max:480',
                'service_name'         => 'sometimes|string|max:255',
                'service_key'          => 'nullable|string|max:255',
                'service_price'        => 'nullable|numeric|min:0|max:99999.99',
                'service_category'     => 'nullable|string|max:255',
                'notes'                => 'nullable|string|max:1000',
                'metadata'             => 'nullable|array',
            ]);

            $success = $this->appointmentService->updateAppointment($id, $validated, true);

            if (!$success) {
                return $this->errorResponse('Appointment not found or update failed', 404);
            }

            $appointment = $this->appointmentService->findAppointment($id, true);

            return $this->successResponse(
                new AppointmentResource($appointment),
                'Appointment updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            Log::error('Appointment update failed', [
                'error'          => $e->getMessage(),
                'appointment_id' => $id,
                'data'           => $validated ?? [],
                'user_id'        => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to update appointment', 500);
        }
    }

    /**
     * Universal status change endpoint
     * Handles: confirm, cancel, complete, no-show, custom status
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $validated = $request->validate([
            'status' => [
                'required',
                'string',
                Rule::in(['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show']),
            ],
            'notes'  => 'sometimes|string|max:500',
        ]);

        try {

            $success = $this->appointmentService->updateAppointmentStatus(
                $id,
                $validated['status'],
                true,
                $validated['notes'] ?? null
            );

            if (!$success) {
                return $this->errorResponse('Appointment not found or status update failed', 404);
            }

            $appointment = $this->appointmentService->findAppointment($id, true);

            return $this->successResponse(
                new AppointmentResource($appointment),
                "Appointment status changed to '{$validated['status']}' successfully"
            );

        } catch (\Exception $e) {
            Log::error('Status update failed', [
                'error'          => $e->getMessage(),
                'appointment_id' => $id,
                'status'         => $validated['status'] ?? null,
                'user_id'        => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to update appointment status', 500);
        }
    }

    /**
     * Delete appointment
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $success = $this->appointmentService->deleteAppointment($id, true);

            if (!$success) {
                return $this->errorResponse('Appointment not found or deletion failed', 404);
            }

            return $this->successResponse(null, 'Appointment deleted successfully');

        } catch (\Exception $e) {
            Log::error('Appointment deletion failed', [
                'error'          => $e->getMessage(),
                'appointment_id' => $id,
                'user_id'        => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to delete appointment', 500);
        }
    }

    /**
     * Get available statuses
     */
    public function statuses(): JsonResponse
    {
        try {
            $statuses = $this->appointmentService->getAvailableStatuses();

            return $this->successResponse(
                $statuses,
                'Available statuses retrieved successfully'
            );

        } catch (\Exception $e) {
            Log::error('Failed to get statuses', [
                'error'   => $e->getMessage(),
                'user_id' => Auth::id() ?? 0,
            ]);

            return $this->errorResponse('Failed to retrieve available statuses', 500);
        }
    }

    // Simplified; removed advanced helper methods
}
