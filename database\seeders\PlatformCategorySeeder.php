<?php

declare (strict_types = 1);

namespace Database\Seeders;

use App\Models\PlatformCategory;
use Illuminate\Database\Seeder;

final class PlatformCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name'        => 'social_media',
                'description' => 'Social media platforms for messaging, comments, and content management',
            ],
            [
                'name'        => 'communication',
                'description' => 'Messaging and communication platforms for customer support',
            ],
            [
                'name'        => 'reviews',
                'description' => 'Review and rating platforms for customer feedback management',
            ],
            [
                'name'        => 'others',
                'description' => 'Other platform services and platforms',
            ],
        ];

        foreach ($categories as $categoryData) {
            PlatformCategory::updateOrCreate(
                ['name' => $categoryData['name']],
                $categoryData
            );
        }

        $this->command->info('Platform categories seeded successfully!');
    }
}