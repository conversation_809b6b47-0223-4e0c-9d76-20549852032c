<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\Message;
use App\Repositories\Message\MessageRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Orchestrates the new chatflow: persist message, build context via schema, detect intent,
 * construct prompt, generate response, optionally book appointments via existing service,
 * and persist/send the reply. Keeps the controller thin and the ChatbotService focused.
 */
final class MessageProcessor
{
    public function __construct(
        private readonly MessageRepositoryInterface $messageRepository,
        private readonly UserAgentContextService $userAgentContext,
        private readonly KnowledgeSearchService $knowledgeSearch,
        private readonly AppointmentBookingService $appointmentBooking,
        private readonly ChatbotAIService $ai,
        private readonly PromptBuilder $promptBuilder,
        private readonly IntentDetector $intentDetector,
        private readonly ConversationService $conversationService,
    ) {}

    /**
     * Entry-point for processing a single inbound message.
     */
    public function process(array $messageData): Message
    {
        // Rate limit
        $this->conversationService->checkRateLimit((string) ($messageData['sender_id'] ?? ''));

        // Persist incoming message
        $message = $this->conversationService->createMessage($messageData);

        // Agent resolution (recipient is page/ig id)
        $recipientId = $messageData['recipient_id'] ?? null;
        $userAgent   = $this->userAgentContext->getUserAgentForConversation($recipientId);

        if (!$userAgent) {
            $fallback = "I apologize, I'm currently unavailable. Please try again later.";
            $this->conversationService->markAsTyping($message);
            $this->conversationService->sendMessage($message->sender_id, $fallback, $recipientId);
            return $this->conversationService->markAsReplied($message, $fallback);
        }

        // Build schema-driven runtime profile
        $profile = (new AgentProfileService())->buildProfile($userAgent);

        // Conversation context
        $history = $this->conversationService->getConversationContext($message->sender_id);

        // Detect greeting early
        if ($this->isGreeting($message->message)) {
            // Check if we need to ask for customer info in greeting
            $customizeConfig = is_string($userAgent->customize_configuration)
            ? json_decode($userAgent->customize_configuration, true)
            : ($userAgent->customize_configuration ?? []);

            $customerInfoSettings = $customizeConfig['customer_info_collection_settings'] ?? [];

            if (($customerInfoSettings['name'] ?? false) && count($history) <= 1) {
                // Creative greeting that asks for name
                $greetings = [
                    "Hi! I'm {$profile['name']}, can I know your name? 😊",
                    "Hello there! I'm {$profile['name']}, what should I call you?",
                    "Hi! I'm {$profile['name']}, nice to meet you! What's your name?",
                    "Hey! I'm {$profile['name']}, and you are...?",
                    "Welcome! I'm {$profile['name']}, mind sharing your name?",
                ];
                $reply = $greetings[array_rand($greetings)];
            } else {
                $reply = $profile['prompts']['greeting'] ?? "Hi! I'm {$profile['name']}. How can I help you today?";
            }

            $this->conversationService->markAsTyping($message);
            $this->conversationService->sendMessage($message->sender_id, $reply, $recipientId);
            return $this->conversationService->markAsReplied($message, $reply);
        }

        // Escalation rules (lightweight inline check since method was removed)
        if ($this->shouldEscalate($userAgent, $message->message, $history)) {
            $reply = $profile['prompts']['escalation'] ?? 'Let me connect you with a human agent for further assistance.';
            $this->conversationService->markAsTyping($message);
            $this->conversationService->sendMessage($message->sender_id, $reply, $recipientId);
            return $this->conversationService->markAsReplied($message, $reply);
        }

        // Intent (AI-driven appointment detection) + knowledge/business context
        // Prefer AI/contextual detector to allow booking decisions without keyword gating
        $intent          = $this->appointmentBooking->detectAppointmentIntent($message->message, $userAgent, $message->sender_id);
        $knowledge       = $this->knowledgeSearch->findRelevantKnowledge($message->message, $userAgent);
        $businessContext = $this->knowledgeSearch->findRelevantBusinessContext($message->message, $userAgent);

        // Prompt and AI
        $prompt = $this->promptBuilder->build($profile, $message->message, $history, $knowledge, $businessContext, $intent);
        // Log the exact prompt at the moment we connect to the LLM
        try {
            Log::info('CHATBOT LLM PROMPT (MessageProcessor)', [
                'sender_id'      => $message->sender_id,
                'recipient_id'   => $recipientId,
                'user_agent_id'  => $userAgent->id ?? null,
                'prompt_length'  => strlen($prompt),
                'prompt_preview' => substr($prompt, 0, 500),
                'prompt'         => $prompt,
            ]);
        } catch (\Throwable $e) {
            // do not block processing if logging fails
        }
        $aiReply = $this->ai->generateResponse($prompt, $history);

        // Appointment handling if relevant (using existing robust implementation)
        // Only trigger when there's clear booking intent, not just service mentions
        if (($intent['detected'] ?? false)
            || ($intent['ready_to_book'] ?? false)
            || (($intent['service_mentioned'] ?? false) && ($intent['confidence'] ?? 0) >= 0.3)
        ) {
            try {
                $aiReply = $this->appointmentBooking->handleAppointmentBooking($message, $aiReply, $intent, $userAgent);
            } catch (Exception $e) {
                Log::warning('Appointment flow failed, using AI reply', ['error' => $e->getMessage()]);
            }
        }

        // Send + persist reply
        $this->conversationService->markAsTyping($message);
        $this->conversationService->sendMessage($message->sender_id, $aiReply, $recipientId);
        return $this->conversationService->markAsReplied($message, $aiReply);
    }

    private function isGreeting(string $text): bool
    {
        $patterns = [
            '/^hi\b/i', '/^hello\b/i', '/^hey\b/i', '/^good morning\b/i', '/^good afternoon\b/i', '/^good evening\b/i',
        ];
        $text = trim($text);
        foreach ($patterns as $p) {
            if (preg_match($p, $text)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Simple escalation check replacing removed UserAgentContextService::checkEscalationRules
     */
    private function shouldEscalate( ? \App\Models\UserAgent $userAgent, string $userMessage, array $conversationHistory) : bool
    {
        if (!$userAgent) {
            return false;
        }
        try {
            $rulesRaw = $userAgent->escalation_rules;
            $rules    = [];
            if (is_string($rulesRaw) && $rulesRaw !== '') {
                $decoded = json_decode($rulesRaw, true);
                $rules   = is_array($decoded) ? $decoded : [];
            } elseif (is_array($rulesRaw)) {
                $rules = $rulesRaw;
            }

            $keywords = array_map('strtolower', (array) ($rules['escalate_on_keywords'] ?? []));
            if (!empty($keywords)) {
                $lower = strtolower($userMessage);
                foreach ($keywords as $kw) {
                    if ($kw !== '' && strpos($lower, $kw) !== false) {
                        return true;
                    }
                }
            }

            // Optional future: time-based escalation using conversation timestamps
        } catch (\Throwable $e) {
            // Non-fatal
        }
        return false;
    }
}
