<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_agents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('agent_id')->constrained()->onDelete('cascade');

            // Basic Agent Configuration (Universal for all agents)
            $table->string('name')->nullable(); // Custom name given by user to their agent
            $table->string('status')->default('inactive'); // active, inactive, maintenance
            $table->string('personality')->nullable(); // friendly, professional, casual, formal, helpful, enthusiastic
            $table->string('agent_gender')->default('female'); // male, female, neutral

            // Core Agent Settings (Universal for all agents)
            $table->json('customize_configuration')->nullable(); // User-specific configuration (agent-type specific settings go here)
            $table->json('prompt_templates')->nullable(); // User-customized templates
            // Removed training_data_sources - using knowledge_files/chunks system instead

            // Universal Business Information
            $table->json('company_details')->nullable(); // Company name, type, description, contact, social_media, business_hours, location
            $table->json('agent_business_data')->nullable(); // Agent-specific business data like services, specialties, hours, etc.

            // Universal Features (applicable to all agent types)
            $table->json('notification_settings')->nullable(); // Notification preferences and rules
            $table->json('analytics_settings')->nullable(); // Analytics and reporting preferences
            $table->json('performance_targets')->nullable(); // Performance goals and KPIs
            $table->json('escalation_rules')->nullable(); // When to escalate to human

            $table->timestamps();
            $table->softDeletes(); // Add soft delete capability

            // Indexes for better performance
            $table->index(['user_id', 'status']);
            $table->index(['agent_id', 'status']);
            $table->unique(['user_id', 'agent_id']); // One subscription per agent per user
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_agents');
    }
};
