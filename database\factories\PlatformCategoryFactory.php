<?php

declare (strict_types = 1);

namespace Database\Factories;

use App\Models\PlatformCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PlatformCategory>
 */
final class PlatformCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = PlatformCategory::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $categories = ['social_media', 'communication', 'reviews', 'others'];

        return [
            'name'        => $this->faker->unique()->randomElement($categories),
            'description' => $this->faker->sentence(),
        ];
    }
}
