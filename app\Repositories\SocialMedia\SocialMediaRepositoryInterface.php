<?php

declare (strict_types = 1);

namespace App\Repositories\SocialMedia;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface SocialMediaRepositoryInterface
{
    /**
     * Get messages by platform
     */
    public function getMessagesByPlatform(string $platform, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get conversation history by platform
     */
    public function getConversationHistoryByPlatform(
        string $senderId,
        string $platform,
        int $limit = 20
    ): Collection;

    /**
     * Get platform statistics
     */
    public function getPlatformStats(string $platform): array;

    /**
     * Get active conversations by platform
     */
    public function getActiveConversationsByPlatform(string $platform, int $limit = 10): Collection;

    /**
     * Get pending messages by platform
     */
    public function getPendingMessagesByPlatform(string $platform): Collection;

    /**
     * Get failed messages by platform
     */
    public function getFailedMessagesByPlatform(string $platform): Collection;

    /**
     * Get messages by sender on platform
     */
    public function getMessagesBySenderOnPlatform(
        string $senderId,
        string $platform,
        int $perPage = 15
    ): LengthAwarePaginator;

    // Deprecated: participants by conversation_id removed

    /**
     * Get platform engagement metrics
     */
    public function getPlatformEngagementMetrics(string $platform): array;

    /**
     * Get hourly message distribution by platform
     */
    public function getHourlyMessageDistribution(string $platform): array;

    /**
     * Get weekly message trends by platform
     */
    public function getWeeklyMessageTrends(string $platform, int $weeks = 4): array;
}