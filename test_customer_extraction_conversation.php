<?php

// Test customer information extraction from conversation history

function extractCustomerNameFromMessage(string $message): ?string
{
    $patterns = [
        '/(?:my name is|i\'m|i am|name is|call me)\s+([a-zA-Z\s]{2,50}?)(?:\s+and|\.|\s*$)/i',
        '/name:\s*([a-zA-Z\s]{2,50})(?:\s+and|,|\.|$)/i',
        '/^([a-zA-Z]{2,15}(?:\s+[a-zA-Z]{2,15}){1,3})(?:\s+and|\s+phone|\s+number|\s+\d|,|$)/i',
        '/\b([a-zA-Z]{2,15}\s+[a-zA-Z]{2,15}(?:\s+[a-zA-Z]{2,15})?)\b/i',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $message, $matches)) {
            $name = trim($matches[1]);
            if (preg_match('/^[a-zA-Z]{2,15}(\s+[a-zA-Z]{2,15}){1,3}$/', $name)) {
                $lowerName = strtolower($name);
                $falsePositives = ['phone number', 'email address', 'full vehicle', 'vehicle wrap', 'august th'];
                if (!in_array($lowerName, $falsePositives)) {
                    return ucwords(strtolower($name));
                }
            }
        }
    }
    return null;
}

function extractCustomerPhoneFromMessage(string $message): ?string
{
    $patterns = [
        '/(?:phone|mobile|cell|tel|call|text|sms)\s*(?:number|#|:)?\s*([+]?[0-9]{1,4}?[-.\s]?[0-9]{8,15})/i',
        '/\b([0-9]{11,15})\b/',
        '/\b([0-9]{10})\b/',
        '/([+][0-9]{1,4}[-.\s]?[0-9]{8,15})/',
        '/([+]?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $message, $matches)) {
            $phone = preg_replace('/[^0-9+]/', '', $matches[1]);
            if (strlen($phone) >= 10 && strlen($phone) <= 15) {
                return $phone;
            }
        }
    }
    return null;
}

function extractCustomerEmailFromMessage(string $message): ?string
{
    if (preg_match('/(?:email|e-mail|mail)\s*(?:address|:)?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i', $message, $matches)) {
        return strtolower(trim($matches[1]));
    }
    if (preg_match('/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/', $message, $matches)) {
        return strtolower(trim($matches[1]));
    }
    return null;
}

// Simulate the conversation history from the actual conversation
$conversationHistory = [
    ['role' => 'assistant', 'content' => 'Hi! I\'m Sarah - Booking Assistant, can I know your name? 😊'],
    ['role' => 'user', 'content' => 'I am touhid.. I want to about your vehicle wrapping service'],
    ['role' => 'assistant', 'content' => 'We offer full and partial wraps, decals, and lettering. Wraps use cast vinyl for curves, calendared vinyl for flat surfaces, and specialty finishes. A full wrap takes 3-5 days to install.\n\nWant to know more, or would you like to book an appointment?'],
    ['role' => 'user', 'content' => 'Costing?'],
    ['role' => 'assistant', 'content' => 'Full Vehicle Wraps are $2,500.\n\nReady to book an appointment? I\'ll need your full name, phone number, and email address to schedule.'],
    ['role' => 'user', 'content' => 'Name Touhidun,  phone 01723604959, email <EMAIL>'],
    ['role' => 'assistant', 'content' => 'Thanks Touhidun! To confirm, you\'d like to book a Full Vehicle Wrap for $2,500. Can you confirm what day you would like to bring your car in?'],
    ['role' => 'user', 'content' => '20 th aug after lunch'],
    ['role' => 'assistant', 'content' => 'Okay, August 20th after lunch. And just to confirm, the service you\'re looking to book is a Full Vehicle Wrap for $2,500.\n\nIs that correct?'],
    ['role' => 'user', 'content' => 'Correct'],
];

// Simulate the current message (the final "Correct")
$currentMessage = 'Correct';

echo "Testing customer information extraction from conversation:\n\n";

// Extract from current message first
$customerName = extractCustomerNameFromMessage($currentMessage);
$customerPhone = extractCustomerPhoneFromMessage($currentMessage);
$customerEmail = extractCustomerEmailFromMessage($currentMessage);

echo "From current message ('$currentMessage'):\n";
echo "Name: " . ($customerName ?: 'NULL') . "\n";
echo "Phone: " . ($customerPhone ?: 'NULL') . "\n";
echo "Email: " . ($customerEmail ?: 'NULL') . "\n";
echo "---\n";

// Check conversation history for missing information
foreach (array_reverse($conversationHistory) as $historyMessage) {
    if (($historyMessage['role'] ?? '') === 'user') {
        if (empty($customerName)) {
            $customerName = extractCustomerNameFromMessage($historyMessage['content'] ?? '');
        }
        if (empty($customerPhone)) {
            $customerPhone = extractCustomerPhoneFromMessage($historyMessage['content'] ?? '');
        }
        if (empty($customerEmail)) {
            $customerEmail = extractCustomerEmailFromMessage($historyMessage['content'] ?? '');
        }
    }
}

echo "After checking conversation history:\n";
echo "Name: " . ($customerName ?: 'NULL') . "\n";
echo "Phone: " . ($customerPhone ?: 'NULL') . "\n";
echo "Email: " . ($customerEmail ?: 'NULL') . "\n";
echo "---\n";

// Test individual messages that should contain customer info
echo "Testing individual messages:\n";
$testMessage = 'Name Touhidun,  phone 01723604959, email <EMAIL>';
echo "Message: '$testMessage'\n";
echo "Name: " . (extractCustomerNameFromMessage($testMessage) ?: 'NULL') . "\n";
echo "Phone: " . (extractCustomerPhoneFromMessage($testMessage) ?: 'NULL') . "\n";
echo "Email: " . (extractCustomerEmailFromMessage($testMessage) ?: 'NULL') . "\n";
echo "---\n";

$testMessage2 = 'I am touhid.. I want to about your vehicle wrapping service';
echo "Message: '$testMessage2'\n";
echo "Name: " . (extractCustomerNameFromMessage($testMessage2) ?: 'NULL') . "\n";
echo "Phone: " . (extractCustomerPhoneFromMessage($testMessage2) ?: 'NULL') . "\n";
echo "Email: " . (extractCustomerEmailFromMessage($testMessage2) ?: 'NULL') . "\n";
