<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Kortana AI</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eaeaea;
        }

        .logo {
            width: 150px;
            margin-bottom: 15px;
        }

        .content {
            padding: 30px 20px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #eaeaea;
        }

        h1 {
            color: #1a56db;
            margin-bottom: 20px;
        }

        .otp-container {
            background-color: #f0f5ff;
            border-left: 4px solid #1a56db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }

        .otp-code {
            font-size: 24px;
            font-weight: bold;
            color: #1a56db;
            letter-spacing: 5px;
        }

        .button {
            display: inline-block;
            background-color: #1a56db;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 20px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #1a56db;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="https://blogger.googleusercontent.com/img/a/AVvXsEi6PgRLvfFyXOyP9_02B5nsNFe2vUu5TGSQqtH0xYN1P_FiasC-mltSCb_QH8oWcsguta-r9FGuziHKLK7a9inar2RNlv8bUBEq39jHjaRc03_0TN4GDVT11HjY4cyy_xjOx-MbheIpiBbPyKbtRE2g_c5ELWMPmtDyZgnKigtXkK_okyQ6258AaFTOkJ6T"
                alt="Kortana AI Logo" class="logo">
            <h1>Welcome to Kortana AI</h1>
        </div>

        <div class="content">
            <p>Hello <?php echo e($details['name']); ?>,</p>

            <p>Thank you for joining Kortana AI! We're excited to have you on board. Your AI assistant is ready to help
                automate your appointment bookings and boost your business engagement.</p>

            <p>To verify your email address, please use the following OTP code:</p>

            <div class="otp-container">
                <div class="otp-code"><?php echo e($details['otp']); ?></div>
                <p>This code will expire in 10 minutes.</p>
            </div>

            <p>Once verified, you'll have full access to your AI dashboard where you can customize your assistant and
                start automating your appointment bookings.</p>

            <p>If you have any questions or need assistance, our support team is always ready to help.</p>

            <p>Best regards,<br>The Kortana AI Team</p>
        </div>

        <div class="footer">
            <p>&copy; <?php echo e(date('Y')); ?> Kortana AI. All rights reserved.</p>
            <div class="social-links">
                <a href="#">Twitter</a> |
                <a href="#">Facebook</a> |
                <a href="#">LinkedIn</a>
            </div>
            <p>
                <small>If you didn't create an account with us, please ignore this email.</small>
            </p>
        </div>
    </div>
</body>

</html>
<?php /**PATH C:\laragon\www\kortana-ai\resources\views/emails/auth/welcome.blade.php ENDPATH**/ ?>