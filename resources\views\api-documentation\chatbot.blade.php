<h2 id="chatbot">AI Chatbot</h2>
<p>
    Kortana AI's chatbot API allows you to integrate intelligent conversational AI capabilities into your applications.
    The chatbot can be customized with your knowledge base to provide domain-specific responses.
</p>

<div class="section-description">
    <p>Key chatbot features:</p>
    <ul>
        <li>Natural language understanding and generation</li>
        <li>Context-aware conversations with memory</li>
        <li>Integration with your knowledge base</li>
        <li>Customizable behavior and personality</li>
        <li>Support for multiple conversation threads</li>
    </ul>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/chatbot/message</span>
    </div>
    <div class="description">Send a message to the chatbot and receive a response</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>application/json</td>
                <td>Request body format</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>message <span class="required">*</span></td>
                <td>string</td>
                <td>User message text</td>
            </tr>
            <tr>
                <td>conversation_id</td>
                <td>string</td>
                <td>ID to maintain conversation context (optional)</td>
            </tr>
            <tr>
                <td>use_knowledge_base</td>
                <td>boolean</td>
                <td>Whether to use the knowledge base for responses (default: true)</td>
            </tr>
            <tr>
                <td>file_ids</td>
                <td>array</td>
                <td>Array of specific knowledge file IDs to use (optional)</td>
            </tr>
            <tr>
                <td>metadata</td>
                <td>object</td>
                <td>Additional context information (optional)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Message processed successfully",
  "code": 200,
  "data": {
    "response": "Based on our company policy, employees are entitled to 20 days of paid vacation per year, which can be carried over to the next year with manager approval.",
    "conversation_id": "conv_123456789",
    "sources": [
      {
        "file_id": 1,
        "file_name": "company_policy.pdf",
        "content": "Employees are entitled to 20 days of paid vacation per year...",
        "score": 0.92
      }
    ],
    "metadata": {
      "tokens_used": 156,
      "processing_time": "0.82s"
    }
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Unauthorized (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Unauthenticated",
  "code": 401
}</code></pre>

    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "message": [
      "The message field is required."
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/chatbot/conversations</span>
    </div>
    <div class="description">Get a list of user's chatbot conversations</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Query Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>page</td>
                <td>integer</td>
                <td>Page number for pagination</td>
            </tr>
            <tr>
                <td>per_page</td>
                <td>integer</td>
                <td>Number of conversations per page</td>
            </tr>
            <tr>
                <td>start_date</td>
                <td>date</td>
                <td>Filter conversations from this date (YYYY-MM-DD)</td>
            </tr>
            <tr>
                <td>end_date</td>
                <td>date</td>
                <td>Filter conversations until this date (YYYY-MM-DD)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Conversations retrieved successfully",
  "code": 200,
  "data": [
    {
      "id": "conv_123456789",
      "title": "Vacation Policy Inquiry",
      "last_message": "Based on our company policy, employees are entitled to 20 days...",
      "message_count": 6,
      "created_at": "2023-08-15 14:30:45",
      "updated_at": "2023-08-15 14:35:22"
    },
    {
      "id": "conv_987654321",
      "title": "Product Features Discussion",
      "last_message": "The premium plan includes all the features of the basic plan plus...",
      "message_count": 12,
      "created_at": "2023-08-14 10:15:30",
      "updated_at": "2023-08-14 10:25:45"
    }
  ],
  "pagination": {
    "current_page": 1,
    "last_page": 3,
    "per_page": 10,
    "total": 25,
    "from": 1,
    "to": 10
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/chatbot/conversations/{id}</span>
    </div>
    <div class="description">Get a specific conversation with message history</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Path Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>id <span class="required">*</span></td>
                <td>string</td>
                <td>Conversation ID</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Conversation retrieved successfully",
  "code": 200,
  "data": {
    "id": "conv_123456789",
    "title": "Vacation Policy Inquiry",
    "created_at": "2023-08-15 14:30:45",
    "updated_at": "2023-08-15 14:35:22",
    "messages": [
      {
        "id": 1,
        "role": "user",
        "content": "How many vacation days do employees get per year?",
        "created_at": "2023-08-15 14:30:45"
      },
      {
        "id": 2,
        "role": "assistant",
        "content": "Based on our company policy, employees are entitled to 20 days of paid vacation per year, which can be carried over to the next year with manager approval.",
        "sources": [
          {
            "file_id": 1,
            "file_name": "company_policy.pdf"
          }
        ],
        "created_at": "2023-08-15 14:30:48"
      },
      {
        "id": 3,
        "role": "user",
        "content": "Can I carry over unused vacation days?",
        "created_at": "2023-08-15 14:35:15"
      },
      {
        "id": 4,
        "role": "assistant",
        "content": "Yes, you can carry over unused vacation days to the next year, but you'll need to get approval from your manager first.",
        "sources": [
          {
            "file_id": 1,
            "file_name": "company_policy.pdf"
          }
        ],
        "created_at": "2023-08-15 14:35:22"
      }
    ]
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "Conversation not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method delete">DELETE</span>
        <span class="url">/api/v1/chatbot/conversations/{id}</span>
    </div>
    <div class="description">Delete a conversation</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Path Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>id <span class="required">*</span></td>
                <td>string</td>
                <td>Conversation ID to delete</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Conversation deleted successfully",
  "code": 200
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Not Found (404)</h4>
    <pre><code>{
  "success": false,
  "message": "Conversation not found",
  "code": 404
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/chatbot/config</span>
    </div>
    <div class="description">Update chatbot configuration settings</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>application/json</td>
                <td>Request body format</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>name</td>
                <td>string</td>
                <td>Chatbot name</td>
            </tr>
            <tr>
                <td>personality</td>
                <td>string</td>
                <td>Chatbot personality description</td>
            </tr>
            <tr>
                <td>default_knowledge_files</td>
                <td>array</td>
                <td>Array of default knowledge file IDs</td>
            </tr>
            <tr>
                <td>system_prompt</td>
                <td>string</td>
                <td>Custom system prompt for the chatbot</td>
            </tr>
            <tr>
                <td>model</td>
                <td>string</td>
                <td>AI model to use (default: "gpt-3.5-turbo")</td>
            </tr>
            <tr>
                <td>temperature</td>
                <td>float</td>
                <td>Response randomness (0.0-2.0, default: 0.7)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Chatbot configuration updated successfully",
  "code": 200,
  "data": {
    "name": "Support Assistant",
    "personality": "Friendly, helpful, and knowledgeable about our products",
    "default_knowledge_files": [1, 2, 3],
    "system_prompt": "You are a helpful support assistant for our company...",
    "model": "gpt-4",
    "temperature": 0.5,
    "updated_at": "2023-08-15 14:30:45"
  }
}</code></pre>
</div>

<div class="section-notes">
    <h3>Chatbot Notes</h3>
    <ul>
        <li>Conversations maintain context for more natural interactions</li>
        <li>Responses are generated using advanced AI language models</li>
        <li>Knowledge base integration provides accurate domain-specific answers</li>
        <li>Conversation history is stored securely and accessible only to authorized users</li>
        <li>Response time may vary based on message complexity and server load</li>
        <li>Chatbot can be customized with different personalities and knowledge sources</li>
    </ul>
</div>
