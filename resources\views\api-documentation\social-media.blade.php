<h2 id="social-media">Social Media Integration</h2>
<p>
    Kortana AI provides seamless integration with popular social media platforms like Facebook and Instagram. These
    endpoints allow you to send messages, receive webhooks, and manage your social media presence.
</p>

<div class="section-description">
    <p>Available social media features:</p>
    <ul>
        <li>Send messages to Facebook and Instagram users</li>
        <li>Receive and process incoming messages via webhooks</li>
        <li>Authenticate with Instagram</li>
        <li>Manage Meta (Facebook/Instagram) tokens</li>
    </ul>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/social/send-message</span>
    </div>
    <div class="description">Send a message to a user on a social media platform</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
            <tr>
                <td>Content-Type</td>
                <td>application/json</td>
                <td>Request body format</td>
            </tr>
        </tbody>
    </table>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>platform <span class="required">*</span></td>
                <td>string</td>
                <td>Social media platform (facebook, instagram)</td>
            </tr>
            <tr>
                <td>recipient_id <span class="required">*</span></td>
                <td>string</td>
                <td>ID of the message recipient</td>
            </tr>
            <tr>
                <td>message <span class="required">*</span></td>
                <td>string</td>
                <td>Text message to send</td>
            </tr>
            <tr>
                <td>media_url</td>
                <td>string</td>
                <td>URL to media attachment (image, video, etc.)</td>
            </tr>
            <tr>
                <td>media_type</td>
                <td>string</td>
                <td>Type of media (image, video, audio, file)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Message sent successfully",
  "code": 200,
  "data": {
    "message_id": "mid.123456789",
    "recipient_id": "123456789",
    "timestamp": 1625097600
  }
}</code></pre>

    <h3>Error Responses</h3>
    <h4>Unauthorized (401)</h4>
    <pre><code>{
  "success": false,
  "message": "Unauthenticated",
  "code": 401
}</code></pre>

    <h4>Validation Error (422)</h4>
    <pre><code>{
  "success": false,
  "message": "Validation failed",
  "code": 422,
  "errors": {
    "platform": [
      "The platform field is required.",
      "The platform must be one of: facebook, instagram."
    ],
    "recipient_id": [
      "The recipient id field is required."
    ],
    "message": [
      "The message field is required when media url is not present."
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/social/messages</span>
    </div>
    <div class="description">Get social media messages history</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Query Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>platform</td>
                <td>string</td>
                <td>Filter by platform (facebook, instagram)</td>
            </tr>
            <tr>
                <td>user_id</td>
                <td>string</td>
                <td>Filter by user ID</td>
            </tr>
            <tr>
                <td>page</td>
                <td>integer</td>
                <td>Page number for pagination</td>
            </tr>
            <tr>
                <td>per_page</td>
                <td>integer</td>
                <td>Number of messages per page</td>
            </tr>
            <tr>
                <td>start_date</td>
                <td>date</td>
                <td>Filter messages from this date (YYYY-MM-DD)</td>
            </tr>
            <tr>
                <td>end_date</td>
                <td>date</td>
                <td>Filter messages until this date (YYYY-MM-DD)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Messages retrieved successfully",
  "code": 200,
  "data": [
    {
      "id": 1,
      "platform": "instagram",
      "sender_id": "123456789",
      "recipient_id": "987654321",
      "message": "Hello there!",
      "media_url": null,
      "media_type": null,
      "direction": "inbound",
      "timestamp": "2023-08-15 14:30:45",
      "created_at": "2023-08-15 14:30:45",
      "updated_at": "2023-08-15 14:30:45"
    },
    {
      "id": 2,
      "platform": "instagram",
      "sender_id": "987654321",
      "recipient_id": "123456789",
      "message": "Hi! How can I help you today?",
      "media_url": null,
      "media_type": null,
      "direction": "outbound",
      "timestamp": "2023-08-15 14:31:22",
      "created_at": "2023-08-15 14:31:22",
      "updated_at": "2023-08-15 14:31:22"
    }
  ],
  "pagination": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 10,
    "total": 42,
    "from": 1,
    "to": 10
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/social/stats</span>
    </div>
    <div class="description">Get social media messaging statistics</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Query Parameters</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>platform</td>
                <td>string</td>
                <td>Filter by platform (facebook, instagram)</td>
            </tr>
            <tr>
                <td>start_date</td>
                <td>date</td>
                <td>Start date for statistics (YYYY-MM-DD)</td>
            </tr>
            <tr>
                <td>end_date</td>
                <td>date</td>
                <td>End date for statistics (YYYY-MM-DD)</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Statistics retrieved successfully",
  "code": 200,
  "data": {
    "total_messages": 542,
    "inbound_messages": 271,
    "outbound_messages": 271,
    "unique_users": 42,
    "platforms": {
      "instagram": 342,
      "facebook": 200
    },
    "daily_stats": [
      {
        "date": "2023-08-10",
        "total": 56,
        "inbound": 28,
        "outbound": 28
      },
      {
        "date": "2023-08-11",
        "total": 62,
        "inbound": 31,
        "outbound": 31
      }
    ]
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method get">GET</span>
        <span class="url">/api/v1/instagram/auth</span>
    </div>
    <div class="description">Get Instagram authentication URL</div>

    <h3>Request Headers</h3>
    <table>
        <thead>
            <tr>
                <th>Header</th>
                <th>Value</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Authorization <span class="required">*</span></td>
                <td>Bearer {token}</td>
                <td>Authentication token</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Instagram auth URL generated successfully",
  "code": 200,
  "data": {
    "auth_url": "https://api.instagram.com/oauth/authorize?client_id=YOUR_CLIENT_ID&redirect_uri=YOUR_REDIRECT_URI&scope=user_profile,user_media&response_type=code"
  }
}</code></pre>
</div>

<div class="endpoint">
    <div class="endpoint-header">
        <span class="method post">POST</span>
        <span class="url">/api/v1/instagram/callback</span>
    </div>
    <div class="description">Handle Instagram OAuth callback</div>

    <h3>Request Body</h3>
    <table>
        <thead>
            <tr>
                <th>Parameter</th>
                <th>Type</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>code <span class="required">*</span></td>
                <td>string</td>
                <td>Authorization code from Instagram</td>
            </tr>
        </tbody>
    </table>

    <h3>Response</h3>
    <pre><code>{
  "success": true,
  "message": "Instagram authentication successful",
  "code": 200,
  "data": {
    "access_token": "INSTAGRAM_ACCESS_TOKEN",
    "user_id": "INSTAGRAM_USER_ID",
    "expires_in": 5184000
  }
}</code></pre>
</div>

<div class="section-notes">
    <h3>Social Media Integration Notes</h3>
    <ul>
        <li>Instagram and Facebook integrations require valid Meta developer credentials</li>
        <li>Webhook endpoints must be publicly accessible and verified by Meta</li>
        <li>Message content is subject to Meta's platform policies</li>
        <li>Media attachments must comply with platform size and format restrictions</li>
        <li>Instagram access tokens expire after 60 days and require refresh</li>
    </ul>
</div>
