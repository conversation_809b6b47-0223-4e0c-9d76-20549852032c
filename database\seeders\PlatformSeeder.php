<?php

declare (strict_types = 1);

namespace Database\Seeders;

use App\Models\Platform;
use App\Models\PlatformCategory;
use Illuminate\Database\Seeder;

final class PlatformSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get category IDs
        $socialMediaCategory   = PlatformCategory::where('name', 'social_media')->first();
        $communicationCategory = PlatformCategory::where('name', 'communication')->first();
        $reviewsCategory       = PlatformCategory::where('name', 'reviews')->first();
        $othersCategory        = PlatformCategory::where('name', 'others')->first();

        $platforms = [
            // Social Media Platforms
            [
                'name'        => 'Instagram',
                'key'         => 'instagram',
                'description' => 'Connect your Instagram Business account to respond to comments and DMs',
                'category_id' => $socialMediaCategory?->id,
                'enabled'     => true,
            ],
            [
                'name'        => 'Facebook',
                'key'         => 'facebook',
                'description' => 'Connect your Facebook Page to handle messages and comments',
                'category_id' => $socialMediaCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'Twitter',
                'key'         => 'twitter',
                'description' => 'Connect your Twitter account to manage tweets and direct messages',
                'category_id' => $socialMediaCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'LinkedIn',
                'key'         => 'linkedin',
                'description' => 'Connect your LinkedIn account to respond to messages and comments',
                'category_id' => $socialMediaCategory?->id,
                'enabled'     => false,
            ],

            // Communication Platforms
            [
                'name'        => 'WhatsApp',
                'key'         => 'whatsapp',
                'description' => 'Connect your WhatsApp Business account for customer support',
                'category_id' => $communicationCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'Telegram',
                'key'         => 'telegram',
                'description' => 'Connect your Telegram bot for automated messaging',
                'category_id' => $communicationCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'Slack',
                'key'         => 'slack',
                'description' => 'Connect your Slack workspace for team notifications',
                'category_id' => $communicationCategory?->id,
                'enabled'     => false,
            ],

            // Review Platforms
            [
                'name'        => 'Google Reviews',
                'key'         => 'google_reviews',
                'description' => 'Connect your Google Business account to manage reviews',
                'category_id' => $reviewsCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'Yelp',
                'key'         => 'yelp',
                'description' => 'Connect your Yelp Business account to respond to reviews',
                'category_id' => $reviewsCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'Trustpilot',
                'key'         => 'trustpilot',
                'description' => 'Connect your Trustpilot account to manage customer reviews',
                'category_id' => $reviewsCategory?->id,
                'enabled'     => false,
            ],

            // Other Platforms
            [
                'name'        => 'Email',
                'key'         => 'email',
                'description' => 'Connect your email account for automated responses',
                'category_id' => $othersCategory?->id,
                'enabled'     => false,
            ],
            [
                'name'        => 'SMS',
                'key'         => 'sms',
                'description' => 'Connect your SMS service for text message automation',
                'category_id' => $othersCategory?->id,
                'enabled'     => false,
            ],
        ];

        foreach ($platforms as $platformData) {
            Platform::updateOrCreate(
                ['key' => $platformData['key']],
                $platformData
            );
        }

        $this->command->info('Platform seeder completed successfully!');
    }
}