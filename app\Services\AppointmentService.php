<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\Appointment;
use App\Repositories\Appointment\AppointmentRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

final class AppointmentService
{
    public function __construct(
        private readonly AppointmentRepositoryInterface $appointmentRepository
    ) {
    }

    /**
     * Universal query method for appointments with filters and options
     */
    public function queryAppointments(array $options = []): LengthAwarePaginator | Collection | array | int
    {
        // Add user_id if userOnly is specified and not already set
        if (($options['user_only'] ?? true) && !isset($options['user_id'])) {
            $options['user_id'] = Auth::id();
        }

        return $this->appointmentRepository->queryAppointments($options);
    }

    /**
     * Find appointment by ID
     */
    public function findAppointment(int $id, bool $userOnly = true): ?Appointment
    {
        $userId = $userOnly ? Auth::id() : null;
        return $this->appointmentRepository->findAppointment($id, $userId);
    }

    /**
     * Create a new appointment
     */
    public function createAppointment(array $data): Appointment
    {
        // Add user_id if not provided
        if (!isset($data['user_id'])) {
            // Try to get user_id from user_agent via repository first (more reliable)
            if (isset($data['user_agent_id'])) {
                $userAgentRepo = app(\App\Repositories\UserAgent\UserAgentRepositoryInterface::class);
                $userAgent     = $userAgentRepo->findById((int) $data['user_agent_id']);
                if ($userAgent) {
                    $data['user_id'] = $userAgent->user_id;
                }
            }

            // If still no user_id, try Auth (but handle CLI context)
            if (!isset($data['user_id'])) {
                try {
                    $data['user_id'] = Auth::id();
                } catch (\Exception $e) {
                    // Auth not available in CLI context
                    throw new \InvalidArgumentException('user_id is required and could not be determined automatically. Please provide user_id explicitly.');
                }
            }
        }

        // Set booking timestamp
        if (!isset($data['booked_at'])) {
            $data['booked_at'] = now();
        }

        // Validate time slot availability
        if (isset($data['appointment_date'], $data['appointment_time'], $data['user_agent_id'])) {
            if (!$this->isTimeSlotAvailable($data['appointment_date'], $data['appointment_time'], $data['user_agent_id'])) {
                throw new \InvalidArgumentException('The selected time slot is not available.');
            }
        }

        Log::info('Creating appointment', [
            'data'    => $data,
            'user_id' => $data['user_id'] ?? null,
        ]);

        return $this->appointmentRepository->createAppointment($data);
    }

    /**
     * Update an appointment
     */
    public function updateAppointment(int $id, array $data, bool $userOnly = true): bool
    {
        // Verify ownership if userOnly is true
        if ($userOnly) {
            $appointment = $this->findAppointment($id, true);
            if (!$appointment) {
                return false;
            }
        }

        // Validate time slot availability if date/time is being changed
        if (isset($data['appointment_date'], $data['appointment_time'], $data['user_agent_id'])) {
            if (!$this->isTimeSlotAvailable($data['appointment_date'], $data['appointment_time'], $data['user_agent_id'], $id)) {
                throw new \InvalidArgumentException('The selected time slot is not available.');
            }
        }

        Log::info('Updating appointment', [
            'id'        => $id,
            'data'      => $data,
            'user_only' => $userOnly,
        ]);

        return $this->appointmentRepository->updateAppointment($id, $data);
    }

    /**
     * Update appointment status with optional notes
     */
    public function updateAppointmentStatus(int $id, string $status, bool $userOnly = true, ?string $notes = null): bool
    {
        $availableStatuses = $this->getAvailableStatuses();

        if (!in_array($status, $availableStatuses)) {
            throw new \InvalidArgumentException('Invalid status provided.');
        }

        $userId = $userOnly ? Auth::id() : null;

        Log::info('Updating appointment status', [
            'id'      => $id,
            'status'  => $status,
            'notes'   => $notes,
            'user_id' => $userId,
        ]);

        return $this->appointmentRepository->updateAppointmentStatus($id, $status, $userId, $notes);
    }

    /**
     * Delete an appointment
     */
    public function deleteAppointment(int $id, bool $userOnly = true): bool
    {
        // Verify ownership if userOnly is true
        if ($userOnly) {
            $appointment = $this->findAppointment($id, true);
            if (!$appointment) {
                return false;
            }
        }

        Log::info('Deleting appointment', [
            'id'        => $id,
            'user_only' => $userOnly,
        ]);

        return $this->appointmentRepository->deleteAppointment($id);
    }

    /**
     * Get appointment statistics
     */
    public function getAppointmentStats(bool $userOnly = true): array
    {
        return $this->queryAppointments([
            'type'      => 'stats',
            'user_only' => $userOnly,
        ]);
    }

    /**
     * Get today's appointments
     */
    public function getTodayAppointments(bool $userOnly = true): Collection
    {
        return $this->queryAppointments([
            'type'      => 'collection',
            'today'     => true,
            'user_only' => $userOnly,
        ]);
    }

    /**
     * Get today's appointments with pagination
     */
    public function getTodayAppointmentsPaginated(bool $userOnly = true, int $perPage = 15): LengthAwarePaginator
    {
        return $this->queryAppointments([
            'today'     => true,
            'user_only' => $userOnly,
            'per_page'  => $perPage,
        ]);
    }

    /**
     * Get upcoming appointments count
     */
    public function getUpcomingAppointmentsCount(bool $userOnly = true): int
    {
        $result = $this->queryAppointments([
            'type'      => 'count',
            'upcoming'  => true,
            'user_only' => $userOnly,
        ]);

        return is_int($result) ? $result : 0;
    }

    /**
     * Check if time slot is available
     */
    public function isTimeSlotAvailable(string $date, string $time, int $userAgentId, int $excludeId = null): bool
    {
        return $this->appointmentRepository->isTimeSlotAvailable($date, $time, $userAgentId, $excludeId);
    }

    /**
     * Get available statuses
     */
    public function getAvailableStatuses(): array
    {
        return $this->appointmentRepository->getAvailableStatuses();
    }

    /**
     * Search appointments
     */
    public function searchAppointments(string $query, bool $userOnly = true, int $perPage = 15): LengthAwarePaginator
    {
        return $this->queryAppointments([
            'search'    => $query,
            'user_only' => $userOnly,
            'per_page'  => $perPage,
        ]);
    }

    /**
     * Get appointments for calendar view
     */
    public function getCalendarAppointments(string $month = null, string $year = null, bool $userOnly = true): array
    {
        return $this->queryAppointments([
            'type'      => 'calendar',
            'month'     => $month ?? now()->month,
            'year'      => $year ?? now()->year,
            'user_only' => $userOnly,
        ]);
    }

    /**
     * Get appointment counts by date range for calendar
     */
    public function getAppointmentCountsByDateRange(string $startDate, string $endDate, bool $userOnly = true): array
    {
        $appointments = $this->queryAppointments([
            'type'      => 'collection',
            'date_from' => $startDate,
            'date_to'   => $endDate,
            'user_only' => $userOnly,
        ]);

        $counts = [];
        foreach ($appointments as $appointment) {
            $dateKey          = $appointment->appointment_date->format('Y-m-d');
            $counts[$dateKey] = ($counts[$dateKey] ?? 0) + 1;
        }

        return $counts;
    }

    /**
     * Get platform statistics
     */
    public function getPlatformStats(bool $userOnly = true): array
    {
        // This would need to be implemented in the repository
        // For now, return empty array
        return [];
    }

    // ===== BACKWARD COMPATIBILITY METHODS =====
    // These methods maintain backward compatibility while using the new unified approach

    /**
     * Get appointments with filters and pagination
     */
    public function getUserAppointments(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->queryAppointments(array_merge($filters, [
            'per_page'  => $perPage,
            'user_only' => true,
        ]));
    }

    /**
     * Get appointments for a specific date
     */
    public function getAppointmentsByDate(string $date, bool $userOnly = true): Collection
    {
        return $this->queryAppointments([
            'type'      => 'collection',
            'date'      => $date,
            'user_only' => $userOnly,
        ]);
    }

    /**
     * Get appointments for a date range
     */
    public function getAppointmentsByDateRange(string $startDate, string $endDate, bool $userOnly = true): Collection
    {
        return $this->queryAppointments([
            'type'      => 'collection',
            'date_from' => $startDate,
            'date_to'   => $endDate,
            'user_only' => $userOnly,
        ]);
    }

    /**
     * Get appointments by status
     */
    public function getAppointmentsByStatus(string $status, bool $userOnly = true, int $perPage = 15): LengthAwarePaginator
    {
        return $this->queryAppointments([
            'status'    => $status,
            'user_only' => $userOnly,
            'per_page'  => $perPage,
        ]);
    }

    /**
     * Get appointments by platform
     */
    public function getAppointmentsByPlatform(string $platform, bool $userOnly = true, int $perPage = 15): LengthAwarePaginator
    {
        return $this->queryAppointments([
            'platform'  => $platform,
            'user_only' => $userOnly,
            'per_page'  => $perPage,
        ]);
    }
}