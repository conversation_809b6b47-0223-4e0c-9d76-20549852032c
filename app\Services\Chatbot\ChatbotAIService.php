<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Services\OllamaService;
use Exception;
use GeminiAPI\Client as GeminiClient;
use GeminiAPI\GenerationConfig;
use GeminiAPI\Resources\ModelName;
use GeminiAPI\Resources\Parts\TextPart;
use Illuminate\Support\Facades\Log;

final class ChatbotAIService
{
    private ?GeminiClient $geminiClient;
    private ?OllamaService $ollamaService;
    private bool $useOllama;

    public function __construct()
    {
        $llmModel = config('services.llm_model', 'gemini');
        $this->initializeServices($llmModel);

        Log::info('ChatbotAIService initialized', [
            'llm_model'         => $llmModel,
            'use_ollama'        => $this->useOllama,
            'ollama_available'  => $this->ollamaService?->isAvailable() ?? false,
            'gemini_configured' => $this->geminiClient !== null,
        ]);
    }

    /**
     * Initialize AI services based on LLM model configuration
     */
    private function initializeServices(string $llmModel): void
    {
        $llmModel = strtolower($llmModel);

        switch ($llmModel) {
            case 'ollama':
                $this->ollamaService = app(OllamaService::class);
                $this->useOllama     = $this->ollamaService->isAvailable();
                $this->geminiClient  = $this->initializeGeminiAsBackup();

                if (!$this->useOllama) {
                    Log::warning('Ollama requested but not available, falling back to Gemini');
                }
                break;

            case 'gemini':
            default:
                $this->useOllama     = false;
                $this->ollamaService = null;
                $this->geminiClient  = $this->initializeGemini();
                break;
        }
    }

    /**
     * Initialize Gemini client
     */
    private function initializeGemini(): ?GeminiClient
    {
        $geminiApiKey = config('services.gemini.api_key');

        if (!$geminiApiKey) {
            Log::error('Gemini API key not configured. Please set GEMINI_API_KEY in your .env file.');
            return null;
        }

        return new GeminiClient($geminiApiKey);
    }

    /**
     * Initialize Gemini as backup service
     */
    private function initializeGeminiAsBackup(): ?GeminiClient
    {
        $geminiApiKey = config('services.gemini.api_key');

        if (!$geminiApiKey) {
            Log::warning('Gemini API key not configured - no fallback available for Ollama');
            return null;
        }

        return new GeminiClient($geminiApiKey);
    }

    /**
     * Generate AI response using the configured LLM
     */
    public function generateResponse(string $prompt, array $conversationHistory = []): string
    {
        // Log the exact prompt sent to the LLM each time
        try {
            Log::info('CHATBOT LLM PROMPT', [
                'model'                   => $this->useOllama ? 'ollama' : 'gemini',
                'prompt_length'           => strlen($prompt),
                'conversation_hist_count' => count($conversationHistory),
                'prompt'                  => $prompt,
            ]);
        } catch (Exception $e) {
            // Never fail generation because logging failed
        }

        try {
            $response = $this->useOllama
            ? $this->generateOllamaResponse($prompt, $conversationHistory)
            : $this->generateGeminiResponse($prompt);

            // Clean model output and ensure length constraints
            $response = $this->sanitizeModelOutput($response);
            return $this->ensureResponseLength($response);

        } catch (Exception $e) {
            Log::error('Failed to generate AI response', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 'I apologize, but I\'m experiencing technical difficulties. Please try again in a moment.';
        }
    }

    /**
     * Generate response using Gemini API
     */
    private function generateGeminiResponse(string $prompt): string
    {
        if (!$this->geminiClient) {
            throw new Exception('Gemini client not available');
        }

        // Configure Gemini generation with shorter responses for Instagram
        $config = (new GenerationConfig())
            ->withTemperature((float) config('services.gemini.temperature', 0.7))
            ->withTopP((float) config('services.gemini.top_p', 0.9))
            ->withTopK((int) config('services.gemini.top_k', 40))
            ->withMaxOutputTokens((int) config('services.gemini.max_tokens', 600));

        // Call Gemini API
        $modelName = config('services.gemini.model', 'gemini-2.0-flash-exp');
        $response  = $this->geminiClient->generativeModel($modelName)
            ->withGenerationConfig($config)
            ->generateContent(new TextPart($prompt));

        return $response->text() ?: 'I apologize, but I encountered an issue generating a response. Please try again.';
    }

    /**
     * Generate response using Ollama API
     */
    private function generateOllamaResponse(string $prompt, array $conversationHistory): string
    {
        try {
            // Format conversation history for Ollama
            $messages = [];

            // Add system message
            $messages[] = [
                'role'    => 'system',
                'content' => "You are a friendly AI assistant for our business. " .
                "You help customers with service information, pricing, and appointment booking. " .
                "Be conversational, helpful, and professional. " .
                "IMPORTANT: Keep responses short and concise for Instagram messaging (under 600 characters when possible). " .
                "Break complex information into simple, digestible parts. Use emojis sparingly and focus on the most relevant information.",
            ];

            // Add conversation history
            foreach ($conversationHistory as $msg) {
                $messages[] = [
                    'role'    => $msg['role'],
                    'content' => $msg['content'],
                ];
            }

            // Add the current prompt as the final user message if not already included
            if (empty($conversationHistory) || $conversationHistory[count($conversationHistory) - 1]['role'] !== 'user') {
                $messages[] = [
                    'role'    => 'user',
                    'content' => $prompt,
                ];
            }

            // Generate response using Ollama
            return $this->ollamaService->generateChatCompletion($messages);

        } catch (Exception $e) {
            Log::error('Failed to generate Ollama response, falling back to Gemini', [
                'error' => $e->getMessage(),
            ]);

            // Fallback to Gemini if Ollama fails
            return $this->generateGeminiResponse($prompt);
        }
    }

    /**
     * Generate text embedding using the configured service
     */
    public function generateEmbedding(string $text): array
    {
        // Try Ollama first if enabled and available
        if ($this->useOllama) {
            try {
                return $this->generateOllamaEmbedding($text);
            } catch (Exception $e) {
                Log::warning('Ollama embedding failed, falling back to Gemini', [
                    'error' => $e->getMessage(),
                    'text'  => substr($text, 0, 100),
                ]);
                // Continue to Gemini fallback
            }
        }

        // Use Gemini as fallback or primary
        return $this->generateGeminiEmbedding($text);
    }

    /**
     * Generate text embedding using Gemini
     */
    private function generateGeminiEmbedding(string $text): array
    {
        if (!$this->geminiClient) {
            throw new Exception('Gemini API key not configured. Please set GEMINI_API_KEY in your .env file.');
        }

        try {
            $response = $this->geminiClient->embeddingModel(ModelName::EMBEDDING_001)
                ->embedContent(new TextPart($text));

            return $response->embedding->values;
        } catch (Exception $e) {
            Log::error('Failed to generate Gemini embedding', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);
            throw $e;
        }
    }

    /**
     * Generate text embedding using Ollama
     */
    private function generateOllamaEmbedding(string $text): array
    {
        if (!$this->ollamaService) {
            throw new Exception('Ollama service not available');
        }

        try {
            return $this->ollamaService->generateEmbedding($text);
        } catch (Exception $e) {
            Log::error('Failed to generate Ollama embedding', [
                'error' => $e->getMessage(),
                'text'  => substr($text, 0, 100),
            ]);
            throw $e;
        }
    }

    /**
     * Ensure response length is appropriate for Instagram messaging
     */
    private function ensureResponseLength(string $response): string
    {
        // Instagram has a 1000 character limit, but we want to keep responses shorter when possible
        $optimalLength = 800; // Leave room for chunking indicators if needed

        if (strlen($response) <= $optimalLength) {
            return $response;
        }

        Log::info('Response too long, truncating', [
            'original_length' => strlen($response),
            'optimal_length'  => $optimalLength,
        ]);

        // Try to find a natural breaking point near the optimal length
        $truncated = substr($response, 0, $optimalLength);

        // Look for the last complete sentence
        $lastPeriod      = strrpos($truncated, '.');
        $lastExclamation = strrpos($truncated, '!');
        $lastQuestion    = strrpos($truncated, '?');

        $lastSentenceEnd = max($lastPeriod, $lastExclamation, $lastQuestion);

        if ($lastSentenceEnd && $lastSentenceEnd > $optimalLength * 0.7) {
            // Use the last complete sentence if it's not too short
            $truncated = substr($response, 0, $lastSentenceEnd + 1);
        } else {
            // Look for last complete word
            $lastSpace = strrpos($truncated, ' ');
            if ($lastSpace) {
                $truncated = substr($response, 0, $lastSpace);
            }
            $truncated .= '...';
        }

        return $truncated;
    }

    /**
     * Sanitize model output by removing role labels and leading prefixes
     */
    private function sanitizeModelOutput(string $response): string
    {
        $clean = ltrim($response);
        // Remove common role prefixes at the beginning
        $clean = preg_replace('/^(assistant|customer|user|system)\s*:\s*/i', '', $clean ?? '');
        // Also strip a repeated prefix if present twice
        $clean = preg_replace('/^(assistant|customer|user|system)\s*:\s*/i', '', $clean ?? '');
        return trim($clean ?? $response);
    }

}