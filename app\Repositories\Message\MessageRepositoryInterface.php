<?php

declare (strict_types = 1);

namespace App\Repositories\Message;

use App\Models\InstagramToken;
use App\Models\Message;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface MessageRepositoryInterface
{
    public function create(array $data): Message;

    public function findById(int $id): ?Message;

    public function findByMessageId(string $messageId): ?Message;

    public function update(Message $message, array $data): Message;

    public function delete(Message $message): bool;

    // Conversation methods are keyed by sender_id now; deprecated conversation_id methods removed

    /**
     * Get pending messages for processing
     */
    public function getPendingMessages(): Collection;

    /**
     * Get messages by sender with pagination
     */
    public function getMessagesBySender(string $senderId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Mark message as replied with AI response
     */
    public function markAsReplied(Message $message, string $reply): Message;

    /**
     * Mark message as typing (for status updates)
     */
    public function markAsTyping(Message $message): Message;

    /**
     * Get conversation messages ordered by time for context building
     */
    public function getConversationHistory(string $senderId, int $limit = 20): Collection;

    /**
     * Check if conversation exists
     */
    public function conversationExists(string $senderId): bool;

    /**
     * Get message statistics for analytics
     */
    public function getMessageStats(): array;

    /**
     * Create an incoming message from webhook data
     */
    public function createIncomingMessage(array $data, InstagramToken $instagramToken): Message;

    /**
     * Create an outgoing message (reply) from webhook data
     */
    public function createOutgoingMessage(array $data, Message $incomingMessage, InstagramToken $instagramToken): Message;

    /**
     * Get messages for a specific Instagram Business Account
     */
    public function getMessagesForInstagramAccount(string $instagramBusinessId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get conversations for a specific Instagram Business Account
     */
    public function getConversationsForInstagramAccount(string $instagramBusinessId, int $limit = 20): Collection;
}
