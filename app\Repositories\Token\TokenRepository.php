<?php

declare (strict_types = 1);

namespace App\Repositories\Token;

use App\Models\Token;
use Illuminate\Database\Eloquent\Collection;

final class TokenRepository implements TokenRepositoryInterface
{
    public function __construct(
        private readonly Token $model
    ) {}

    public function create(array $data): Token
    {
        return $this->model->create($data);
    }

    public function findByPlatformAndUser(int $platformId, int $userId): ?Token
    {
        return $this->model->where('platform_id', $platformId)
            ->where('user_id', $userId)
            ->first();
    }

    public function getByPlatformAndUser(int $platformId, int $userId): Collection
    {
        return $this->model->where('platform_id', $platformId)
            ->where('user_id', $userId)
            ->get();
    }

    public function getActiveTokenForUserAndPlatform(int $userId, int $platformId): ?Token
    {
        return $this->model->where('platform_id', $platformId)
            ->where('user_id', $userId)
            ->whereHasMorph('tokenable', ['*'], function ($query) {
                $query->where('is_active', true)
                    ->where('status', 'active');
            })
            ->with('tokenable')
            ->first();
    }
}