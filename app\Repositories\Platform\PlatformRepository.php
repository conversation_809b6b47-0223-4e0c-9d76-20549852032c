<?php

declare (strict_types = 1);

namespace App\Repositories\Platform;

use App\Models\Platform;
use App\Models\PlatformCategory;
use Illuminate\Database\Eloquent\Collection;

final class PlatformRepository implements PlatformRepositoryInterface
{
    /**
     * Get all platforms.
     */
    public function getAll(): Collection
    {
        return Platform::all();
    }

    /**
     * Get platforms by category ID.
     */
    public function getByCategoryId(int $categoryId): Collection
    {
        return Platform::byCategory($categoryId)->get();
    }

    /**
     * Get platforms by category name.
     */
    public function getByCategoryName(string $categoryName): Collection
    {
        $categoryId = PlatformCategory::getCategoryIdByName($categoryName);

        if (!$categoryId) {
            return new Collection();
        }

        return $this->getByCategoryId($categoryId);
    }

    /**
     * Get active platforms (enabled and connected).
     */
    public function getActive(): Collection
    {
        return Platform::enabled()->get();
    }

    /**
     * Get enabled platforms.
     */
    public function getEnabled(): Collection
    {
        return Platform::enabled()->get();
    }

    /**
     * Find platform by key.
     */
    public function findByKey(string $key): ?Platform
    {
        return Platform::byKey($key)->first();
    }

    /**
     * Enable a platform.
     */
    public function enable(Platform $platform): bool
    {
        return $platform->update(['enabled' => true]);
    }

    /**
     * Disable a platform.
     */
    public function disable(Platform $platform): bool
    {
        return $platform->update(['enabled' => false]);
    }

    /**
     * Get platform statistics.
     */
    public function getStatistics(): array
    {
        $total   = Platform::count();
        $enabled = Platform::enabled()->count();

        return [
            'total'    => $total,
            'enabled'  => $enabled,
            'disabled' => $total - $enabled,
        ];
    }

    /**
     * Get platforms grouped by category.
     */
    public function getGroupedByCategory(): Collection
    {
        return Platform::orderBy('category_id')
            ->orderBy('name')
            ->get()
            ->groupBy('category.name');
    }

    /**
     * Get enabled platforms grouped by category.
     */
    public function getEnabledGroupedByCategory(): Collection
    {
        return Platform::enabled()
            ->with('category')
            ->orderBy('category_id')
            ->orderBy('name')
            ->get()
            ->groupBy('category.name');
    }
}