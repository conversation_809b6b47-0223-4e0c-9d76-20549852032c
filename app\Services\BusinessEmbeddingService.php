<?php

declare (strict_types = 1);

namespace App\Services;

use App\Models\BusinessEmbedding;
use App\Models\UserAgent;
use App\Repositories\BusinessEmbedding\BusinessEmbeddingRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class BusinessEmbeddingService
{

    public function __construct(
        private readonly BusinessEmbeddingRepositoryInterface $businessEmbeddingRepository,
        private readonly KnowledgeService $knowledgeService
    ) {
        Log::info('BusinessEmbeddingService initialized', [
            'using_knowledge_service' => true,
            'embedding_delegated_to'  => 'KnowledgeService',
        ]);
    }

    /**
     * Process and embed all business data for a user agent
     */
    public function embedUserAgentBusinessData(UserAgent $userAgent): void
    {
        try {
            DB::beginTransaction();

            // Clear existing embeddings for this user agent
            $this->clearUserAgentEmbeddings($userAgent);

            // Process company details
            if ($userAgent->company_details) {
                $companyDetails = is_string($userAgent->company_details)
                ? json_decode($userAgent->company_details, true)
                : $userAgent->company_details;
                $this->embedCompanyDetails($userAgent, $companyDetails);
            }

            // Process agent business data
            if ($userAgent->agent_business_data) {
                $businessData = is_string($userAgent->agent_business_data)
                ? json_decode($userAgent->agent_business_data, true)
                : $userAgent->agent_business_data;
                $this->embedAgentBusinessData($userAgent, $businessData);
            }

            DB::commit();

            Log::info('Successfully embedded business data for user agent', [
                'user_agent_id'    => $userAgent->id,
                'embeddings_count' => $this->businessEmbeddingRepository->getCountByUserAgent($userAgent),
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to embed business data for user agent', [
                'user_agent_id' => $userAgent->id,
                'error'         => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Embed company details data
     */
    private function embedCompanyDetails(UserAgent $userAgent, array $companyDetails): void
    {
        // Embed basic company information
        $basicInfo = [
            'name'        => $companyDetails['name'] ?? '',
            'type'        => $companyDetails['type'] ?? '',
            'description' => $companyDetails['description'] ?? '',
        ];

        if (!empty(array_filter($basicInfo))) {
            $content = $this->formatCompanyBasicInfo($basicInfo);
            $this->createEmbedding($userAgent, 'company_details', 'basic_info', $content, $basicInfo);
        }

        // Embed contact information
        if (!empty($companyDetails['contact'])) {
            $content = $this->formatContactInfo($companyDetails['contact']);
            $this->createEmbedding($userAgent, 'company_details', 'contact', $content, $companyDetails['contact']);
        }

        // Embed location information
        if (!empty($companyDetails['location'])) {
            $content = $this->formatLocationInfo($companyDetails['location']);
            $this->createEmbedding($userAgent, 'company_details', 'location', $content, $companyDetails['location']);
        }

        // Embed social media information
        if (!empty($companyDetails['social_media'])) {
            $content = $this->formatSocialMediaInfo($companyDetails['social_media']);
            $this->createEmbedding($userAgent, 'company_details', 'social_media', $content, $companyDetails['social_media']);
        }
    }

    /**
     * Embed agent business data
     */
    private function embedAgentBusinessData(UserAgent $userAgent, array $businessData): void
    {
        // Embed business services
        if (!empty($businessData['business_services'])) {
            foreach ($businessData['business_services'] as $serviceKey => $service) {
                $content = $this->formatServiceInfo($serviceKey, $service);
                $this->createEmbedding($userAgent, 'business_services', $serviceKey, $content, $service);
            }
        }

        // Embed business specialties
        if (!empty($businessData['business_specialties'])) {
            $content = $this->formatSpecialties($businessData['business_specialties']);
            $this->createEmbedding($userAgent, 'business_specialties', 'all', $content, $businessData['business_specialties']);
        }

        // Embed business hours
        if (!empty($businessData['business_hours'])) {
            $content = $this->formatBusinessHours($businessData['business_hours']);
            $this->createEmbedding($userAgent, 'business_hours', 'schedule', $content, $businessData['business_hours']);
        }

        // Embed scheduling rules
        if (!empty($businessData['scheduling_rules'])) {
            $content = $this->formatSchedulingRules($businessData['scheduling_rules']);
            $this->createEmbedding($userAgent, 'scheduling_rules', 'policies', $content, $businessData['scheduling_rules']);
        }

        // Embed time policy (merged from legacy time slots)
        if (!empty($businessData['scheduling_rules']['time_policy'])) {
            $content = $this->formatTimePolicy($businessData['scheduling_rules']['time_policy']);
            $this->createEmbedding($userAgent, 'time_policy', 'policies', $content, $businessData['scheduling_rules']['time_policy']);
        }

        // Embed weekend settings
        if (!empty($businessData['weekend_settings'])) {
            $content = $this->formatWeekendSettings($businessData['weekend_settings']);
            $this->createEmbedding($userAgent, 'weekend_settings', 'policies', $content, $businessData['weekend_settings']);
        }

        // Embed unavailable dates
        if (!empty($businessData['unavailable_dates'])) {
            $content = $this->formatUnavailableDates($businessData['unavailable_dates']);
            $this->createEmbedding($userAgent, 'unavailable_dates', 'calendar', $content, $businessData['unavailable_dates']);
        }
    }

    /**
     * Create an embedding for the given content
     */
    private function createEmbedding(UserAgent $userAgent, string $contentType, string $contentKey, string $content, array $originalData): void
    {
        try {
            // Generate content hash for change detection
            $contentHash = BusinessEmbedding::generateContentHash($originalData);

            // Check if embedding already exists with same hash (no changes)
            if ($this->businessEmbeddingRepository->existsWithHash(
                $userAgent->id,
                $contentType,
                $contentKey,
                $contentHash
            )) {
                Log::debug('Skipping embedding creation - content unchanged', [
                    'user_agent_id' => $userAgent->id,
                    'content_type'  => $contentType,
                    'content_key'   => $contentKey,
                ]);
                return;
            }

            // Generate embedding using KnowledgeService
            $embedding = $this->knowledgeService->generateEmbedding($content);

            // Create or update embedding record
            $this->businessEmbeddingRepository->updateOrCreate(
                [
                    'user_agent_id' => $userAgent->id,
                    'content_type'  => $contentType,
                    'content_key'   => $contentKey,
                ],
                [
                    'content'      => $content,
                    'embedding'    => $embedding,
                    'metadata'     => [
                        'original_keys'  => array_keys($originalData),
                        'content_length' => strlen($content),
                        'generated_at'   => now()->toISOString(),
                    ],
                    'content_hash' => $contentHash,
                ]
            );

        } catch (Exception $e) {
            Log::error('Failed to create business embedding', [
                'user_agent_id' => $userAgent->id,
                'content_type'  => $contentType,
                'content_key'   => $contentKey,
                'error'         => $e->getMessage(),
                'trace'         => $e->getTraceAsString(),
            ]);
            // Continue processing other embeddings even if one fails
        }
    }

    /**
     * Generate embedding using KnowledgeService (unified embedding logic)
     */
    public function generateEmbedding(string $text): array
    {
        return $this->knowledgeService->generateEmbedding($text);
    }

    /**
     * Clear all existing embeddings for a user agent
     */
    private function clearUserAgentEmbeddings(UserAgent $userAgent): void
    {
        $this->businessEmbeddingRepository->deleteByUserAgent($userAgent);
    }

    /**
     * Format company basic information for embedding
     */
    private function formatCompanyBasicInfo(array $basicInfo): string
    {
        $parts = [];

        if (!empty($basicInfo['name'])) {
            $parts[] = "Company Name: {$basicInfo['name']}";
        }

        if (!empty($basicInfo['type'])) {
            $parts[] = "Business Type: {$basicInfo['type']}";
        }

        if (!empty($basicInfo['description'])) {
            $parts[] = "Description: {$basicInfo['description']}";
        }

        return implode('. ', $parts);
    }

    /**
     * Format contact information for embedding
     */
    private function formatContactInfo(array $contact): string
    {
        $parts = [];

        if (!empty($contact['phone'])) {
            $parts[] = "Phone: {$contact['phone']}";
        }

        if (!empty($contact['email'])) {
            $parts[] = "Email: {$contact['email']}";
        }

        if (!empty($contact['address'])) {
            $parts[] = "Address: {$contact['address']}";
        }

        return "Contact Information: " . implode(', ', $parts);
    }

    /**
     * Format location information for embedding
     */
    private function formatLocationInfo(array $location): string
    {
        $parts = [];

        if (!empty($location['city'])) {
            $parts[] = $location['city'];
        }

        if (!empty($location['state'])) {
            $parts[] = $location['state'];
        }

        if (!empty($location['zip_code'])) {
            $parts[] = $location['zip_code'];
        }

        if (!empty($location['country'])) {
            $parts[] = $location['country'];
        }

        return "Location: " . implode(', ', $parts);
    }

    /**
     * Format social media information for embedding
     */
    private function formatSocialMediaInfo(array $socialMedia): string
    {
        $parts = [];

        foreach ($socialMedia as $platform => $handle) {
            if (!empty($handle)) {
                $parts[] = ucfirst($platform) . ": {$handle}";
            }
        }

        return "Social Media: " . implode(', ', $parts);
    }

    /**
     * Format service information for embedding
     */
    private function formatServiceInfo(string $serviceKey, array $service): string
    {
        $parts = [];

        if (!empty($service['name'])) {
            $parts[] = "Service: {$service['name']}";
        }

        if (!empty($service['description'])) {
            $parts[] = "Description: {$service['description']}";
        }

        if (!empty($service['price'])) {
            $parts[] = "Price: $" . number_format($service['price']);
        }

        if (!empty($service['duration'])) {
            $parts[] = "Duration: {$service['duration']} minutes";
        }

        if (!empty($service['category'])) {
            $parts[] = "Category: {$service['category']}";
        }

        if (isset($service['popular']) && $service['popular']) {
            $parts[] = "This is a popular service";
        }

        return implode('. ', $parts);
    }

    /**
     * Format business specialties for embedding
     */
    private function formatSpecialties(array $specialties): string
    {
        return "Business Specialties: " . implode(', ', $specialties);
    }

    /**
     * Format business hours for embedding
     */
    private function formatBusinessHours(array $hours): string
    {
        $parts = [];

        foreach ($hours as $day => $dayHours) {
            if ($dayHours === null) {
                $parts[] = ucfirst($day) . ": Closed";
            } elseif (is_array($dayHours) && count($dayHours) === 2) {
                $parts[] = ucfirst($day) . ": {$dayHours[0]} - {$dayHours[1]}";
            }
        }

        return "Business Hours: " . implode(', ', $parts);
    }

    /**
     * Format scheduling rules for embedding
     */
    private function formatSchedulingRules(array $rules): string
    {
        $parts = [];

        if (isset($rules['max_bookings_per_day'])) {
            $parts[] = "Maximum {$rules['max_bookings_per_day']} bookings per day";
        }

        if (isset($rules['allow_double_booking']) && !$rules['allow_double_booking']) {
            $parts[] = "Double booking not allowed";
        }

        if (isset($rules['require_customer_confirmation']) && $rules['require_customer_confirmation']) {
            $parts[] = "Customer confirmation required";
        }

        if (isset($rules['default_duration_minutes'])) {
            $parts[] = "Default appointment duration {$rules['default_duration_minutes']} minutes";
        }

        if (isset($rules['max_advance_days'])) {
            $parts[] = "Can book up to {$rules['max_advance_days']} days in advance";
        }

        if (isset($rules['min_advance_hours'])) {
            $parts[] = "Minimum {$rules['min_advance_hours']} hours advance notice";
        }

        if (isset($rules['cancellation_policy']['free_cancellation_hours'])) {
            $hours   = $rules['cancellation_policy']['free_cancellation_hours'];
            $parts[] = "Free cancellation up to {$hours} hours before appointment";
        }

        return "Scheduling Policies: " . implode('. ', $parts);
    }

    /**
     * Format time slots configuration for embedding
     */
    private function formatTimePolicy(array $policy): string
    {
        $parts = [];
        if (isset($policy['max_advance_days'])) {
            $parts[] = "Can book up to {$policy['max_advance_days']} days in advance";
        }
        if (isset($policy['min_advance_hours'])) {
            $parts[] = "Minimum {$policy['min_advance_hours']} hours advance notice required";
        }
        if (isset($policy['default_buffer'])) {
            $parts[] = "Default buffer {$policy['default_buffer']} minutes";
        }
        return "Time Policy: " . implode('. ', $parts);
    }

    /**
     * Format weekend settings for embedding
     */
    private function formatWeekendSettings(array $settings): string
    {
        $parts = [];

        if (isset($settings['saturday_enabled']) && $settings['saturday_enabled']) {
            $parts[] = "Saturday appointments available";
        }

        if (isset($settings['sunday_enabled']) && $settings['sunday_enabled']) {
            $parts[] = "Sunday appointments available";
        } else {
            $parts[] = "Closed on Sundays";
        }

        if (isset($settings['weekend_pricing_multiplier']) && $settings['weekend_pricing_multiplier'] > 1) {
            $multiplier = $settings['weekend_pricing_multiplier'];
            $parts[]    = "Weekend pricing is " . (($multiplier - 1) * 100) . "% higher";
        }

        return "Weekend Policy: " . implode('. ', $parts);
    }

    /**
     * Format unavailable dates for embedding
     */
    private function formatUnavailableDates(array $dates): string
    {
        $parts = [];

        if (!empty($dates['holidays'])) {
            $parts[] = "Closed on holidays: " . implode(', ', $dates['holidays']);
        }

        if (!empty($dates['maintenance_days'])) {
            $parts[] = "Maintenance days: " . implode(', ', $dates['maintenance_days']);
        }

        if (!empty($dates['vacation_periods'])) {
            $parts[] = "Vacation periods: " . implode(', ', $dates['vacation_periods']);
        }

        return "Unavailable Dates: " . implode('. ', $parts);
    }

    /**
     * Get current embedding service status (delegated to KnowledgeService)
     */
    public function getEmbeddingServiceStatus(): array
    {
        return [
            'delegated_to'      => 'KnowledgeService',
            'llm_model'         => config('services.llm_model', 'gemini'),
            'unified_embedding' => true,
            'note'              => 'All embedding generation is now handled by KnowledgeService to eliminate duplication',
        ];
    }

    /**
     * Force refresh of service availability status (no-op, delegated to KnowledgeService)
     */
    public function refreshServiceStatus(): void
    {
        Log::info('BusinessEmbeddingService refresh requested - embedding logic is delegated to KnowledgeService');
    }

    /**
     * Force refresh of Ollama availability status
     * @deprecated Use refreshServiceStatus() instead
     */
    public function refreshOllamaStatus(): void
    {
        $this->refreshServiceStatus();
    }

    /**
     * Search business embeddings for similar content using pgvector
     */
    public function searchBusinessContext(UserAgent $userAgent, string $query, int $limit = 5): array
    {
        try {
            // Generate embedding for query using KnowledgeService
            $embedding = $this->knowledgeService->generateEmbedding($query);

            // Search for similar business embeddings using pgvector
            $results = $this->businessEmbeddingRepository->findSimilar(
                $userAgent->id,
                $embedding,
                $limit
            );

            return $results->map(function ($result) {
                return [
                    'content'      => $result->content,
                    'content_type' => $result->content_type,
                    'content_key'  => $result->content_key,
                    'distance'     => $result->neighbor_distance,
                    'metadata'     => $result->metadata,
                ];
            })->toArray();

        } catch (Exception $e) {
            Log::error('Failed to search business context', [
                'user_agent_id' => $userAgent->id,
                'query'         => $query,
                'error'         => $e->getMessage(),
            ]);

            return [];
        }
    }
}