<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Kortana AI - API Service</title>
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            <style>
        body {
            font-family: 'Instrument Sans', sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
        }

        .container {
            max-width: 800px;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(to right, #38bdf8, #818cf8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .terminal {
            background-color: #1e293b;
            border-radius: 8px;
            padding: 20px;
            width: 100%;
            max-width: 600px;
            text-align: left;
            margin: 2rem auto;
            overflow: hidden;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .terminal-header {
            display: flex;
            margin-bottom: 15px;
        }

        .terminal-button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .red {
            background-color: #f87171;
        }

        .yellow {
            background-color: #fbbf24;
        }

        .green {
            background-color: #34d399;
        }

        .terminal-body {
            color: #a5f3fc;
            font-family: monospace;
        }

        .command {
            color: #f0abfc;
            margin-bottom: 10px;
        }

        .response {
            color: #a5f3fc;
            margin-bottom: 20px;
        }

        .cursor {
            display: inline-block;
            width: 10px;
            height: 18px;
            background-color: #a5f3fc;
            animation: blink 1s step-end infinite;
        }

        .links {
            margin-top: 2rem;
        }

        .link {
            display: inline-block;
            background-color: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s;
            margin: 0 0.5rem;
        }

        .link:hover {
            background-color: #2563eb;
        }

        @keyframes blink {

            from,
            to {
                opacity: 1;
            }

            50% {
                opacity: 0;
            }
        }

        @keyframes typing {
            from {
                width: 0
            }

            to {
                width: 100%
            }
        }

        .typing {
            overflow: hidden;
            white-space: nowrap;
            animation: typing 3s steps(40, end);
        }

        .features {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature {
            background-color: #1e293b;
            border-radius: 8px;
            padding: 1.5rem;
            width: 100%;
            max-width: 250px;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
            </style>
    </head>

<body>
    <div class="container">
        <h1>Kortana AI API Service</h1>
        <p>Looks like you've found our API endpoint! This service provides AI-powered functionality through REST APIs.
        </p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">🤖</div>
                <h3>AI Chatbot</h3>
                <p>Intelligent conversational AI for customer support</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>Social Media</h3>
                <p>Instagram & Facebook integration</p>
            </div>
            <div class="feature">
                <div class="feature-icon">📚</div>
                <h3>Knowledge Base</h3>
                <p>Document processing & semantic search</p>
            </div>
        </div>

        <div class="terminal">
            <div class="terminal-header">
                <div class="terminal-button red"></div>
                <div class="terminal-button yellow"></div>
                <div class="terminal-button green"></div>
            </div>
            <div class="terminal-body">
                <div class="command typing">$ curl -X GET https://kortana-ai.example/api/health</div>
                <div class="response">
                    {<br>
                    &nbsp;&nbsp;"status": "ok",<br>
                    &nbsp;&nbsp;"timestamp": "2023-08-15T14:30:45.123Z",<br>
                    &nbsp;&nbsp;"service": "Instagram Chatbot API"<br>
                    }
                </div>
                <div class="command typing">$ curl -X POST https://kortana-ai.example/api/v1/auth/login -d
                    '{"email":"<EMAIL>","password":"******"}'</div>
                <div class="response">
                    {<br>
                    &nbsp;&nbsp;"success": true,<br>
                    &nbsp;&nbsp;"message": "Login successful",<br>
                    &nbsp;&nbsp;"code": 200,<br>
                    &nbsp;&nbsp;"data": {<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"id": 1,<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"first_name": "John",<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"last_name": "Doe",<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"email": "<EMAIL>",<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"token": "1|laravel_sanctum_token_hash..."<br>
                    &nbsp;&nbsp;}<br>
                    }
                </div>
                <div class="command typing">$ curl -X POST https://kortana-ai.example/api/v1/social/send-message -H
                    "Authorization: Bearer TOKEN" -d
                    '{"platform":"instagram","recipient_id":"123456","message":"Hello!"}'</div>
                <div class="response">
                    {<br>
                    &nbsp;&nbsp;"success": true,<br>
                    &nbsp;&nbsp;"message": "Message sent successfully",<br>
                    &nbsp;&nbsp;"code": 200,<br>
                    &nbsp;&nbsp;"data": {<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"message_id": "mid.123456789",<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"recipient_id": "123456",<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;"timestamp": 1625097600<br>
                    &nbsp;&nbsp;}<br>
                    }
                </div>
                <div class="command">$ _<span class="cursor"></span></div>
            </div>
        </div>

        <p>This is a backend API service. To interact with it, you'll need to make proper API requests with appropriate
            authentication.</p>

        <div class="links">
            <a href="/api/documentation" class="link">API Documentation</a>
            <a href="https://github.com/your-organization/kortana-ai" class="link">GitHub</a>
        </div>
    </div>

    <script>
        // Add some fun terminal animation
        document.addEventListener('DOMContentLoaded', () => {
            const terminalBody = document.querySelector('.terminal-body');

            document.addEventListener('keydown', (e) => {
                if (e.key.length === 1) {
                    const command = document.querySelector('.terminal-body .command:last-child');
                    command.innerHTML = command.innerHTML.replace('<span class="cursor"></span>', e.key +
                        '<span class="cursor"></span>');
                }
            });
        });
    </script>
    </body>

</html>
