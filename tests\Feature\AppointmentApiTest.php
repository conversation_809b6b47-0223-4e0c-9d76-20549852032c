<?php

declare (strict_types = 1);

namespace Tests\Feature;

use App\Models\Agent;
use App\Models\Appointment;
use App\Models\Platform;
use App\Models\Token;
use App\Models\User;
use App\Models\UserAgent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class AppointmentApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private UserAgent $userAgent;
    private Token $token;
    private Platform $platform;
    private Appointment $appointment;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create();

        // Create platform
        $this->platform = Platform::factory()->create([
            'name'    => 'Instagram',
            'key'     => 'instagram',
            'enabled' => true,
        ]);

        // Create agent
        $agent = Agent::factory()->create([
            'name'      => 'Test Agent',
            'key'       => 'test_agent',
            'is_active' => true,
        ]);

        // Create user agent
        $this->userAgent = UserAgent::factory()->create([
            'user_id'  => $this->user->id,
            'agent_id' => $agent->id,
            'name'     => 'Test Assistant',
            'status'   => 'active',
        ]);

        // Create token
        $this->token = Token::factory()->create([
            'user_id'     => $this->user->id,
            'platform_id' => $this->platform->id,
        ]);

        // Create appointment
        $this->appointment = Appointment::factory()->create([
            'customer_name'    => 'John Doe',
            'customer_id'      => 'customer_123',
            'user_id'          => $this->user->id,
            'user_agent_id'    => $this->userAgent->id,
            'token_id'         => $this->token->id,
            'appointment_date' => now()->addDays(7)->format('Y-m-d'),
            'appointment_time' => '14:00:00',
            'service_name'     => 'Test Service',
            'status'           => 'pending',
        ]);

        // Authenticate user
        Sanctum::actingAs($this->user);
    }

    /** @test */
    public function it_can_list_appointments_with_pagination(): void
    {
        $response = $this->getJson('/api/v1/appointments?per_page=15&page=1');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'code',
                'data' => [
                    'items'    => [
                        '*' => [
                            'id',
                            'customer',
                            'appointment',
                            'service',
                            'status',
                        ],
                    ],
                    'paginate' => [
                        'current_page',
                        'per_page',
                        'total',
                    ],
                ],
            ]);
    }

    /** @test */
    public function it_can_search_appointments(): void
    {
        $response = $this->getJson('/api/v1/appointments?type=search&search=John&per_page=15');

        $response->assertStatus(200)
            ->assertJsonPath('data.items.0.customer.name', 'John Doe');
    }

    /** @test */
    public function it_can_filter_appointments_by_status(): void
    {
        $response = $this->getJson('/api/v1/appointments?status=pending&per_page=15');

        $response->assertStatus(200);

        $appointments = $response->json('data.items');
        foreach ($appointments as $appointment) {
            $this->assertEquals('pending', $appointment['status']);
        }
    }

    /** @test */
    public function it_can_filter_appointments_by_platform(): void
    {
        $response = $this->getJson('/api/v1/appointments?platform=instagram&per_page=15');

        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_get_todays_appointments(): void
    {
        // Create today's appointment
        Appointment::factory()->create([
            'user_id'          => $this->user->id,
            'user_agent_id'    => $this->userAgent->id,
            'appointment_date' => now()->format('Y-m-d'),
            'appointment_time' => '10:00:00',
        ]);

        $response = $this->getJson('/api/v1/appointments?type=today');

        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_get_calendar_view(): void
    {
        $response = $this->getJson('/api/v1/appointments?type=calendar&month=8&year=2025');

        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_get_appointment_statistics(): void
    {
        $response = $this->getJson('/api/v1/appointments?type=stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'total',
                    'status_counts',
                    'today',
                    'upcoming',
                    'this_month',
                ],
            ]);
    }

    /** @test */
    public function it_can_create_appointment(): void
    {
        $appointmentData = [
            'customer_name'    => 'Jane Smith',
            'customer_id'      => 'customer_456',
            'customer_phone'   => '+1234567890',
            'customer_email'   => '<EMAIL>',
            'user_agent_id'    => $this->userAgent->id,
            'token_id'         => $this->token->id,
            'appointment_date' => now()->addDays(10)->format('Y-m-d'),
            'appointment_time' => '15:00',
            'duration_minutes' => 120,
            'service_name'     => 'New Service',
            'service_price'    => 150.00,
            'notes'            => 'Test appointment creation',
        ];

        $response = $this->postJson('/api/v1/appointments', $appointmentData);

        $response->assertStatus(201)
            ->assertJsonPath('data.customer.name', 'Jane Smith');

        $this->assertDatabaseHas('appointments', [
            'customer_name' => 'Jane Smith',
            'customer_id'   => 'customer_456',
        ]);
    }

    /** @test */
    public function it_can_show_specific_appointment(): void
    {
        $response = $this->getJson("/api/v1/appointments/{$this->appointment->id}");

        $response->assertStatus(200)
            ->assertJsonPath('data.id', $this->appointment->id)
            ->assertJsonPath('data.customer.name', 'John Doe');
    }

    /** @test */
    public function it_can_update_appointment(): void
    {
        $updateData = [
            'customer_name' => 'John Smith',
            'notes'         => 'Updated notes',
            'service_price' => 200.00,
        ];

        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonPath('data.customer.name', 'John Smith');

        $this->assertDatabaseHas('appointments', [
            'id'            => $this->appointment->id,
            'customer_name' => 'John Smith',
            'notes'         => 'Updated notes',
        ]);
    }

    /** @test */
    public function it_can_confirm_appointment(): void
    {
        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}/status", [
            'status' => 'confirmed',
            'notes'  => 'Confirmed via phone call',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.status', 'confirmed');

        $this->assertDatabaseHas('appointments', [
            'id'     => $this->appointment->id,
            'status' => 'confirmed',
        ]);
    }

    /** @test */
    public function it_can_cancel_appointment(): void
    {
        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}/status", [
            'status' => 'cancelled',
            'notes'  => 'Customer requested cancellation',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.status', 'cancelled');
    }

    /** @test */
    public function it_can_complete_appointment(): void
    {
        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}/status", [
            'status' => 'completed',
            'notes'  => 'Service completed successfully',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.status', 'completed');
    }

    /** @test */
    public function it_can_mark_appointment_as_no_show(): void
    {
        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}/status", [
            'status' => 'no_show',
            'notes'  => 'Customer did not show up',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.status', 'no_show');
    }

    /** @test */
    public function it_can_set_appointment_in_progress(): void
    {
        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}/status", [
            'status' => 'in_progress',
            'notes'  => 'Service has started',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('data.status', 'in_progress');
    }

    /** @test */
    public function it_can_delete_appointment(): void
    {
        $response = $this->deleteJson("/api/v1/appointments/{$this->appointment->id}");

        $response->assertStatus(200);

        $this->assertDatabaseMissing('appointments', [
            'id' => $this->appointment->id,
        ]);
    }

    /** @test */
    public function it_can_get_available_statuses(): void
    {
        $response = $this->getJson('/api/v1/appointments/meta/statuses');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [],
            ]);

        $statuses         = $response->json('data');
        $expectedStatuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'];

        foreach ($expectedStatuses as $status) {
            $this->assertContains($status, $statuses);
        }
    }

    /** @test */
    public function it_validates_appointment_creation_data(): void
    {
        $invalidData = [
            'customer_name'    => '', // Required field empty
            'appointment_date' => 'invalid-date',
            'appointment_time' => 'invalid-time',
        ];

        $response = $this->postJson('/api/v1/appointments', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customer_name', 'appointment_date', 'appointment_time']);
    }

    /** @test */
    public function it_validates_status_update_data(): void
    {
        $invalidData = [
            'status' => 'invalid_status', // Invalid status
        ];

        $response = $this->putJson("/api/v1/appointments/{$this->appointment->id}/status", $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['status']);
    }

    /** @test */
    public function it_can_combine_multiple_filters(): void
    {
        $response = $this->getJson('/api/v1/appointments?status=pending&platform=instagram&per_page=20');

        $response->assertStatus(200);
    }

    /** @test */
    public function it_handles_invalid_appointment_id(): void
    {
        $response = $this->getJson('/api/v1/appointments/99999');

        $response->assertStatus(404);
    }

    /** @test */
    public function it_requires_authentication_for_appointments(): void
    {
        // Create a new test instance without authentication
        $this->refreshApplication();

        // Make an unauthenticated request
        $response = $this->getJson('/api/v1/appointments');

        $response->assertStatus(401);
    }
}