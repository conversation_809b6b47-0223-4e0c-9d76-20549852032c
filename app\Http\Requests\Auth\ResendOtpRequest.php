<?php

declare (strict_types = 1);

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;

final class ResendOtpRequest extends BaseFormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required', 'email', 'max:255'],
            'state_code' => ['required', 'string', 'size:6'],
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Email is required',
            'email.email'    => 'Please provide a valid email address',
            'email.max'      => 'Email cannot exceed 255 characters',
            'state_code.required' => 'State code is required',
            'state_code.string' => 'State code must be a string',
            'state_code.size' => 'State code must be exactly 6 characters',
        ];
    }
}
