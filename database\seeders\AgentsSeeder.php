<?php

namespace Database\Seeders;

use App\Models\Agent;
use App\Models\User;
use App\Models\UserAgent;
use Illuminate\Database\Seeder;

class AgentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 10 base agent types
        $appointmentAgent       = $this->createAppointmentBookingAgent();
        $salesAgent             = $this->createSalesAgent();
        $supportAgent           = $this->createCustomerSupportAgent();
        $marketingAgent         = $this->createMarketingAgent();
        $consultationAgent      = $this->createConsultationAgent();
        $receptionistAgent      = $this->createReceptionistAgent();
        $ecommerceAgent         = $this->createEcommerceAgent();
        $leadQualificationAgent = $this->createLeadQualificationAgent();
        $socialMediaAgent       = $this->createSocialMediaAgent();
        $hrAgent                = $this->createHRAgent();

        // Get all agents for user subscriptions
        $agents = [
            $appointmentAgent,
            $salesAgent,
            $supportAgent,
            $marketingAgent,
            $consultationAgent,
            $receptionistAgent,
            $ecommerceAgent,
            $leadQualificationAgent,
            $socialMediaAgent,
            $hrAgent,
        ];

        // Create user agent subscriptions for admin user only (5 agents)
        $user = User::where('email', '<EMAIL>')->first();

        if ($user) {
            // Always subscribe to appointment agent first (active)
            $this->createUserAgentSubscription($user, $appointmentAgent);

            // Get remaining agents (excluding appointment agent)
            $remainingAgents = collect($agents)->filter(function ($agent) use ($appointmentAgent) {
                return $agent->id !== $appointmentAgent->id;
            })->shuffle()->take(4); // Take 4 more to make total of 5

            foreach ($remainingAgents as $index => $agent) {
                // First remaining agent is active, rest are inactive
                $status = $index === 0 ? 'active' : 'inactive';
                $this->createUserAgentSubscription($user, $agent);
            }

            // Note: Business embeddings will be created by BusinessEmbeddingSeeder
            // This ensures all user agents are created before embeddings are generated
        }
    }

    /**
     * Create an appointment booking agent
     */
    private function createAppointmentBookingAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Appointment Booking Assistant',
            'key'                      => 'appointment_booking',
            'description'              => 'AI assistant specialized in scheduling appointments, managing calendars, and coordinating services with customers.',
            'default_capabilities'     => json_encode([
                'schedule_appointments',
                'check_availability',
                'reschedule_appointments',
                'cancel_appointments',
                'send_reminders',
                'provide_service_information',
                'handle_pricing_inquiries',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('appointment_booking')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your {agent_type} assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create a sales agent
     */
    private function createSalesAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Sales Assistant',
            'key'                      => 'sales',
            'description'              => 'AI sales representative focused on lead qualification, product demonstrations, and closing deals.',
            'default_capabilities'     => json_encode([
                'qualify_leads',
                'product_demonstration',
                'handle_objections',
                'generate_quotes',
                'schedule_follow_ups',
                'upsell_cross_sell',
                'track_sales_pipeline',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('sales')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hi! I\'m your sales assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create a customer support agent
     */
    private function createCustomerSupportAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Customer Support Assistant',
            'key'                      => 'customer_support',
            'description'              => '24/7 customer service agent for issue resolution, product information, and general support.',
            'default_capabilities'     => json_encode([
                'resolve_issues',
                'provide_product_info',
                'track_orders',
                'process_returns',
                'escalate_complex_issues',
                'collect_feedback',
                'update_account_info',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('customer_support')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m here to help. How can I assist you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create a marketing agent
     */
    private function createMarketingAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Marketing Assistant',
            'key'                      => 'marketing',
            'description'              => 'AI marketing specialist for campaign management, content creation, and customer engagement.',
            'default_capabilities'     => json_encode([
                'create_campaigns',
                'generate_content',
                'analyze_audience',
                'track_engagement',
                'a_b_test_content',
                'social_media_management',
                'email_marketing',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('marketing')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your marketing assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create a consultation agent
     */
    private function createConsultationAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Business Consultation Assistant',
            'key'                      => 'consultation',
            'description'              => 'Expert consultation agent for business advice, strategy planning, and professional guidance.',
            'default_capabilities'     => json_encode([
                'business_analysis',
                'strategy_planning',
                'market_research',
                'financial_planning',
                'risk_assessment',
                'growth_planning',
                'industry_insights',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('consultation')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your business consultation assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create a user agent subscription
     */
    private function createUserAgentSubscription(User $user, Agent $agent): void
    {
        $customName  = $this->getCustomAgentName($agent->key);
        $personality = $this->getPersonalityForAgent($agent->key);

        // Get business-specific data for this agent type
        $businessProfile = $this->getAgentBusinessData($agent->key);

        UserAgent::create([
            'user_id'                 => $user->id,
            'agent_id'                => $agent->id,
            'name'                    => $customName,
            'status'                  => 'active',
            'personality'             => $personality,
            'agent_gender'            => 'female',
            'customize_configuration' => json_encode($this->getUniversalConfigurationForAgent($agent->key)),
            'prompt_templates'        => json_encode($this->getCustomPromptTemplates($agent->key)),
            'company_details'         => json_encode($this->getCompanyDetails()),
            'agent_business_data'     => json_encode($businessProfile),
            'notification_settings'   => json_encode([
                'email_notifications' => true,
                'sms_notifications'   => false,
                'push_notifications'  => true,
            ]),
            'analytics_settings'      => json_encode([
                'track_conversations' => true,
                'track_performance'   => true,
                'generate_reports'    => true,
            ]),
            'performance_targets'     => json_encode([
                'satisfaction_goal' => 4.5,
                'resolution_goal'   => 0.9,
            ]),
            'escalation_rules'        => json_encode([
                'auto_escalate_after_minutes' => 10,
                'escalate_on_keywords'        => ['urgent', 'emergency', 'complaint'],
                'escalate_to_human'           => true,
            ]),
        ]);
    }

    /**
     * Get custom agent name based on type
     */
    private function getCustomAgentName(string $agentKey): string
    {
        $names = [
            'appointment_booking' => 'Sarah - Booking Assistant',
            'sales'               => 'Alex - Sales Representative',
            'customer_support'    => 'Emma - Support Specialist',
            'marketing'           => 'Jordan - Marketing Expert',
            'consultation'        => 'Dr. Morgan - Business Consultant',
            'receptionist'        => 'Maya - Virtual Receptionist',
            'ecommerce'           => 'Chris - E-commerce Assistant',
            'lead_qualification'  => 'Taylor - Lead Specialist',
            'social_media'        => 'Riley - Social Media Manager',
            'hr'                  => 'Sam - HR Assistant',
        ];

        return $names[$agentKey] ?? 'AI Assistant';
    }

    /**
     * Get personality based on agent type
     */
    private function getPersonalityForAgent(string $agentKey): string
    {
        $personalities = [
            'appointment_booking' => 'professional',
            'sales'               => 'enthusiastic',
            'customer_support'    => 'helpful',
            'marketing'           => 'friendly',
            'consultation'        => 'formal',
            'receptionist'        => 'professional',
            'ecommerce'           => 'helpful',
            'lead_qualification'  => 'enthusiastic',
            'social_media'        => 'casual',
            'hr'                  => 'formal',
        ];

        return $personalities[$agentKey] ?? 'professional';
    }

    /**
     * Get universal configuration for agent (combines base config with agent-specific settings)
     */
    private function getUniversalConfigurationForAgent(string $agentKey): array
    {
        $baseConfig = [
            'language'                          => 'en',
            'timezone'                          => 'UTC',
            'max_conversation_length'           => 50,
            'context_retention_minutes'         => 30,
            'response_time_target'              => $this->getResponseTimeTarget($agentKey),
            // Customer information collection settings (simplified)
            'customer_info_collection_settings' => [
                'name'    => true, // Always collect and require name
                'phone'   => true, // Collect phone and require it
                'email'   => true, // Collect email and require it
                'address' => true, // Collect address and require it
            ],
        ];

        $specificConfigs = [
            'appointment_booking' => [
                'booking_confirmation_required' => true,
                'send_reminder_hours'           => 24,
                'allow_same_day_booking'        => false,
            ],
            'sales'               => [
                'lead_scoring_enabled'       => true,
                'follow_up_frequency_days'   => 3,
                'discount_threshold_percent' => 15,
            ],
            'customer_support'    => [
                'ticket_creation_enabled'     => true,
                'escalation_timeout_minutes'  => 10,
                'satisfaction_survey_enabled' => true,
            ],
            'marketing'           => [
                'content_approval_required'  => true,
                'campaign_analytics_enabled' => true,
                'social_media_integration'   => true,
            ],
            'consultation'        => [
                'session_recording_enabled' => false,
                'report_generation_enabled' => true,
                'follow_up_required'        => true,
            ],
            'receptionist'        => [
                'call_transfer_enabled' => true,
                'visitor_log_enabled'   => true,
            ],
            'ecommerce'           => [
                'recommendation_engine'   => true,
                'inventory_integration'   => true,
                'return_policy_days'      => 30,
                'free_shipping_threshold' => 50,
            ],
            'lead_qualification'  => [
                'qualification_threshold' => 70,
                'follow_up_schedule'      => [1, 3, 7, 14, 30],
            ],
            'social_media'        => [
                'moderation_enabled' => true,
                'hashtag_limit'      => 10,
            ],
            'hr'                  => [
                'confidentiality_level'  => 'high',
                'policy_database_access' => true,
                'employee_self_service'  => true,
            ],
        ];

        return array_merge($baseConfig, $specificConfigs[$agentKey] ?? []);
    }

    /**
     * Get custom prompt templates based on agent type
     */
    private function getCustomPromptTemplates(string $agentKey): array
    {
        // Get the agent name for personalized greetings
        $agentName = $this->getCustomAgentName($agentKey);

        // Agent-specific templates with personalized names
        $agentTemplates = [
            'appointment_booking' => [
                'greeting' => "Hi! I'm {$agentName}. I can help you schedule appointments for vehicle wrapping, paint protection, window tinting, and other services. What can I help you with today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'sales'               => [
                'greeting' => "Hi! I'm {$agentName}. I'm here to help you with our automotive services and answer any questions you might have. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'customer_support'    => [
                'greeting' => "Hi! I'm {$agentName}. I'm here to help you with any questions or issues you might have. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'marketing'           => [
                'greeting' => "Hi! I'm {$agentName}. I can help you with our marketing services and promotional offers. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'consultation'        => [
                'greeting' => "Hi! I'm {$agentName}. I'm here to provide professional consultation for your automotive needs. How can I help you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'receptionist'        => [
                'greeting' => "Hi! I'm {$agentName}. Welcome to Premium Auto Wraps & Graphics. How can I help you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'ecommerce'           => [
                'greeting' => "Hi! I'm {$agentName}. I can help you with our online services and product information. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'lead_qualification'  => [
                'greeting' => "Hi! I'm {$agentName}. I'm here to help you find the perfect automotive solution for your needs. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'social_media'        => [
                'greeting' => "Hi! I'm {$agentName}. I'm here to help you with our social media presence and services. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
            'hr'                  => [
                'greeting' => "Hi! I'm {$agentName}. I'm here to help you with HR-related questions and support. How can I assist you today?",
                'fallback' => "I'm not sure I understood. Could you please rephrase that?",
            ],
        ];

        return $agentTemplates[$agentKey] ?? [
            'greeting' => "Hi! I'm {$agentName}. How can I help you today?",
            'fallback' => "I'm not sure I understood. Could you please rephrase that?",
        ];
    }

    // Removed getTrainingDataSources method - using knowledge_files/chunks system instead

    /**
     * Get response time target
     */
    private function getResponseTimeTarget(string $agentKey): int
    {
        $targets = [
            'appointment_booking' => 15,
            'sales'               => 30,
            'customer_support'    => 10,
            'marketing'           => 45,
            'consultation'        => 60,
            'receptionist'        => 20,
            'ecommerce'           => 25,
            'lead_qualification'  => 35,
            'social_media'        => 60,
            'hr'                  => 40,
        ];

        return $targets[$agentKey] ?? 30;
    }

    /**
     * Get company details
     */
    private function getCompanyDetails(): array
    {
        return [
            'name'         => 'Premium Auto Wraps & Graphics',
            'type'         => 'automotive_services',
            'description'  => 'Professional vehicle wrapping, paint protection, and graphics services for cars, trucks, and commercial fleets',
            'contact'      => [
                'phone'   => '******-AUTO-WRAP',
                'email'   => '<EMAIL>',
                'address' => '456 Auto Plaza, Suite 200, Vehicle City, VC 67890',
            ],
            'social_media' => [
                'website'   => 'https://premiumautowraps.com',
                'facebook'  => '@premiumautowraps',
                'instagram' => '@premiumautowraps',
                'linkedin'  => '@premiumautowraps',
            ],
            'location'     => [
                'city'     => 'Vehicle City',
                'state'    => 'VC',
                'zip_code' => '67890',
                'country'  => 'USA',
            ],
        ];
    }

    /**
     * Get business services based on agent
     */
    private function getBusinessServices(string $agentKey): array
    {
        if ($agentKey === 'appointment_booking') {
            return [
                'car_wrap'         => [
                    'name'        => 'Full Vehicle Wrap',
                    'duration'    => 240,
                    'price'       => 2500.00,
                    'description' => 'Complete vehicle wrap with premium vinyl',
                    'category'    => 'wrapping',
                    'popular'     => true,
                ],
                'partial_wrap'     => [
                    'name'        => 'Partial Vehicle Wrap',
                    'duration'    => 120,
                    'price'       => 1200.00,
                    'description' => 'Selective area wrapping for branding',
                    'category'    => 'wrapping',
                    'popular'     => false,
                ],
                'paint_protection' => [
                    'name'        => 'Paint Protection Film',
                    'duration'    => 180,
                    'price'       => 1800.00,
                    'description' => 'Clear protective film for vehicle paint',
                    'category'    => 'protection',
                    'popular'     => true,
                ],
                'window_tinting'   => [
                    'name'        => 'Window Tinting',
                    'duration'    => 90,
                    'price'       => 400.00,
                    'description' => 'Professional window tinting service',
                    'category'    => 'tinting',
                    'popular'     => false,
                ],
                'consultation'     => [
                    'name'        => 'Design Consultation',
                    'duration'    => 60,
                    'price'       => 100.00,
                    'description' => 'Professional design and planning session',
                    'category'    => 'consultation',
                    'popular'     => false,
                ],
            ];
        }

        // Default services for other agents
        return [
            'consultation'     => [
                'name'        => 'Professional Consultation',
                'duration'    => 60,
                'price'       => 150.00,
                'description' => 'Expert advice and strategic planning',
            ],
            'standard_service' => [
                'name'        => 'Standard Service Package',
                'duration'    => 120,
                'price'       => 300.00,
                'description' => 'Comprehensive service delivery',
            ],
            'premium_service'  => [
                'name'        => 'Premium Service Package',
                'duration'    => 180,
                'price'       => 500.00,
                'description' => 'Premium service with extended support',
            ],
        ];
    }

    /**
     * Get business specialties
     */
    private function getBusinessSpecialties(string $agentKey): array
    {
        if ($agentKey === 'appointment_booking') {
            return [
                'Vehicle Wrapping',
                'Paint Protection',
                'Window Tinting',
                'Commercial Graphics',
                'Fleet Branding',
                'Custom Design',
                'Installation Services',
                'Maintenance & Care',
            ];
        }

        return [
            'Professional Consultation',
            'Strategic Planning',
            'Customer Service Excellence',
            'Business Process Optimization',
            'Digital Transformation',
        ];
    }

    /**
     * Get business hours
     */
    private function getBusinessHours(): array
    {
        return [
            'monday'    => ['08:00', '18:00'],
            'tuesday'   => ['08:00', '18:00'],
            'wednesday' => ['08:00', '18:00'],
            'thursday'  => ['08:00', '18:00'],
            'friday'    => ['08:00', '18:00'],
            'saturday'  => ['09:00', '17:00'],
            'sunday'    => null,
        ];
    }

    // Removed: time slots configuration is merged into scheduling rules (time_policy). Kept removed to avoid duplication.

    /**
     * Get scheduling rules for flexible booking
     */
    private function getSchedulingRules(): array
    {
        return [
            'max_bookings_per_day'          => 20,
            'max_concurrent_bookings'       => 3,
            'max_advance_days'              => 60,
            'min_advance_hours'             => 24,
            'default_durations'             => 120,
            'require_customer_confirmation' => true,
            'send_reminders'                => true,
            'reminder_schedule'             => [
                '24_hours_before'   => true,
                '2_hours_before'    => true,
                '15_minutes_before' => false,
            ],
            'cancellation_policy'           => [
                'free_cancellation_hours' => 24,
                'late_cancellation_fee'   => 50.00,
            ],
        ];
    }

    /**
     * Get weekend settings
     */
    private function getWeekendSettings(): array
    {
        return [
            'saturday_enabled'           => true,
            'sunday_enabled'             => false,
            'weekend_pricing_multiplier' => 1.1, // 10% premium for Saturday appointments
        ];
    }

    /**
     * Get unavailable dates
     */
    private function getUnavailableDates(): array
    {
        return [
            'holidays'         => [
                '2024-12-25', // Christmas
                '2024-01-01', // New Year
                '2024-07-04', // Independence Day
                '2024-11-28', // Thanksgiving
                '2024-05-27', // Memorial Day
                '2024-09-02', // Labor Day
                '2024-11-11', // Veterans Day
            ],
            'maintenance_days' => [
                '2024-01-15', // Equipment maintenance
                '2024-03-20', // Facility cleaning
                '2024-06-10', // System updates
                '2024-09-15', // Annual maintenance
            ],
            'vacation_periods' => [
                '2024-07-15', // Summer vacation
                '2024-07-16',
                '2024-07-17',
                '2024-07-18',
                '2024-07-19',
                '2024-12-23', // Holiday break
                '2024-12-24',
            ],
        ];
    }

    /**
     * Get analytics settings
     */
    private function getAnalyticsSettings(): array
    {
        return [
            'track_conversations'  => true,
            'track_response_times' => true,
            'track_satisfaction'   => true,
            'generate_reports'     => true,
        ];
    }

    /**
     * Get notification settings
     */
    private function getNotificationSettings(): array
    {
        return [
            'email_notifications'    => true,
            'sms_notifications'      => false,
            'push_notifications'     => true,
            'notification_frequency' => 'immediate',
        ];
    }

    /**
     * Get performance targets
     */
    private function getPerformanceTargets(string $agentKey): array
    {
        return [
            'response_time_seconds' => 30,
            'satisfaction_score'    => 4.5,
            'resolution_rate'       => 0.85,
            'daily_interactions'    => 100,
        ];
    }

    /**
     * Get escalation rules
     */
    private function getEscalationRules(string $agentKey): array
    {
        return [
            'escalate_after_attempts' => 3,
            'escalation_keywords'     => ['manager', 'supervisor', 'complaint', 'refund'],
            'escalation_email'        => '<EMAIL>',
            'escalation_phone'        => '******-MANAGER',
        ];
    }

    /**
     * Create Receptionist Agent
     */
    private function createReceptionistAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Virtual Receptionist',
            'key'                      => 'receptionist',
            'description'              => 'AI receptionist that handles front desk operations, greets visitors, manages calls, and provides general information.',
            'default_capabilities'     => json_encode([
                'greet_visitors',
                'handle_phone_calls',
                'transfer_calls',
                'take_messages',
                'provide_directions',
                'manage_visitor_log',
                'basic_information',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('receptionist')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your receptionist assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create E-commerce Agent
     */
    private function createEcommerceAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'E-commerce Assistant',
            'key'                      => 'ecommerce',
            'description'              => 'AI assistant specialized in online sales, product recommendations, order management, and customer shopping experience.',
            'default_capabilities'     => json_encode([
                'product_recommendations',
                'order_tracking',
                'inventory_check',
                'shopping_cart_assistance',
                'payment_support',
                'return_processing',
                'wishlist_management',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('ecommerce')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your e-commerce assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create Lead Qualification Agent
     */
    private function createLeadQualificationAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Lead Qualification Specialist',
            'key'                      => 'lead_qualification',
            'description'              => 'AI agent focused on qualifying potential customers, scoring leads, and nurturing prospects through the sales funnel.',
            'default_capabilities'     => json_encode([
                'lead_scoring',
                'qualification_questions',
                'budget_assessment',
                'timeline_evaluation',
                'decision_maker_identification',
                'competitor_analysis',
                'nurture_campaigns',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('lead_qualification')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your lead qualification assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create Social Media Agent
     */
    private function createSocialMediaAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'Social Media Manager',
            'key'                      => 'social_media',
            'description'              => 'AI assistant for managing social media presence, engaging with followers, and handling social customer service.',
            'default_capabilities'     => json_encode([
                'respond_to_comments',
                'manage_direct_messages',
                'content_moderation',
                'engagement_tracking',
                'hashtag_suggestions',
                'post_scheduling',
                'crisis_management',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('social_media')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your social media assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Create HR Agent
     */
    private function createHRAgent(): Agent
    {
        return Agent::create([
            'name'                     => 'HR Assistant',
            'key'                      => 'hr',
            'description'              => 'AI assistant for human resources tasks including employee inquiries, policy information, and basic HR support.',
            'default_capabilities'     => json_encode([
                'policy_information',
                'benefits_explanation',
                'leave_requests',
                'employee_onboarding',
                'performance_reminders',
                'compliance_guidance',
                'employee_directory',
            ]),
            'default_configuration'    => json_encode($this->getUniversalConfigurationForAgent('hr')),
            'default_prompt_templates' => json_encode([
                'greeting' => 'Hello! I\'m your HR assistant. How can I help you today?',
                'fallback' => 'I\'m not sure I understood. Could you please rephrase that?',
            ]),
            'is_active'                => true,
        ]);
    }

    /**
     * Get agent-specific business data based on its key.
     */
    private function getAgentBusinessData(string $agentKey): array
    {
        // Add agent-specific business data only (basic info is in company_details)
        switch ($agentKey) {
            case 'appointment_booking':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                    // time_slots_config removed in favor of flexible scheduling rules
                    'weekend_settings'     => $this->getWeekendSettings(),
                    'unavailable_dates'    => $this->getUnavailableDates(),
                    'scheduling_rules'     => $this->getSchedulingRules(),
                    'business_hours'       => $this->getBusinessHours(),
                ];
            case 'sales':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'customer_support':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'marketing':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'consultation':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'receptionist':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'ecommerce':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'lead_qualification':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'social_media':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            case 'hr':
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
            default:
                return [
                    'business_services'    => $this->getBusinessServices($agentKey),
                    'business_specialties' => $this->getBusinessSpecialties($agentKey),
                ];
        }
    }
}