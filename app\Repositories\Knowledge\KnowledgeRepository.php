<?php

declare (strict_types = 1);

namespace App\Repositories\Knowledge;

use App\Models\KnowledgeChunk;
use App\Models\KnowledgeFile;
use App\Repositories\Knowledge\KnowledgeRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Pgvector\Laravel\Distance;

final class KnowledgeRepository implements KnowledgeRepositoryInterface
{
    // File operations
    public function createFile(array $data): KnowledgeFile
    {
        return KnowledgeFile::create($data);
    }

    public function findFileById(int $id): ?KnowledgeFile
    {
        return KnowledgeFile::find($id);
    }

    public function updateFile(KnowledgeFile $file, array $data): KnowledgeFile
    {
        $file->update($data);
        return $file->fresh();
    }

    public function deleteFile(KnowledgeFile $file): bool
    {
        return $file->delete();
    }

    public function getCompletedFiles(): Collection
    {
        return KnowledgeFile::completed()->get();
    }

    public function getFilesByType(string $type): Collection
    {
        return KnowledgeFile::byType($type)->get();
    }

    public function getAllFiles(int $perPage = 15): LengthAwarePaginator
    {
        return KnowledgeFile::orderBy('created_at', 'desc')->paginate($perPage);
    }

    // Chunk operations
    public function createChunk(array $data): KnowledgeChunk
    {
        return KnowledgeChunk::create($data);
    }

    public function createChunks(array $chunks): Collection
    {
        $results = collect();
        foreach ($chunks as $chunkData) {
            $results->push($this->createChunk($chunkData));
        }
        return $results;
    }

    public function findChunkById(int $id): ?KnowledgeChunk
    {
        return KnowledgeChunk::find($id);
    }

    public function getChunksForFile(int $fileId): Collection
    {
        return KnowledgeChunk::where('knowledge_file_id', $fileId)->orderBy('chunk_index')->get();
    }

    /**
     * Find similar chunks using vector similarity search
     */
    public function findSimilarChunks(array $embedding, int $limit = 5): Collection
    {
        return KnowledgeChunk::query()
            ->nearestNeighbors('embedding', $embedding, Distance::Cosine)
            ->limit($limit)
            ->get();
    }

    /**
     * Find similar chunks with minimum similarity threshold
     */
    public function findSimilarChunksWithThreshold(
        array $embedding,
        float $threshold = 0.8,
        int $limit = 5
    ): Collection {
        return KnowledgeChunk::query()
            ->nearestNeighbors('embedding', $embedding, Distance::Cosine)
            ->limit($limit)
            ->get()
            ->filter(function ($chunk) use ($threshold) {
                return $chunk->neighbor_distance <= $threshold;
            });
    }

    /**
     * Find similar chunks for a specific user agent
     */
    public function findSimilarChunksForUserAgent(int $userAgentId, array $embedding, int $limit = 5): Collection
    {
        return KnowledgeChunk::query()
            ->with('knowledgeFile')
            ->whereHas('knowledgeFile', function ($query) use ($userAgentId) {
                $query->where('user_agent_id', $userAgentId);
            })
            ->nearestNeighbors('embedding', $embedding, Distance::Cosine)
            ->limit($limit)
            ->get();
    }

    /**
     * Get all chunks with their files for context
     */
    public function getChunksWithFiles(): Collection
    {
        return KnowledgeChunk::with('knowledgeFile')->get();
    }

    /**
     * Delete all chunks for a specific file
     */
    public function deleteChunksForFile(int $fileId): bool
    {
        return KnowledgeChunk::where('knowledge_file_id', $fileId)->delete();
    }

    /**
     * Update chunk embedding
     */
    public function updateChunkEmbedding(KnowledgeChunk $chunk, array $embedding): KnowledgeChunk
    {
        $chunk->update(['embedding' => $embedding]);
        return $chunk->fresh();
    }

    /**
     * Get chunk count for a file
     */
    public function getChunkCountForFile(int $fileId): int
    {
        return KnowledgeChunk::where('knowledge_file_id', $fileId)->count();
    }

    /**
     * Mark file processing as completed
     */
    public function markFileAsCompleted(KnowledgeFile $file): KnowledgeFile
    {
        return $this->updateFile($file, ['status' => 'completed']);
    }

    /**
     * Mark file processing as failed
     */
    public function markFileAsFailed(KnowledgeFile $file, string $errorMessage): KnowledgeFile
    {
        return $this->updateFile($file, [
            'status' => 'failed',
        ]);
    }

    /**
     * Get knowledge base statistics
     */
    public function getKnowledgeStats(): array
    {
        return [
            'total_files'      => KnowledgeFile::count(),
            'completed_files'  => KnowledgeFile::where('status', 'completed')->count(),
            'processing_files' => KnowledgeFile::where('status', 'processing')->count(),
            'failed_files'     => KnowledgeFile::where('status', 'failed')->count(),
            'total_chunks'     => KnowledgeChunk::count(),
            'files_by_type'    => KnowledgeFile::groupBy('type')
                ->selectRaw('type, count(*) as count')
                ->pluck('count', 'type')
                ->toArray(),
        ];
    }
}