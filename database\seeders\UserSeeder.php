<?php

declare (strict_types = 1);

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

final class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name'        => 'Admin',
                'last_name'         => 'User',
                'phone'             => '************',
                'address'           => 'Admin HQ',
                'company'           => 'AdminCorp',
                'timezone'          => 'UTC',
                'about'             => 'Superuser account',
                'email_verified_at' => now(),
                'password'          => Hash::make('Pa$$w0rd!'), // Use a secure password!
                'remember_token'    => null,
            ]
        );
    }
}