<?php

declare (strict_types = 1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instagram_tokens', function (Blueprint $table) {
            $table->id();
            $table->string('app_scope_id')->index();
            $table->string('ig_id')->nullable()->index();
            $table->string('username')->nullable();
            $table->string('name')->nullable();
            $table->string('account_type')->nullable();
            $table->text('profile_picture_url')->nullable();
            $table->integer('followers_count')->nullable();
            $table->integer('follows_count')->nullable();
            $table->integer('media_count')->nullable();

            $table->text('access_token');
            $table->string('token_type')->default('bearer');
            $table->timestamp('expires_at')->nullable()->index();
            $table->enum('status', ['active', 'expired', 'revoked'])->default('active')->index();
            $table->boolean('is_active')->default(true);

            $table->json('scopes')->nullable();
            $table->boolean('has_business_permission')->default(false);

            $table->string('page_id')->index()->nullable();

            $table->timestamps();

            $table->index(['ig_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instagram_tokens');
    }
};