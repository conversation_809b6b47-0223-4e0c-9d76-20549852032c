<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\BusinessEmbedding;
use App\Models\UserAgent;
use App\Services\BusinessEmbeddingService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

final class BusinessEmbeddingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting Business Embedding Seeder...');

        // Get all user agents that have business data
        $userAgents = UserAgent::whereNotNull('company_details')
            ->orWhereNotNull('agent_business_data')
            ->get();

        if ($userAgents->isEmpty()) {
            $this->command->warn('No user agents with business data found. Please run AgentsSeeder first.');
            return;
        }

        $this->command->info("Found {$userAgents->count()} user agents with business data.");

        $businessEmbeddingService = app(BusinessEmbeddingService::class);
        $totalEmbeddings = 0;
        // dd($userAgents->toArray());
        foreach ($userAgents as $userAgent) {
            // dd($userAgent);
            try {
                $this->command->info("Processing embeddings for UserAgent ID: {$userAgent->id}");

                // Generate embeddings for this user agent
                $businessEmbeddingService->embedUserAgentBusinessData($userAgent);

                // Count embeddings created
                $embeddingCount = $userAgent->businessEmbeddings()->count();
                $totalEmbeddings += $embeddingCount;

                $this->command->info("Created {$embeddingCount} embeddings for UserAgent ID: {$userAgent->id}");

                // Log the types of embeddings created
                $embeddingTypes = $userAgent->businessEmbeddings()
                    ->select('content_type', 'content_key')
                    ->get()
                    ->groupBy('content_type')
                    ->map(function ($group) {
                        return $group->pluck('content_key')->toArray();
                    });

                foreach ($embeddingTypes as $contentType => $contentKeys) {
                    $this->command->line("  - {$contentType}: " . implode(', ', $contentKeys));
                }

            } catch (\Exception $e) {
                $this->command->error("Failed to create embeddings for UserAgent ID: {$userAgent->id}");
                $this->command->error("Error: " . $e->getMessage());
                Log::error('BusinessEmbeddingSeeder failed', [
                    'user_agent_id' => $userAgent->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        $this->command->info("Business Embedding Seeder completed successfully!");
        $this->command->info("Total embeddings created: {$totalEmbeddings}");

        // Show summary of all embeddings by type
        $this->showEmbeddingSummary();
    }

    /**
     * Show a summary of all embeddings created
     */
    private function showEmbeddingSummary(): void
    {
        $this->command->info("\n=== Embedding Summary ===");

        $summary = BusinessEmbedding::selectRaw('
            content_type,
            COUNT(*) as count,
            COUNT(DISTINCT user_agent_id) as user_agents
        ')
        ->groupBy('content_type')
        ->orderBy('content_type')
        ->get();

        foreach ($summary as $row) {
            $this->command->line("  {$row->content_type}: {$row->count} embeddings across {$row->user_agents} user agents");
        }

        $totalEmbeddings = BusinessEmbedding::count();
        $totalUserAgents = BusinessEmbedding::distinct('user_agent_id')->count();

        $this->command->info("\nTotal: {$totalEmbeddings} embeddings across {$totalUserAgents} user agents");
    }
} 