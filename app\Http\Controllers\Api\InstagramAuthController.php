<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InstagramToken;
use App\Models\Platform;
use App\Models\Token;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

final class InstagramAuthController extends Controller
{
    use ApiResponseTrait;

    private string $clientId;
    private string $clientSecret;
    private string $redirectUri;
    private string $graphApiUrl;

    public function __construct()
    {
        $this->clientId     = config('services.instagram.app_id');
        $this->clientSecret = config('services.instagram.app_secret');
        $this->redirectUri  = config('services.instagram.redirect_uri');
        $this->graphApiUrl  = config('services.instagram.graph_api_url', 'https://graph.instagram.com');
    }

    // /**
    //  * Redirect user to Instagram OAuth authorization page
    //  *
    //  * @param Request $request
    //  * @return JsonResponse
    //  */
    public function redirectToInstagram(Request $request): JsonResponse
    {
        // Generate a random state parameter for security
        $state = Str::random(40);
        session(['instagram_oauth_state' => $state]);

        // Get user_id from request if available (for non-authenticated contexts)
        $userId = $request->query('user_id');

        // Store user_id in session if provided
        if ($userId) {
            session(['instagram_auth_user_id' => $userId]);
        }

        // Define scopes for Instagram Business API
        // Using the new scope values as per Meta's documentation (old ones will be deprecated on January 27, 2025)
        $scope = 'instagram_business_basic,instagram_business_manage_messages,instagram_business_manage_comments,instagram_business_content_publish,instagram_business_manage_insights';

        // Prepare redirect URI with user_id if provided
        $redirectUri = $this->redirectUri;
        if ($userId) {
            $redirectUri = strpos($redirectUri, '?') !== false
            ? $redirectUri . '&user_id=' . $userId
            : $redirectUri . '?user_id=' . $userId;
        }

        // Instagram OAuth URL
        $authUrl = "https://www.instagram.com/oauth/authorize?" . http_build_query([
            'client_id'     => $this->clientId,
            'redirect_uri'  => $redirectUri,
            'scope'         => $scope,
            'response_type' => 'code',
            'state'         => $state,
            'force_reauth'  => 'true',
        ]);

        return $this->successResponse([
            'auth_url' => $authUrl,
            'state'    => $state,
            'user_id'  => $userId,
        ], 'Instagram authorization URL generated successfully');
    }

    /**
     * Handle Instagram OAuth callback
     */
    public function handleCallback(Request $request)
    {
        // If this is an AJAX request or API call, return JSON
        if ($request->expectsJson() || $request->is('api/*')) {
            return $this->handleCallbackJson($request);
        }

        // For web requests, return the view and let JavaScript handle the API call
        // Pass any user_id from the request to the view for client-side handling
        $userId = $request->query('user_id');
        return view('instagram-auth', ['user_id' => $userId]);
    }

    /**
     * Handle Instagram OAuth callback and return JSON response
     */
    public function handleCallbackJson(Request $request): JsonResponse
    {
        try {
            // Validate required parameters - check both query params and request body for testing
            $code             = $request->query('code') ?? $request->input('code');
            $state            = $request->query('state') ?? $request->input('state');
            $error            = $request->query('error') ?? $request->input('error');
            $errorReason      = $request->query('error_reason') ?? $request->input('error_reason');
            $errorDescription = $request->query('error_description') ?? $request->input('error_description');

            // dd($code, $state, $error, $errorReason, $errorDescription);

            // Get user_id from request or session - check both query params and request body
            $userId = $request->query('user_id') ?? $request->input('user_id');

            // If not in request, try to get from session
            if (!$userId && session()->has('instagram_auth_user_id')) {
                $userId = session('instagram_auth_user_id');
                session()->forget('instagram_auth_user_id'); // Clear it after use
            }

            Log::info('Instagram callback received', [
                'code_exists'       => !empty($code),
                'code_length'       => !empty($code) ? strlen($code) : 0,
                'state'             => $state,
                'error'             => $error,
                'error_reason'      => $errorReason,
                'error_description' => $errorDescription,
                'full_url'          => $request->fullUrl(),
            ]);

            // Check for OAuth errors
            if ($error) {
                Log::error('Instagram OAuth error', [
                    'error'             => $error,
                    'error_reason'      => $errorReason,
                    'error_description' => $errorDescription,
                ]);

                return $this->errorResponse(
                    "Instagram authorization failed: {$errorDescription}",
                    400
                );
            }

            // Validate state parameter for CSRF protection
            $sessionState = session('instagram_oauth_state');
            if ($state && $sessionState && $state !== $sessionState) {
                Log::error('Instagram OAuth state mismatch', [
                    'received_state' => $state,
                    'session_state'  => $sessionState,
                ]);

                return $this->errorResponse('Invalid state parameter', 400);
            }

            // Clear the state from session
            session()->forget('instagram_oauth_state');

            if (!$code) {
                return $this->errorResponse('Authorization code not provided', 400);
            }

            // Step 1: Exchange code for short-lived access token
            $shortLivedToken = $this->getShortLivedToken($code);

            if (!$shortLivedToken) {
                return $this->errorResponse('Failed to exchange code for access token', 500);
            }

            Log::info('Short-lived token obtained', [
                'token_data'   => array_merge(
                    $shortLivedToken,
                    ['access_token' => substr($shortLivedToken['access_token'] ?? '', 0, 10) . '...']
                ),
                'user_id_type' => isset($shortLivedToken['user_id']) ? gettype($shortLivedToken['user_id']) : 'not set',
            ]);

            // Step 2: Exchange short-lived token for long-lived token
            $longLivedToken = $this->getLongLivedToken($shortLivedToken['access_token']);

            if (!$longLivedToken) {
                return $this->errorResponse('Failed to exchange for long-lived token', 500);
            }

            Log::info('Long-lived token obtained', [
                'token_data' => array_merge(
                    $longLivedToken,
                    ['access_token' => substr($longLivedToken['access_token'] ?? '', 0, 10) . '...']
                ),
            ]);

            // Step 3: Get Instagram account info with extended fields
            // Pass permissions from token exchange to avoid additional API call
            $permissions   = $shortLivedToken['permissions'] ?? [];
            $igAccountInfo = $this->getInstagramAccountInfo($longLivedToken['access_token'], $permissions);

            // $subscribedApps = $this->checkSubscribedApps($userId, $longLivedToken['access_token']);

            if (!$igAccountInfo) {
                return $this->errorResponse('Failed to retrieve Instagram account information', 500);
            }

            try {
                // Step 4: Store token in database - pass the user_id from request if available
                $InstagramToken = $this->storeToken(
                    $longLivedToken,
                    (string) $shortLivedToken['user_id'],
                    $igAccountInfo,
                    null,
                    $userId // Pass the user_id from the request
                );

                // Step 5: Ensure webhook subscription for this IG account
                $subscription = $this->ensureWebhookSubscription($InstagramToken);

                return $this->successResponse([
                    'token_id'            => $InstagramToken->id,
                    'username'            => $InstagramToken->username,
                    'account_type'        => $InstagramToken->account_type,
                    'expires_at'          => $InstagramToken->expires_at,
                    'media_count'         => $InstagramToken->media_count,
                    'webhook_subscribed'  => $subscription['subscribed'] ?? false,
                    'subscription_status' => $subscription['status'] ?? null,
                ], 'Instagram account connected successfully');
            } catch (\Exception $dbException) {
                Log::error('Database error during token storage', [
                    'error' => $dbException->getMessage(),
                    'trace' => $dbException->getTraceAsString(),
                ]);

                return $this->errorResponse(
                    'Error storing Instagram token: ' . $dbException->getMessage(),
                    500
                );
            }

        } catch (\Exception $e) {
            Log::error('Instagram OAuth callback error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'An error occurred during Instagram authorization: ' . $e->getMessage(),
                500
            );
        }
    }

    // private function checkSubscribedApps(string $userId, string $code)
    // {
    //     try {
    //         $response = Http::timeout(30)
    //             ->retry(3, 1000)
    //             ->get("{$this->graphApiUrl}/$userId/subscribed_apps", [
    //                     'access_token'      => $code,
    //                 'subscribed_fields' => ['messaging_postbacks', 'messaging_seen', 'messaging_handover', 'messaging_referral', 'messaging_optins', 'message_reactions', 'message_edit', 'messages'],
    //             ]);
    //     } catch (\Exception $e) {
    //         Log::error('Instagram token exchange exception', [
    //             'error' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString(),
    //         ]);

    //         return null;
    //     }
    // }

    /**
     * Exchange authorization code for short-lived access token
     */
    private function getShortLivedToken(string $code): ?array
    {
        try {
            Log::info('Exchanging code for token', [
                'code_length'  => strlen($code),
                'redirect_uri' => $this->redirectUri,
            ]);

            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->asForm()
                ->post('https://api.instagram.com/oauth/access_token', [
                    'client_id'     => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type'    => 'authorization_code',
                    'redirect_uri'  => $this->redirectUri,
                    'code'          => $code,
                ]);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('Instagram token exchange response', [
                    'response'            => $responseData,
                    'user_id_type'        => isset($responseData['user_id']) ? gettype($responseData['user_id']) : 'not set',
                    'access_token_length' => isset($responseData['access_token']) ? strlen($responseData['access_token']) : 0,
                ]);

                // Mark this as a short-lived token
                $responseData['token_source'] = 'short_lived';

                return $responseData;
            }

            Log::error('Instagram token exchange failed', [
                'status'   => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Instagram token exchange exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Exchange short-lived token for long-lived token
     * Valid for 60 days
     */
    private function getLongLivedToken(string $shortLivedToken): ?array
    {
        try {
            $response = Http::timeout(30)
                ->retry(3, 1000) // Retry 3 times with 1 second delay
                ->get("{$this->graphApiUrl}/access_token", [
                    'grant_type'    => 'ig_exchange_token',
                    'client_secret' => $this->clientSecret,
                    'access_token'  => $shortLivedToken,
                ]);

            if ($response->successful()) {
                $responseData = $response->json();

                // Mark this as a long-lived token
                $responseData['token_source'] = 'long_lived';

                return $responseData;
            }

            Log::error('Long-lived token exchange failed', [
                'status'   => $response->status(),
                'response' => $response->body(),
            ]);

            // Return the short-lived token as fallback if long-lived exchange fails
            Log::warning('Falling back to short-lived token due to API response failure');
            return [
                'access_token' => $shortLivedToken,
                'token_type'   => 'bearer',
                'token_source' => 'short_lived_fallback',
                'expires_in'   => 3600, // 1 hour for short-lived tokens
            ];
        } catch (\Exception $e) {
            Log::error('Long-lived token exchange exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Return the short-lived token as fallback if long-lived exchange fails
            Log::warning('Falling back to short-lived token due to exchange failure');
            return [
                'access_token' => $shortLivedToken,
                'token_type'   => 'bearer',
                'token_source' => 'short_lived_fallback',
                'expires_in'   => 3600, // 1 hour for short-lived tokens
            ];
        }
    }

    /**
     * Get Instagram account information with extended fields
     */
    private function getInstagramAccountInfo(string $accessToken, array $permissions = []): ?array
    {
        try {
            // Request only fields that are in our schema
            $fields = 'id,user_id,username,account_type,media_count,profile_picture_url,name,followers_count,follows_count';

            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->get("{$this->graphApiUrl}/me", [
                    'fields'       => $fields,
                    'access_token' => $accessToken,
                ]);

            if ($response->successful()) {
                $data = $response->json();

                Log::info('Instagram account info retrieved', [
                    'data' => $data,
                ]);

                // Use permissions from token exchange response
                $data['permissions'] = $permissions;

                // Check if it has instagram_business_basic permission
                $data['has_business_permission'] = false;
                if (is_array($permissions)) {
                    foreach ($permissions as $permission) {
                        if ($permission === 'instagram_business_basic' ||
                            $permission === 'business_basic') {
                            $data['has_business_permission'] = true;
                            break;
                        }
                    }
                }

                return $data;
            }

            Log::error('Instagram account info request failed', [
                'status'   => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Instagram account info exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Store Instagram token in database
     *
     * @param array $tokenData The token data to store
     * @param string $userId The Instagram user ID
     * @param array|null $igAccountInfo Instagram account information
     * @param array|null $pageData Facebook page data if applicable
     * @param string|null $requestUserId Optional user ID from request for non-authenticated contexts
     * @return InstagramToken
     */
    private function storeToken(array $tokenData, $userId, ?array $igAccountInfo = null, ?array $pageData = null, ?string $requestUserId = null): InstagramToken
    {
        // Get the authenticated user's ID
        $authenticatedUserId = Auth::id();

        if (!$authenticatedUserId) {
            // Use the provided user ID from request, or fall back to a default
            $authenticatedUserId = $requestUserId ?? request()->input('user_id') ?? 1;

            // Log this fallback for debugging
            Log::warning('Using fallback user ID for Instagram token storage', [
                'user_id' => $authenticatedUserId,
                'reason'  => 'No authenticated user found',
                'source'  => $requestUserId ? 'request parameter' : 'default value',
            ]);
        }

        // Ensure userId is a string
        $userId = (string) $userId;

        // Make sure we have a valid Instagram ID
        $instagramId = null;

        // First try to get the ID from the account info
        if ($igAccountInfo && isset($igAccountInfo['id'])) {
            $instagramId = (string) $igAccountInfo['id'];
        }
        // If not available, use the userId as fallback
        else {
            $instagramId = $userId;
        }

        // Log the ID we're using
        Log::info('Using Instagram app-scoped ID for token', [
            'app_scope_id' => $instagramId,
            'source'       => $igAccountInfo && isset($igAccountInfo['id']) ? 'account_info' : 'user_id',
        ]);

        // Get Instagram platform ID
        $instagramPlatformId = Platform::where('key', 'instagram')->value('id');

        if (!$instagramPlatformId) {
            throw new \Exception('Instagram platform not found');
        }

        // Find existing InstagramToken records connected to this user via the Token model
        $existingTokenIds = Token::where('user_id', $authenticatedUserId)
            ->where('platform_id', $instagramPlatformId)
            ->where('tokenable_type', InstagramToken::class)
            ->pluck('tokenable_id')
            ->toArray();

        // Deactivate any existing tokens for this Instagram account
        if (!empty($existingTokenIds)) {
            InstagramToken::whereIn('id', $existingTokenIds)
                ->where(function ($query) use ($instagramId, $igAccountInfo) {
                    $query->where('app_scope_id', $instagramId);
                    if ($igAccountInfo && isset($igAccountInfo['user_id'])) {
                        $query->orWhere('ig_id', (string) $igAccountInfo['user_id']);
                    }
                })
                ->update(['is_active' => false]);
        }

        // Calculate expiration date from expires_in (seconds)
        $expiresAt = null;
        if (isset($tokenData['expires_in'])) {
            $expiresAt = now()->addSeconds($tokenData['expires_in']);
        }

        // Determine permissions granted
        $scopes = [];
        if (isset($igAccountInfo['permissions']) && is_array($igAccountInfo['permissions'])) {
            $scopes = array_map(function ($item) {
                return $item['permission'] ?? '';
            }, $igAccountInfo['permissions']);
        } elseif (isset($tokenData['permissions'])) {
            $scopes = is_array($tokenData['permissions'])
            ? $tokenData['permissions']
            : explode(',', $tokenData['permissions']);
        }

        // Create transaction to ensure both records are created or none
        $instagramToken = DB::transaction(function () use ($instagramId, $igAccountInfo, $tokenData, $scopes, $expiresAt, $pageData, $instagramPlatformId, $authenticatedUserId) {
            // Create new Instagram token record without platform_id and user_id
            $instagramToken = InstagramToken::create([
                'app_scope_id'            => $instagramId,
                'ig_id'                   => $igAccountInfo['user_id'] ?? $instagramId, // Instagram account ID
                'access_token'            => $tokenData['access_token'],
                'token_type'              => $tokenData['token_type'] ?? 'bearer',
                'scopes'                  => $scopes,
                'is_active'               => true,
                'status'                  => 'active',
                'expires_at'              => $expiresAt,
                'page_id'                 => $pageData['page_id'] ?? null,
                'username'                => $igAccountInfo['username'] ?? null,
                'name'                    => $igAccountInfo['name'] ?? null,
                'account_type'            => $igAccountInfo['account_type'] ?? null,
                'media_count'             => $igAccountInfo['media_count'] ?? null,
                'profile_picture_url'     => $igAccountInfo['profile_picture_url'] ?? null,
                'has_business_permission' => $igAccountInfo['has_business_permission'] ?? false,
                'followers_count'         => $igAccountInfo['followers_count'] ?? null,
                'follows_count'           => $igAccountInfo['follows_count'] ?? null,
            ]);

            // Create the polymorphic token relationship - this is where we store platform_id and user_id
            Token::create([
                'platform_id'    => $instagramPlatformId,
                'user_id'        => $authenticatedUserId,
                'tokenable_type' => InstagramToken::class,
                'tokenable_id'   => $instagramToken->id,
            ]);

            return $instagramToken;
        });

        return $instagramToken;
    }

    /**
     * Get current Instagram connection status for authenticated user
     */
    public function getConnectionStatus(): JsonResponse
    {
        try {
            $authenticatedUserId = Auth::id();

            if (!$authenticatedUserId) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Find active Instagram tokens through the Token relationship
            $token = Token::where('user_id', $authenticatedUserId)
                ->where('platform_id', Platform::where('key', 'instagram')->value('id'))
                ->where('tokenable_type', InstagramToken::class)
                ->whereHas('tokenable', function ($query) {
                    $query->where('is_active', true);
                })
                ->first();

            // Get the actual InstagramToken model
            $instagramToken = $token ? $token->tokenable : null;

            if (!$instagramToken) {
                return $this->successResponse([
                    'connected' => false,
                    'message'   => 'No Instagram account connected',
                ]);
            }

            // Check if token is expired
            if ($instagramToken->expires_at && $instagramToken->expires_at->isPast()) {
                $instagramToken->update(['is_active' => false, 'status' => 'expired']);

                return $this->successResponse([
                    'connected' => false,
                    'message'   => 'Instagram token has expired',
                ]);
            }

            // Check if token needs refreshing (within 7 days of expiration)
            if ($instagramToken->needsRefresh()) {
                // Try to refresh the token automatically
                $this->attemptTokenRefresh($instagramToken);
                // Reload the token to get updated data
                $instagramToken->refresh();
            }

            return $this->successResponse([
                'connected' => true,
                'account'   => [
                    'id'                      => $token->user_id ?? $instagramToken->app_scope_id,
                    'username'                => $instagramToken->username,
                    'name'                    => $instagramToken->name,
                    'account_type'            => $instagramToken->account_type,
                    'connected_at'            => $instagramToken->created_at,
                    'expires_at'              => $instagramToken->expires_at,
                    'days_until_expiration'   => $instagramToken->days_until_expiration,
                    'media_count'             => $instagramToken->media_count,
                    'followers_count'         => $instagramToken->followers_count,
                    'follows_count'           => $instagramToken->follows_count,
                    'has_business_permission' => $instagramToken->has_business_permission,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram connection status error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Failed to check Instagram connection status',
                500
            );
        }
    }

    /**
     * Disconnect Instagram account for authenticated user
     */
    public function disconnect(): JsonResponse
    {
        try {
            $authenticatedUserId = Auth::id();

            if (!$authenticatedUserId) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Deactivate through Token relationship
            $updated = 0;

            // Find active Instagram tokens through the Token relationship
            $tokens = Token::where('user_id', $authenticatedUserId)
                ->where('platform_id', Platform::where('key', 'instagram')->value('id'))
                ->where('tokenable_type', InstagramToken::class)
                ->get();

            foreach ($tokens as $token) {
                if ($token->tokenable && $token->tokenable->is_active) {
                    // Try to unsubscribe webhooks for this IG account (best-effort)
                    try {
                        $this->unsubscribeFromWebhooks($token->tokenable);
                    } catch (\Throwable $t) {
                        Log::warning('Failed to unsubscribe IG webhooks during disconnect', [
                            'token_id' => $token->tokenable->id,
                            'error'    => $t->getMessage(),
                        ]);
                    }

                    $token->tokenable->update(['is_active' => false, 'status' => 'revoked']);
                    $updated++;
                }
            }

            if ($updated === 0) {
                return $this->errorResponse('No active Instagram connection found', 404);
            }

            Log::info('Instagram account disconnected for user', [
                'user_id' => $authenticatedUserId,
            ]);

            return $this->successResponse(
                ['message' => 'Instagram account disconnected successfully']
            );

        } catch (\Exception $e) {
            Log::error('Instagram disconnect error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Failed to disconnect Instagram account',
                500
            );
        }
    }

    /**
     * Refresh Instagram access token for authenticated user
     */
    public function refreshToken(): JsonResponse
    {
        try {
            $authenticatedUserId = Auth::id();

            if (!$authenticatedUserId) {
                return $this->errorResponse('User not authenticated', 401);
            }

            // Find active Instagram tokens through the Token relationship
            $token = Token::where('user_id', $authenticatedUserId)
                ->where('platform_id', Platform::where('key', 'instagram')->value('id'))
                ->where('tokenable_type', InstagramToken::class)
                ->whereHas('tokenable', function ($query) {
                    $query->where('is_active', true);
                })
                ->first();

            // Get the actual InstagramToken model
            $instagramToken = $token ? $token->tokenable : null;

            if (!$instagramToken) {
                return $this->errorResponse('No Instagram token found to refresh', 404);
            }

            // Verify token is eligible for refresh
            if (!$instagramToken->isEligibleForRefresh()) {
                return $this->errorResponse(
                    'Token is not eligible for refresh. It must be at least 24 hours old, active, not expired, and have business permissions.',
                    400
                );
            }

            $refreshed = $this->attemptTokenRefresh($instagramToken);

            if (!$refreshed) {
                return $this->errorResponse('Failed to refresh Instagram token', 500);
            }

            // Reload the token to get updated data
            $instagramToken->refresh();

            return $this->successResponse([
                'message'               => 'Instagram token refreshed successfully',
                'expires_at'            => $instagramToken->expires_at,
                'days_until_expiration' => $instagramToken->days_until_expiration,
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram token refresh error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Failed to refresh Instagram token: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Attempt to refresh a token
     *
     * @param InstagramToken $token The token to refresh
     * @return bool True if refresh was successful
     */
    private function attemptTokenRefresh(InstagramToken $token): bool
    {
        try {
            // Check if token has instagram_business_basic permission
            if (!$token->hasBusinessPermission()) {
                Log::warning('Token lacks instagram_business_basic permission required for refresh', [
                    'token_id' => $token->id,
                    'scopes'   => $token->scopes,
                ]);
                return false;
            }

            // Instagram Basic Display API tokens can be refreshed
            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->get("{$this->graphApiUrl}/refresh_access_token", [
                    'grant_type'   => 'ig_refresh_token',
                    'access_token' => $token->access_token,
                ]);

            if (!$response->successful()) {
                Log::error('Instagram token refresh failed', [
                    'status'   => $response->status(),
                    'response' => $response->body(),
                ]);
                return false;
            }

            $refreshData = $response->json();

            // Update token in database
            $token->update([
                'access_token' => $refreshData['access_token'],
                'expires_at'   => isset($refreshData['expires_in']) ?
                now()->addSeconds($refreshData['expires_in']) : null,
                'updated_at'   => now(),
            ]);

            Log::info('Instagram token refreshed successfully', [
                'token_id'   => $token->id,
                'expires_at' => $token->expires_at,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Token refresh attempt failed', [
                'error'    => $e->getMessage(),
                'token_id' => $token->id,
                'trace'    => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Ensure the Instagram account is subscribed to required webhook fields
     *
     * @return array{subscribed: bool, status?: mixed}
     */
    private function ensureWebhookSubscription(InstagramToken $token): array
    {
        try {
            $igId         = (string) ($token->ig_id ?: $token->app_scope_id);
            $accessToken  = $token->access_token;
            $fields       = $this->getRequiredSubscriptionFields();
            $fieldsString = implode(',', $fields);

            // Primary attempt using configured Instagram Graph API base
            $primaryUrl = rtrim($this->graphApiUrl, '/') . "/{$igId}/subscribed_apps";

            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->asForm()
                ->post($primaryUrl, [
                    'access_token'      => $accessToken,
                    'subscribed_fields' => $fieldsString,
                ]);

            if ($response->successful()) {
                Log::info('IG webhook subscribed via primary URL', [
                    'ig_id'  => $igId,
                    'fields' => $fields,
                    'base'   => $this->graphApiUrl,
                    'result' => $response->json(),
                ]);
                return ['subscribed' => true, 'status' => $response->json()];
            }

            Log::warning('Primary IG webhook subscribe failed; attempting Facebook Graph fallback', [
                'ig_id'  => $igId,
                'status' => $response->status(),
                'body'   => $response->body(),
            ]);

            // Fallback to Facebook Graph domain with versioned path
            $fbUrl  = 'https://graph.facebook.com/v23.0/' . $igId . '/subscribed_apps';
            $fbResp = Http::timeout(30)->retry(3, 1000)->asForm()->post($fbUrl, [
                'access_token'      => $accessToken,
                'subscribed_fields' => $fieldsString,
            ]);

            if ($fbResp->successful()) {
                Log::info('IG webhook subscribed via Facebook Graph fallback', [
                    'ig_id'  => $igId,
                    'fields' => $fields,
                    'result' => $fbResp->json(),
                ]);
                return ['subscribed' => true, 'status' => $fbResp->json()];
            }

            Log::error('IG webhook subscribe failed on both primary and fallback', [
                'ig_id'    => $igId,
                'primary'  => ['status' => $response->status(), 'body' => $response->body()],
                'fallback' => ['status' => $fbResp->status(), 'body' => $fbResp->body()],
            ]);
            return ['subscribed' => false, 'status' => [
                'primary_status'  => $response->status(),
                'fallback_status' => $fbResp->status(),
            ]];
        } catch (\Throwable $e) {
            Log::error('Error ensuring IG webhook subscription', [
                'token_id' => $token->id,
                'error'    => $e->getMessage(),
            ]);
            return ['subscribed' => false, 'status' => $e->getMessage()];
        }
    }

    /**
     * Best-effort unsubscribe from IG webhooks for an account
     */
    private function unsubscribeFromWebhooks(InstagramToken $token): bool
    {
        try {
            $igId        = (string) ($token->ig_id ?: $token->app_scope_id);
            $accessToken = $token->access_token;

            $primaryUrl = rtrim($this->graphApiUrl, '/') . "/{$igId}/subscribed_apps";
            $resp       = Http::timeout(30)
                ->retry(2, 500)
                ->asForm()
                ->delete($primaryUrl, [
                    'access_token' => $accessToken,
                ]);

            if ($resp->successful()) {
                Log::info('Unsubscribed IG webhooks via primary URL', [
                    'ig_id' => $igId,
                ]);
                return true;
            }

            $fbUrl  = 'https://graph.facebook.com/v23.0/' . $igId . '/subscribed_apps';
            $fbResp = Http::timeout(30)
                ->retry(2, 500)
                ->asForm()
                ->delete($fbUrl, [
                    'access_token' => $accessToken,
                ]);

            if ($fbResp->successful()) {
                Log::info('Unsubscribed IG webhooks via Facebook Graph fallback', [
                    'ig_id' => $igId,
                ]);
                return true;
            }

            Log::warning('Failed to unsubscribe IG webhooks', [
                'ig_id'   => $igId,
                'primary' => ['status' => $resp->status(), 'body' => $resp->body()],
                'fb'      => ['status' => $fbResp->status(), 'body' => $fbResp->body()],
            ]);
            return false;
        } catch (\Throwable $e) {
            Log::warning('Error during IG webhook unsubscribe', [
                'token_id' => $token->id,
                'error'    => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Required subscribed fields for our app to receive relevant IG webhooks
     */
    private function getRequiredSubscriptionFields(): array
    {
        return [
            'messages',
            'messaging_postbacks',
            'messaging_seen',
            'messaging_referral',
            'messaging_optins',
            'message_reactions',
            'message_edit',
        ];
    }

    /**
     * Get active Instagram token for authenticated user
     *
     * @return InstagramToken|null The active token or null if none found
     */
    public function getActiveToken(): ?InstagramToken
    {
        $authenticatedUserId = Auth::id();

        if (!$authenticatedUserId) {
            return null;
        }

        // Find active Instagram tokens through the Token relationship
        $token = Token::where('user_id', $authenticatedUserId)
            ->where('platform_id', Platform::where('key', 'instagram')->value('id'))
            ->where('tokenable_type', InstagramToken::class)
            ->whereHas('tokenable', function ($query) {
                $query->where('is_active', true);
            })
            ->first();

        // Get the actual InstagramToken model
        $instagramToken = $token ? $token->tokenable : null;

        if (!$instagramToken) {
            return null;
        }

        // Check if token is expired
        if ($instagramToken->isExpired()) {
            $instagramToken->update(['is_active' => false, 'status' => 'expired']);
            return null;
        }

        // Check if token needs refreshing (within 7 days of expiration)
        if ($instagramToken->needsRefresh()) {
            // Try to refresh the token automatically
            $this->attemptTokenRefresh($instagramToken);
            // Reload the token to get updated data
            $instagramToken->refresh();
        }

        return $instagramToken;
    }
}
