<?php

declare (strict_types = 1);

namespace App\Repositories\Agent;

use App\Models\Agent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

final class AgentRepository implements AgentRepositoryInterface
{
    public function __construct(
        private readonly Agent $model
    ) {}

    public function getAll(): Collection
    {
        return $this->model->with('userAgents')->get();
    }

    public function getAllWithFilters(array $filters = []): Collection
    {
        $query = $this->model->query()->with('userAgents');

        if (isset($filters['active'])) {
            $query->where('is_active', $filters['active']);
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('key', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->get();
    }

    public function getPaginated(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $this->model->query()->with('userAgents');

        if (isset($filters['active'])) {
            $query->where('is_active', $filters['active']);
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('key', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->paginate($perPage);
    }

    public function findById(string $id): ?Agent
    {
        return $this->model->with('userAgents')->find($id);
    }

    public function findByKey(string $key): ?Agent
    {
        return $this->model->with('userAgents')->where('key', $key)->first();
    }

    public function getActive(): Collection
    {
        return $this->model->with('userAgents')->where('is_active', true)->get();
    }

    public function search(string $query): Collection
    {
        return $this->model->with('userAgents')->where(function ($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
                ->orWhere('key', 'like', "%{$query}%")
                ->orWhere('description', 'like', "%{$query}%");
        })->get();
    }

    public function updateStatus(Agent $agent, bool $isActive): Agent
    {
        $agent->is_active = $isActive;
        $agent->save();
        return $agent->fresh(['userAgents']);
    }

    public function getWithUserAgentsCount(string $id): ?Agent
    {
        return $this->model->with([
            'userAgents.knowledgeFiles.chunks',
            'userAgents.knowledgeFiles.uploadedBy',
        ])->find($id);
    }

}
