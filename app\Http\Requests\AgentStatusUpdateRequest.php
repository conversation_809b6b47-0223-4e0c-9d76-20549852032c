<?php

declare (strict_types = 1);

namespace App\Http\Requests;

final class AgentStatusUpdateRequest extends AgentIdRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'is_active' => 'required|boolean',
        ]);
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'is_active.required' => 'The is_active field is required.',
            'is_active.boolean'  => 'The is_active field must be true or false.',
        ]);
    }
}
