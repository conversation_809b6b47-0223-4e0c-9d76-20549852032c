<?php

declare (strict_types = 1);

namespace App\Services\Chatbot;

use App\Models\Appointment;
use App\Models\Message;
use App\Models\UserAgent;
use App\Repositories\Message\MessageRepositoryInterface;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

final class AppointmentBookingService
{
    public function __construct(
        private readonly MessageRepositoryInterface $messageRepository,
    ) {}
    /**
     * Detect appointment booking intent using AI-powered contextual analysis
     */
    public function detectAppointmentIntent(string $message, ?UserAgent $userAgent = null, ?string $senderId = null): array
    {
        Log::info('=== APPOINTMENT: Starting intelligent intent detection ===', [
            'message'        => $message,
            'user_agent_id'  => $userAgent?->id,
            'message_length' => strlen($message),
        ]);

        // Get conversation context to understand the full flow
        $conversationHistory = $this->getRecentConversationContext($message, $senderId);

        // Use AI-powered analysis instead of just keywords
        $contextualIntent = $this->analyzeBookingContextWithAI($message, $conversationHistory, $userAgent);

        if ($contextualIntent['detected']) {
            Log::info('APPOINTMENT: AI detected booking intent', $contextualIntent);
            return $contextualIntent;
        }

        // Fallback to keyword-based detection for explicit booking requests
        $appointmentKeywords = [
            'book', 'schedule', 'appointment', 'reserve', 'availability',
            'available', 'time', 'when', 'today', 'tomorrow', 'next week',
            'calendar', 'free', 'slot', 'consultation', 'meeting',
        ];

        $urgentKeywords = ['urgent', 'asap', 'today', 'now', 'emergency'];

        // Get service keywords from UserAgent business data
        $serviceKeywords = $this->getServiceKeywordsFromUserAgent($userAgent);
        Log::info('APPOINTMENT: Service keywords loaded', [
            'service_keywords_count' => count($serviceKeywords),
            'service_keywords'       => array_keys($serviceKeywords),
        ]);

        $message = strtolower($message);
        $intent  = [
            'detected'          => false,
            'confidence'        => 0,
            'urgency'           => 'normal',
            'service_mentioned' => false,
            'specific_services' => [],
            'keywords_found'    => [],
        ];

        // Check for appointment-related keywords
        foreach ($appointmentKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                $intent['detected'] = true;
                $intent['confidence'] += 0.1;
                $intent['keywords_found'][] = $keyword;
            }
        }

        // Check for urgency
        foreach ($urgentKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                $intent['urgency'] = 'high';
                $intent['confidence'] += 0.2;
                $intent['keywords_found'][] = $keyword;
            }
        }

        // Check for specific services using business data
        foreach ($serviceKeywords as $serviceKey => $serviceData) {
            $serviceName     = strtolower($serviceData['name'] ?? $serviceKey);
            $serviceCategory = strtolower($serviceData['category'] ?? '');

            if (strpos($message, $serviceName) !== false ||
                ($serviceCategory && strpos($message, $serviceCategory) !== false)) {
                $intent['service_mentioned']   = true;
                $intent['specific_services'][] = [
                    'key'      => $serviceKey,
                    'name'     => $serviceData['name'] ?? $serviceKey,
                    'price'    => $serviceData['price'] ?? null,
                    'duration' => $serviceData['duration'] ?? null,
                    'category' => $serviceData['category'] ?? null,
                ];

                // Only boost confidence if not asking for general information
                if (!preg_match('/\b(tell me|more details|more information|what is|what are|explain|describe|about)\b/', $message)) {
                    $intent['confidence'] += 0.15;
                } else {
                    $intent['confidence'] += 0.05; // Minimal boost for information requests
                }
            }
        }

        // Removed hardcoded common services fallback. Service detection relies on UserAgent business configuration only.

        // Adjust confidence based on question words, but be more selective
        // Don't boost confidence for general information questions
        if (preg_match('/\b(when can i|what time can i|how soon can i|when to book|when to schedule)\b/', $message)) {
            $intent['confidence'] += 0.15; // Higher boost for explicit booking questions
        } elseif (preg_match('/\b(how much|cost|price)\b/', $message) && !preg_match('/\b(tell me|more details|information|about)\b/', $message)) {
            $intent['confidence'] += 0.1; // Only boost for pricing questions if not asking for general info
        }

        // Boost confidence if multiple indicators are present
        if (count($intent['keywords_found']) > 2) {
            $intent['confidence'] += 0.1;
        }

        $intent['confidence'] = min($intent['confidence'], 1.0);

        Log::info('APPOINTMENT: Intent detection completed', [
            'final_intent' => $intent,
            'message'      => $message,
        ]);

        return $intent;
    }

    /**
     * Handle appointment booking conversation flow with flexible scheduling
     */
    public function handleAppointmentBooking(Message $message, string $aiResponse, array $intent, ?UserAgent $userAgent = null): string
    {
        try {
            if (!$userAgent) {
                return $this->sanitizeFalseConfirmation($aiResponse) . "\n\nI'd be happy to help you schedule an appointment! Please let me know your preferred date and time.";
            }

            // Let the AI handle the conversation flow naturally

            Log::info('Handling appointment booking', [
                'message'           => $message->message,
                'intent_detected'   => $intent['detected'],
                'service_mentioned' => $intent['service_mentioned'],
                'specific_services' => $intent['specific_services'],
                'user_agent_id'     => $userAgent->id,
            ]);

            // Extract date, time, and quantity from current message
            $extractedDate     = $this->extractDateFromMessage($message->message);
            $extractedTime     = $this->extractTimeFromMessage($message->message);
            $extractedQuantity = $this->extractQuantityFromMessage($message->message);

            // Prefer LLM-provided structured values only when missing from user text, and normalize relative terms
            if (!$extractedDate && !empty($intent['llm_date'])) {
                $llmDate = (string) $intent['llm_date'];
                if (str_contains(strtolower($llmDate), 'tomorrow')) {
                    $extractedDate = Carbon::tomorrow()->format('Y-m-d');
                } elseif (str_contains(strtolower($llmDate), 'today')) {
                    $extractedDate = Carbon::today()->format('Y-m-d');
                } else {
                    $extractedDate = $llmDate;
                }
            }
            if (!$extractedTime && !empty($intent['llm_time'])) {
                $extractedTime = (string) $intent['llm_time'];
            }
            if (!empty($intent['llm_quantity'])) {
                $q                 = (int) $intent['llm_quantity'];
                $extractedQuantity = $q > 0 ? min($q, 10) : $extractedQuantity;
            }

            Log::info('Extracted booking details from message', [
                'extracted_date'     => $extractedDate,
                'extracted_time'     => $extractedTime,
                'extracted_quantity' => $extractedQuantity,
                'message'            => $message->message,
            ]);

            // Check if we have enough information to book
            $hasService  = !empty($intent['specific_services']);
            $hasDateTime = $extractedDate && $extractedTime;

            // Check for confirmation words (broad) and explicit confirmation (strict)
            $confirmationWords = ['yes', 'confirm', 'book it', 'book', 'ok', 'okay', 'sure', 'absolutely', 'correct'];
            $isConfirmation    = false;
            $messageLower      = strtolower($message->message);
            foreach ($confirmationWords as $word) {
                if (strpos($messageLower, $word) !== false) {
                    $isConfirmation = true;
                    break;
                }
            }
            // Explicit confirmation requires the user to write 'confirm' (or 'confirmed')
            // to avoid accidental auto-booking from casual phrases like 'ok' or 'sure'
            $hasExplicitConfirm = (bool) preg_match('/\bconfirm(ed)?\b|\bconfirm (booking|appointment)\b/', $messageLower);

            // If confirmation detected but no service/date/time in current message, try to get missing pieces from recent user messages only
            if ($isConfirmation && (!$hasService || !$hasDateTime)) {
                $conversationHistory = app(\App\Services\Chatbot\ConversationService::class)
                    ->getConversationContext($message->sender_id);

                Log::info('Searching conversation history for service info', [
                    'sender_id'       => $message->sender_id,
                    'history_count'   => count($conversationHistory),
                    'is_confirmation' => $isConfirmation,
                    'has_service'     => $hasService,
                    'has_date_time'   => $hasDateTime,
                ]);

                // Build dynamic service patterns from UserAgent business configuration
                $servicePatterns = $this->buildServicePatternsFromUserAgent($userAgent);

                // Look for service mentions in recent conversation
                foreach (array_reverse($conversationHistory) as $historyMessage) {
                    $content = strtolower($historyMessage['content'] ?? '');
                    foreach ($servicePatterns as $pattern => $serviceData) {
                        if (strpos($content, $pattern) !== false) {
                            $intent['service_mentioned']   = true;
                            $intent['specific_services'][] = $this->normalizeServiceInfo($serviceData);
                            $hasService                    = true;
                            Log::info('Retrieved service from conversation history', [
                                'service'    => $serviceData['name'],
                                'pattern'    => $pattern,
                                'message_id' => $message->id,
                                'found_in'   => $historyMessage['role'] ?? 'unknown',
                            ]);
                            break 2; // Break both loops
                        }
                    }
                }

                // If still no service found, check the raw message history from database
                if (!$hasService) {
                    $recentMessages = app(\App\Repositories\Message\MessageRepositoryInterface::class)
                        ->getConversationHistory($message->sender_id, 10);

                    foreach ($recentMessages as $msg) {
                        $content = strtolower($msg->message . ' ' . ($msg->reply ?? ''));
                        foreach ($servicePatterns as $pattern => $serviceData) {
                            if (strpos($content, $pattern) !== false) {
                                $intent['service_mentioned']   = true;
                                $intent['specific_services'][] = $this->normalizeServiceInfo($serviceData);
                                $hasService                    = true;
                                Log::info('Retrieved service from database message history', [
                                    'service'    => $serviceData['name'],
                                    'pattern'    => $pattern,
                                    'message_id' => $message->id,
                                ]);
                                break 2;
                            }
                        }
                    }
                }

                // Look for date/time in recent user messages (not assistant)
                if (!$hasDateTime) {
                    foreach (array_reverse($conversationHistory) as $historyMessage) {
                        if (($historyMessage['role'] ?? '') !== 'user') {
                            continue; // only trust user-provided text
                        }
                        $historicDate = $this->extractDateFromMessage($historyMessage['content'] ?? '');
                        $historicTime = $this->extractTimeFromMessage($historyMessage['content'] ?? '');
                        if ($historicDate && $historicTime) {
                            $extractedDate = $historicDate;
                            $extractedTime = $historicTime;
                            $hasDateTime   = true;
                            Log::info('Retrieved date/time from user history', [
                                'date'       => $extractedDate,
                                'time'       => $extractedTime,
                                'message_id' => $message->id,
                            ]);
                            break;
                        }
                    }
                }
            }

            Log::info('Booking conditions check', [
                'has_service'     => $hasService,
                'has_date_time'   => $hasDateTime,
                'is_confirmation' => $isConfirmation,
                'extracted_date'  => $extractedDate,
                'extracted_time'  => $extractedTime,
            ]);

            // Do NOT infer date/time from assistant messages to avoid hallucinated bookings

            // Check if customer information is complete before allowing booking
            $currentCustomerInfo = [
                'name'    => $this->extractCustomerNameFromMessage($message->message),
                'phone'   => $this->extractCustomerPhoneFromMessage($message->message),
                'email'   => $this->extractCustomerEmailFromMessage($message->message),
                'address' => $this->extractCustomerAddressFromMessage($message->message),
            ];

            // Also check conversation history for customer information
            $conversationHistory = app(\App\Services\Chatbot\ConversationService::class)
                ->getConversationContext($message->sender_id);

            foreach (array_reverse($conversationHistory) as $historyMessage) {
                if (($historyMessage['role'] ?? '') === 'user') {
                    if (empty($currentCustomerInfo['name'])) {
                        $currentCustomerInfo['name'] = $this->extractCustomerNameFromMessage($historyMessage['content'] ?? '');
                    }
                    if (empty($currentCustomerInfo['phone'])) {
                        $currentCustomerInfo['phone'] = $this->extractCustomerPhoneFromMessage($historyMessage['content'] ?? '');
                    }
                    if (empty($currentCustomerInfo['email'])) {
                        $currentCustomerInfo['email'] = $this->extractCustomerEmailFromMessage($historyMessage['content'] ?? '');
                    }
                    if (empty($currentCustomerInfo['address'])) {
                        $currentCustomerInfo['address'] = $this->extractCustomerAddressFromMessage($historyMessage['content'] ?? '');
                    }
                }
            }

            // Check what customer information is missing
            $customerInfoCheck       = $this->needsCustomerInfoCollection($userAgent, $currentCustomerInfo);
            $hasCompleteCustomerInfo = empty($customerInfoCheck['missing']);

            // Only ask for customer information if there's a clear booking intent
            // Don't ask just because a service was mentioned - user might be asking for information
            $hasBookingIntent = $hasDateTime || $isConfirmation ||
            ($intent['confidence'] >= 0.3 && count($intent['keywords_found']) > 0) ||
            $this->hasExplicitBookingLanguage($message->message);

            // If customer information is missing AND there's clear booking intent, ask for it
            if (!$hasCompleteCustomerInfo && $hasBookingIntent) {
                $missingInfoPrompt = $this->generateCustomerInfoPrompt($customerInfoCheck['missing'], $customerInfoCheck['collect_settings']);
                if (!empty($missingInfoPrompt)) {
                    Log::info('Customer information incomplete, requesting missing info', [
                        'missing_fields'     => $customerInfoCheck['missing'],
                        'message_id'         => $message->id,
                        'has_booking_intent' => $hasBookingIntent,
                        'confidence'         => $intent['confidence'],
                        'keywords_found'     => $intent['keywords_found'],
                    ]);
                    return $missingInfoPrompt;
                }
            }

            // Decide if we should initiate booking now
            $assistantAskedConfirm = $this->assistantRecentlyAskedForConfirmation($message->sender_id) || $this->aiContainsConfirmationInstruction($aiResponse);
            $llmConfirmType        = strtolower((string) ($intent['llm_confirmation'] ?? 'none'));
            $llmInitiate           = (bool) ($intent['llm_initiate'] ?? false);
            $canBook               = $hasService && $hasDateTime && $hasCompleteCustomerInfo && (
                $hasExplicitConfirm ||
                $llmInitiate ||
                ($isConfirmation && $assistantAskedConfirm) ||
                ($llmConfirmType === 'explicit') ||
                ($llmConfirmType === 'affirm' && $assistantAskedConfirm) ||
                ($intent['ready_to_book'] ?? false)
            );

            if ($canBook) {
                Log::info('Attempting to book appointment', [
                    'has_service'       => $hasService,
                    'has_date_time'     => $hasDateTime,
                    'is_confirmation'   => $isConfirmation,
                    'explicit_confirm'  => $hasExplicitConfirm,
                    'assistant_confirm' => $assistantAskedConfirm,
                    'llm_confirm_type'  => $llmConfirmType,
                    'llm_initiate'      => $llmInitiate,
                ]);

                $bookedAppointments = $this->attemptFlexibleBooking(
                    $message,
                    $intent,
                    $userAgent,
                    $extractedQuantity,
                    $extractedDate,
                    $extractedTime
                );

                Log::info('Booking attempt result', [
                    'booked_count' => count($bookedAppointments),
                    'appointments' => $bookedAppointments,
                ]);

                if (!empty($bookedAppointments)) {
                    Log::info('Successfully booked appointments', [
                        'appointment_count' => count($bookedAppointments),
                        'sender_id'         => $message->sender_id,
                        'appointment_ids'   => array_map(fn($apt) => $apt->id, $bookedAppointments),
                    ]);
                    return $this->generateFlexibleBookingConfirmation($bookedAppointments);
                } else {
                    Log::error('CRITICAL: Failed to book appointments - returning error message', [
                        'message_id'       => $message->id,
                        'user_agent_id'    => $userAgent->id,
                        'extracted_date'   => $extractedDate,
                        'extracted_time'   => $extractedTime,
                        'has_service'      => $hasService,
                        'has_date_time'    => $hasDateTime,
                        'is_confirmation'  => $isConfirmation,
                        'explicit_confirm' => $hasExplicitConfirm,
                        'intent_services'  => $intent['specific_services'] ?? [],
                    ]);

                    // Return error message instead of proceeding with AI response
                    return "I apologize, but I encountered an issue while creating your appointment. Please try booking again or contact us directly at our business number. Let me know if you'd like to try a different time or service.";
                }
            }

            // If all details provided but no explicit confirmation, rely on AI phrasing; avoid generic repeated CTA
            if ($hasService && $hasDateTime && !$hasExplicitConfirm) {
                // Check if customer information is complete before proceeding
                if (!$hasCompleteCustomerInfo) {
                    $missingInfoPrompt = $this->generateCustomerInfoPrompt($customerInfoCheck['missing'], $customerInfoCheck['collect_settings']);
                    if (!empty($missingInfoPrompt)) {
                        return $missingInfoPrompt;
                    }
                }

                $serviceName = $intent['specific_services'][0]['name'] ?? 'the selected service';
                $dateText    = $extractedDate;
                $timeText    = $extractedTime;
                $cleaned     = $this->finalizeAiResponse($aiResponse, $extractedDate, $extractedTime);
                return $cleaned; // Let the LLM lead the confirmation phrasing
            }

            // If user mentioned service but not complete booking info, do NOT suggest slots in flexible mode
            if ($hasService && !$hasDateTime) {
                // Check if customer information is complete before asking for date/time
                if (!$hasCompleteCustomerInfo) {
                    $missingInfoPrompt = $this->generateCustomerInfoPrompt($customerInfoCheck['missing'], $customerInfoCheck['collect_settings']);
                    if (!empty($missingInfoPrompt)) {
                        return $missingInfoPrompt;
                    }
                }

                $service = $intent['specific_services'][0];
                if ($this->aiAsksForDateOrTime($aiResponse)) {
                    return $this->finalizeAiResponse($aiResponse, $extractedDate, $extractedTime);
                }
                return "Great choice!\n\nWhen would you like to schedule your {$service['name']}? Please share any preferred date and time.";
            }

            // If user provided partial information, ask for what's missing — no slot suggestions
            if ($extractedDate && !$extractedTime) {
                // Check if customer information is complete before asking for time
                if (!$hasCompleteCustomerInfo) {
                    $missingInfoPrompt = $this->generateCustomerInfoPrompt($customerInfoCheck['missing'], $customerInfoCheck['collect_settings']);
                    if (!empty($missingInfoPrompt)) {
                        return $missingInfoPrompt;
                    }
                }

                $formattedDate = $this->formatDateForDisplay($extractedDate);
                if ($this->aiAsksForDateOrTime($aiResponse)) {
                    return $this->finalizeAiResponse($aiResponse, $extractedDate, null);
                }
                return $this->finalizeAiResponse($aiResponse, $extractedDate, null) . "\n\nWhat time would you prefer on {$formattedDate}?";
            }

            if ($extractedTime && !$extractedDate) {
                // Check if customer information is complete before asking for date
                if (!$hasCompleteCustomerInfo) {
                    $missingInfoPrompt = $this->generateCustomerInfoPrompt($customerInfoCheck['missing'], $customerInfoCheck['collect_settings']);
                    if (!empty($missingInfoPrompt)) {
                        return $missingInfoPrompt;
                    }
                }

                // Do not append our own date question; let the LLM drive the follow-up
                return $this->finalizeAiResponse($aiResponse, null, $extractedTime);
            }

            // Default: return the model's response without adding boilerplate
            return $this->finalizeAiResponse($aiResponse, $extractedDate, $extractedTime);

        } catch (Exception $e) {
            Log::error('Error handling appointment booking', [
                'error'         => $e->getMessage(),
                'message_id'    => $message->id,
                'user_agent_id' => $userAgent?->id,
            ]);

            return $aiResponse . "\n\nI apologize, but I encountered an issue while processing your booking request. Please try again or contact us directly for assistance.";
        }
    }

    /**
     * Attempt flexible booking with custom times and multiple appointments
     */
    public function attemptFlexibleBooking(Message $message, array $intent, ?UserAgent $userAgent = null, int $quantity = 1, ?string $forcedDate = null, ?string $forcedTime = null): array
    {
        try {
            if (!$userAgent) {
                return [];
            }

            // Validate customer information before attempting to book
            $currentCustomerInfo = [
                'name'    => $this->extractCustomerNameFromMessage($message->message),
                'phone'   => $this->extractCustomerPhoneFromMessage($message->message),
                'email'   => $this->extractCustomerEmailFromMessage($message->message),
                'address' => $this->extractCustomerAddressFromMessage($message->message),
            ];

            // Check conversation history for customer information
            $conversationHistory = app(\App\Services\Chatbot\ConversationService::class)
                ->getConversationContext($message->sender_id);

            foreach (array_reverse($conversationHistory) as $historyMessage) {
                if (($historyMessage['role'] ?? '') === 'user') {
                    if (empty($currentCustomerInfo['name'])) {
                        $currentCustomerInfo['name'] = $this->extractCustomerNameFromMessage($historyMessage['content'] ?? '');
                    }
                    if (empty($currentCustomerInfo['phone'])) {
                        $currentCustomerInfo['phone'] = $this->extractCustomerPhoneFromMessage($historyMessage['content'] ?? '');
                    }
                    if (empty($currentCustomerInfo['email'])) {
                        $currentCustomerInfo['email'] = $this->extractCustomerEmailFromMessage($historyMessage['content'] ?? '');
                    }
                    if (empty($currentCustomerInfo['address'])) {
                        $currentCustomerInfo['address'] = $this->extractCustomerAddressFromMessage($historyMessage['content'] ?? '');
                    }
                }
            }

            $customerInfoCheck = $this->needsCustomerInfoCollection($userAgent, $currentCustomerInfo);
            if (!empty($customerInfoCheck['missing'])) {
                Log::warning('BOOKING BLOCKED: Customer information incomplete', [
                    'missing_fields' => $customerInfoCheck['missing'],
                    'message_id'     => $message->id,
                    'user_agent_id'  => $userAgent->id,
                ]);
                return []; // Return empty array to indicate booking cannot proceed
            }

            $businessData    = $this->ensureArrayFromJson($userAgent->agent_business_data);
            $schedulingRules = $businessData['scheduling_rules'] ?? [];

            // Validate quantity (simple clamp 1..10; seeder no longer controls this)
            $quantity = min(max($quantity, 1), 10);

            // Prefer forced values (from recent user message), fallback to current message extraction
            $extractedDate = $forcedDate ?: $this->extractDateFromMessage($message->message);
            $extractedTime = $forcedTime ?: $this->extractTimeFromMessage($message->message);

            if (!$extractedDate || !$extractedTime) {
                Log::error('BOOKING FAILED: Insufficient date/time information for booking', [
                    'extracted_date' => $extractedDate,
                    'extracted_time' => $extractedTime,
                    'message'        => $message->message,
                    'message_id'     => $message->id,
                ]);
                return [];
            }

            // Parse the date and time
            $dateTime = $this->parseFlexibleDateTime($extractedDate, $extractedTime);
            if (!$dateTime) {
                Log::error('BOOKING FAILED: Could not parse date/time', [
                    'extracted_date' => $extractedDate,
                    'extracted_time' => $extractedTime,
                    'message_id'     => $message->id,
                ]);
                return [];
            }

            // Check if the time is within business hours
            if (!$this->isWithinBusinessHours($userAgent, $dateTime)) {
                Log::error('BOOKING FAILED: Time is outside business hours', [
                    'date_time'     => $dateTime->toISOString(),
                    'message_id'    => $message->id,
                    'user_agent_id' => $userAgent->id,
                ]);
                return [];
            }

            // Get service information (needed for duration-based overlap checks)
            $service = $intent['specific_services'][0] ?? null;
            Log::info('Service extraction for booking', [
                'message_id'      => $message->id,
                'service_found'   => !is_null($service),
                'service'         => $service,
                'intent_services' => $intent['specific_services'] ?? [],
            ]);

            if (!$service) {
                // If no service found in intent, create a default service based on conversation context
                $service = [
                    'key'      => 'general',
                    'name'     => 'General Service',
                    'price'    => null,
                    'duration' => 120,
                    'category' => 'general',
                ];

                Log::warning('No service found in intent - using default service', [
                    'message_id' => $message->id,
                    'service'    => $service,
                    'intent'     => $intent,
                ]);
            }

            // Concurrency and day-limit checks for flexible scheduling
            $defaultDuration = (int) ($schedulingRules['default_durations'] ?? $schedulingRules['default_duration_minutes'] ?? 120);
            $durationMinutes = (int) ($service['duration'] ?? $defaultDuration);
            $dateString      = $dateTime->format('Y-m-d');

            $configuredMax = (int) ($schedulingRules['max_concurrent_bookings'] ?? 1);
            $maxConcurrent = max(1, $configuredMax);

            $overlapCount = $this->getExistingBookingsAtTime($userAgent, $dateTime, $durationMinutes, $maxConcurrent);

            $availableCapacity = $maxConcurrent - $overlapCount;

            // Daily limit
            $maxPerDay      = (int) ($schedulingRules['max_bookings_per_day'] ?? PHP_INT_MAX);
            $currentDayLoad = app(\App\Repositories\Appointment\AppointmentRepositoryInterface::class)
                ->queryAppointments([
                    'type'          => 'count',
                    'user_agent_id' => $userAgent->id,
                    'date'          => $dateString,
                ]);
            $remainingDayCap = $maxPerDay - (int) $currentDayLoad;

            // Clamp quantity to capacity constraints
            $effectiveQuantity = $quantity;
            $effectiveQuantity = min($effectiveQuantity, max(0, $availableCapacity));
            $effectiveQuantity = min($effectiveQuantity, max(0, $remainingDayCap));

            if ($effectiveQuantity <= 0) {
                Log::error('BOOKING FAILED: Capacity exceeded for requested time', [
                    'overlap_count'      => $overlapCount,
                    'max_concurrent'     => $maxConcurrent,
                    'available_capacity' => $availableCapacity,
                    'remaining_day_cap'  => $remainingDayCap,
                    'duration_minutes'   => $durationMinutes,
                    'date'               => $dateString,
                    'start'              => $dateTime->format('H:i:s'),
                    'end'                => $dateTime->copy()->addMinutes($durationMinutes)->format('H:i:s'),
                    'message_id'         => $message->id,
                ]);
                return [];
            }

            $bookedAppointments = [];

            // Create multiple appointments if requested
            for ($i = 0; $i < $effectiveQuantity; $i++) {
                $appointment = $this->createFlexibleAppointment($message, $userAgent, $dateTime, $service, $i + 1);
                if ($appointment) {
                    $bookedAppointments[] = $appointment;
                }
            }

            return $bookedAppointments;

        } catch (Exception $e) {
            Log::error('Error attempting flexible booking', [
                'error'         => $e->getMessage(),
                'message_id'    => $message->id,
                'user_agent_id' => $userAgent?->id,
            ]);
            return [];
        }
    }

    // Removed unused generateBookingConfirmation(Appointment) – flexible confirmation is used

    /**
     * Generate flexible booking confirmation
     */
    private function generateFlexibleBookingConfirmation(array $appointments): string
    {
        if (empty($appointments)) {
            return "I'm sorry, but I couldn't complete your booking. Please try again or contact us directly.";
        }

        $appointment = $appointments[0];
        $count       = count($appointments);

        $lines   = [];
        $lines[] = '✅ Booking confirmed!';
        if ($count > 1) {
            $lines[] = "Booked appointments: {$count}";
            $idsList = array_map(fn($apt) => '#' . $apt->id, $appointments);
            $lines[] = 'Appointment numbers: ' . implode(', ', $idsList);
        } else {
            $lines[] = 'Appointment number: #' . $appointment->id;
        }

        $lines[] = 'Date: ' . ($appointment->appointment_date instanceof \Carbon\CarbonInterface
            ? $appointment->appointment_date->format('Y-m-d')
            : (string) $appointment->appointment_date);
        $lines[] = 'Time: ' . $this->formatTimeForDisplay($appointment->appointment_time);
        $lines[] = 'Duration: ' . $appointment->duration_minutes . ' minutes';
        $lines[] = 'Service: ' . ($appointment->service_name ?? 'Service');

        if ($count > 1) {
            $lines[] = 'Note: These are scheduled concurrently.';
        }

        $confirmation = implode("\n- ", $lines);
        $confirmation = '- ' . $confirmation; // prefix the first line for consistent bullets
        $confirmation .= "\n\nYou will receive a confirmation shortly. To modify, contact us at least 24 hours in advance.";

        return $confirmation;
    }

    /**
     * Get available appointment slots based on UserAgent configuration
     */
    public function getAvailableSlots(UserAgent $userAgent, int $days = 7): array
    {
        // Flexible scheduling: disable slot generation entirely
        return [];
    }

    /**
     * Generate time slots for a specific day
     */
    private function generateTimeSlotsForDay(Carbon $date, array $dayHours, int $slotDuration, int $bufferMinutes, int $maxSlots, UserAgent $userAgent): array
    {
        // Deprecated for flexible scheduling
        return [];
    }

    /**
     * Check if a specific time slot is available
     */
    public function isSlotAvailable(UserAgent $userAgent, Carbon $dateTime): bool
    {
        try {
            $businessData    = $this->ensureArrayFromJson($userAgent->agent_business_data);
            $schedulingRules = $businessData['scheduling_rules'] ?? [];

            // Check if we've reached max bookings for this day
            $maxBookingsPerDay = $schedulingRules['max_bookings_per_day'] ?? 10;
            $dateString        = $dateTime->format('Y-m-d');

            $existingBookings = app(\App\Repositories\Appointment\AppointmentRepositoryInterface::class)
                ->queryAppointments([
                    'type'          => 'count',
                    'user_agent_id' => $userAgent->id,
                    'date'          => $dateString,
                ]);

            if ($existingBookings >= $maxBookingsPerDay) {
                return false;
            }

            // Check for double booking if not allowed
            // Single-slot availability is superseded by flexible concurrency; skip strict time-slot check
            if (false) {
                $timeSlot           = $dateTime->format('H:i:s');
                $conflictingBooking = app(\App\Repositories\Appointment\AppointmentRepositoryInterface::class)
                    ->isTimeSlotAvailable($dateString, $timeSlot, $userAgent->id) === false;

                if ($conflictingBooking) {
                    return false;
                }
            }

            // Check business hours
            $dayName       = strtolower($dateTime->format('l'));
            $businessHours = $businessData['business_hours'] ?? [];

            if (!isset($businessHours[$dayName]) || $businessHours[$dayName] === null) {
                return false;
            }

            $dayHours = $businessHours[$dayName];
            if (!is_array($dayHours) || count($dayHours) < 2) {
                return false;
            }

            $timeString = $dateTime->format('H:i');
            return $timeString >= $dayHours[0] && $timeString <= $dayHours[1];

        } catch (Exception $e) {
            Log::error('Error checking slot availability', [
                'error'         => $e->getMessage(),
                'datetime'      => $dateTime->toISOString(),
                'user_agent_id' => $userAgent->id,
            ]);
            return false;
        }
    }

    /**
     * Extract date from message content
     */
    private function extractDateFromMessage(string $content): ?string
    {
        $content = strtolower($content);

        // ISO date first: YYYY-MM-DD
        if (preg_match('/\b(20\d{2})-(\d{2})-(\d{2})\b/', $content, $m)) {
            try {
                $year  = (int) $m[1];
                $month = (int) $m[2];
                $day   = (int) $m[3];
                $date  = Carbon::create($year, $month, $day);
                if ($date) {
                    return $date->format('Y-m-d');
                }
            } catch (Exception $e) {
                // ignore
            }
        }

        // ISO with slashes: YYYY/MM/DD
        if (preg_match('/\b(20\d{2})\/(\d{2})\/(\d{2})\b/', $content, $m)) {
            try {
                $year  = (int) $m[1];
                $month = (int) $m[2];
                $day   = (int) $m[3];
                $date  = Carbon::create($year, $month, $day);
                if ($date) {
                    return $date->format('Y-m-d');
                }
            } catch (Exception $e) {
                // ignore
            }
        }

        // Check for relative dates
        if (strpos($content, 'today') !== false) {
            return Carbon::today()->format('Y-m-d');
        }

        if (strpos($content, 'tomorrow') !== false) {
            return Carbon::tomorrow()->format('Y-m-d');
        }

        // Check for "next week" - default to next Monday
        if (strpos($content, 'next week') !== false) {
            return Carbon::parse('next monday')->format('Y-m-d');
        }

        // Check for day names (this week or next week)
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        foreach ($days as $day) {
            if (strpos($content, $day) !== false) {
                $nextDay = Carbon::parse('next ' . $day);
                // If the day is today or past this week, get next week's occurrence
                if ($nextDay->diffInDays(Carbon::now()) > 7) {
                    $nextDay = Carbon::parse('this ' . $day);
                }
                return $nextDay->format('Y-m-d');
            }
        }

        // Check for month names with day numbers (e.g., "august 8", "8th august", "11 aug")
        $months = [
            'january'   => 1, 'february' => 2, 'march'     => 3, 'april'     => 4,
            'may'       => 5, 'june'     => 6, 'july'      => 7, 'august'    => 8,
            'september' => 9, 'october'  => 10, 'november' => 11, 'december' => 12,
            'jan'       => 1, 'feb'      => 2, 'mar'       => 3, 'apr'       => 4,
            'jun'       => 6, 'jul'      => 7, 'aug'       => 8, 'sep'       => 9,
            'oct'       => 10, 'nov'     => 11, 'dec'      => 12,
        ];

        foreach ($months as $monthName => $monthNumber) {
            // Pattern: "august 8", "8 august", "8th august", "11 aug", "aug 11"
            $patterns = [
                '/\b' . $monthName . '\s+(\d{1,2})(?:st|nd|rd|th)?\b/', // "august 8" or "aug 11"
                '/\b(\d{1,2})(?:st|nd|rd|th)?\s+' . $monthName . '\b/', // "8 august" or "11 aug"
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $content, $matches)) {
                    try {
                        $day  = (int) $matches[1];
                        $year = Carbon::now()->year;

                        $date = Carbon::create($year, $monthNumber, $day);

                        // If the date is in the past, assume next year
                        if ($date->isPast()) {
                            $date->addYear();
                        }

                        return $date->format('Y-m-d');
                    } catch (Exception $e) {
                        continue;
                    }
                }
            }
        }

        // Check for date patterns (MM/DD, MM-DD, etc.)
        if (preg_match('/(\d{1,2})[\/\-](\d{1,2})/', $content, $matches)) {
            try {
                $month = (int) $matches[1];
                $day   = (int) $matches[2];
                $year  = Carbon::now()->year;

                $date = Carbon::create($year, $month, $day);
                if ($date->isPast()) {
                    $date->addYear();
                }

                return $date->format('Y-m-d');
            } catch (Exception $e) {
                return null;
            }
        }

        // Check for standalone day numbers (assume current month)
        // Guard: if the message already contains a time (e.g., "10 am"), do NOT treat the number as a day-of-month
        if (preg_match('/\b(\d{1,2})(?::\d{2})?\s*(am|pm)\b/i', $content)) {
            // Time present without explicit date; don't infer a date from the number
            return null;
        }
        if (preg_match('/\b(\d{1,2})(?:st|nd|rd|th)?\b/', $content, $matches)) {
            try {
                $day = (int) $matches[1];
                if ($day >= 1 && $day <= 31) {
                    $date = Carbon::now()->startOfMonth()->addDays($day - 1);

                    // If the date is in the past, assume next month
                    if ($date->isPast()) {
                        $date->addMonth();
                    }

                    return $date->format('Y-m-d');
                }
            } catch (Exception $e) {
                return null;
            }
        }

        return null;
    }

    /**
     * Extract time from message content
     */
    private function extractTimeFromMessage(string $content): ?string
    {
        $content = strtolower($content);

        // Fix common typos like "an" instead of "am" (only when attached to a number)
        $content = preg_replace('/\b(\d{1,2})\s*an\b/i', '$1 am', $content);
        $content = preg_replace('/\b(\d{1,2})\s*pn\b/i', '$1 pm', $content);

        // Check for common time phrases including flexible expressions
        $timeMap = [
            'tomorrow after morning' => '13:00',
            'after morning'          => '13:00',
            'morning'                => '10:00',
            'lunch'                  => '12:00',
            'after lunch'            => '13:00',
            'afternoon'              => '14:00',
            'evening'                => '18:00',
            'night'                  => '20:00',
            'noon'                   => '12:00',
            'lunch time'             => '12:00',
        ];

        foreach ($timeMap as $phrase => $time) {
            if (strpos($content, $phrase) !== false) {
                return $time;
            }
        }

        // Look for time patterns like "2:30 PM", "14:30", "2 PM", "8:00 am", etc.
        if (preg_match('/(\d{1,2}):?(\d{2})?\s*(am|pm)/i', $content, $matches)) {
            $hour   = (int) $matches[1];
            $minute = isset($matches[2]) ? (int) $matches[2] : 0;
            $ampm   = strtolower($matches[3]);

            if ($ampm === 'pm' && $hour !== 12) {
                $hour += 12;
            } elseif ($ampm === 'am' && $hour === 12) {
                $hour = 0;
            }

            return sprintf('%02d:%02d', $hour, $minute);
        }

        // Look for 24-hour format (e.g., "14:30")
        if (preg_match('/(\d{1,2}):(\d{2})/', $content, $matches)) {
            $hour   = (int) $matches[1];
            $minute = (int) $matches[2];

            if ($hour >= 0 && $hour <= 23 && $minute >= 0 && $minute <= 59) {
                return sprintf('%02d:%02d', $hour, $minute);
            }
        }

        // Look for just numbers that could be hours (like "1 pm" without the colon)
        if (preg_match('/\b(\d{1,2})\s*(pm|am)\b/i', $content, $matches)) {
            $hour = (int) $matches[1];
            $ampm = strtolower($matches[2]);

            if ($ampm === 'pm' && $hour !== 12) {
                $hour += 12;
            } elseif ($ampm === 'am' && $hour === 12) {
                $hour = 0;
            }

            if ($hour >= 0 && $hour <= 23) {
                return sprintf('%02d:00', $hour);
            }
        }

        return null;
    }

    /**
     * Extract quantity from message
     */
    private function extractQuantityFromMessage(string $content): int
    {
        $content = strtolower($content);

        // Look for quantity patterns
        $patterns = [
            '/(\d+)\s*(appointment|booking|slot|time|session)/',
            '/(\d+)\s*(of|for)\s*(appointment|booking|slot|time|session)/',
            '/book\s*(\d+)/',
            '/schedule\s*(\d+)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $quantity = (int) $matches[1];
                return min(max($quantity, 1), 10); // Limit to 1-10
            }
        }

        // Look for words like "multiple", "several", "few"
        if (preg_match('/(multiple|several|few|many)/', $content)) {
            return 3; // Default to 3 for multiple
        }

        return 1; // Default to 1
    }

    /**
     * Parse flexible date and time
     */
    private function parseFlexibleDateTime(string $date, string $time): ?Carbon
    {
        try {
            // Try to parse the date
            $dateObj = null;

            // Handle various date formats
            $dateFormats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'M j, Y', 'j M Y', 'Y-m-d\TH:i:sP'];
            foreach ($dateFormats as $format) {
                try {
                    $dateObj = Carbon::createFromFormat($format, $date);
                    break;
                } catch (Exception $e) {
                    continue;
                }
            }

            if (!$dateObj) {
                // Try relative dates
                $relativeDates = [
                    'today'      => Carbon::today(),
                    'tomorrow'   => Carbon::tomorrow(),
                    'next week'  => Carbon::now()->addWeek(),
                    'next month' => Carbon::now()->addMonth(),
                ];

                $dateLower = strtolower($date);
                foreach ($relativeDates as $key => $value) {
                    if (strpos($dateLower, $key) !== false) {
                        $dateObj = $value;
                        break;
                    }
                }
            }

            if (!$dateObj) {
                return null;
            }

            // Parse the time
            $timeObj     = null;
            $timeFormats = ['H:i', 'h:i A', 'g:i A', 'H:i:s'];

            foreach ($timeFormats as $format) {
                try {
                    $timeObj = Carbon::createFromFormat($format, $time);
                    break;
                } catch (Exception $e) {
                    continue;
                }
            }

            if (!$timeObj) {
                return null;
            }

            // Combine date and time
            $dateTime = $dateObj->copy()->setTime($timeObj->hour, $timeObj->minute, $timeObj->second);

            // Ensure it's in the future
            if ($dateTime->isPast()) {
                return null;
            }

            return $dateTime;

        } catch (Exception $e) {
            Log::error('Error parsing flexible date time', [
                'date'  => $date,
                'time'  => $time,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check if time is within business hours
     */
    private function isWithinBusinessHours(UserAgent $userAgent, Carbon $dateTime): bool
    {
        $businessData  = $this->ensureArrayFromJson($userAgent->agent_business_data);
        $businessHours = $businessData['business_hours'] ?? [];

        $dayName = strtolower($dateTime->format('l'));

        if (!isset($businessHours[$dayName]) || !$businessHours[$dayName]) {
            return false;
        }

        $dayHours = $businessHours[$dayName];
        if (!is_array($dayHours) || count($dayHours) < 2) {
            return false;
        }

        $appointmentTime = $dateTime->format('H:i');

        return $appointmentTime >= $dayHours[0] && $appointmentTime <= $dayHours[1];
    }

    /**
     * Get existing bookings at a specific time
     */
    private function getExistingBookingsAtTime(UserAgent $userAgent, Carbon $dateTime, int $durationMinutes, int $maxConcurrent): int
    {
        $dateString = $dateTime->format('Y-m-d');
        $startTime  = $dateTime->format('H:i:s');
        $endTime    = $dateTime->copy()->addMinutes($durationMinutes)->format('H:i:s');

        return app(\App\Repositories\Appointment\AppointmentRepositoryInterface::class)
            ->countOverlappingBookings($userAgent->id, $dateString, $startTime, $endTime);
    }

    /**
     * Create a flexible appointment
     */
    private function createFlexibleAppointment(Message $message, UserAgent $userAgent, Carbon $dateTime, array $service, int $sequenceNumber): ?Appointment
    {
        Log::info('=== CREATING FLEXIBLE APPOINTMENT ===', [
            'message_id'      => $message->id,
            'user_agent_id'   => $userAgent->id,
            'date_time'       => $dateTime->toISOString(),
            'service'         => $service,
            'sequence_number' => $sequenceNumber,
        ]);

        try {
            $businessData    = $this->ensureArrayFromJson($userAgent->agent_business_data);
            $rulesArray      = $businessData['scheduling_rules'] ?? [];
            $defaultDuration = (int) ($rulesArray['default_durations'] ?? $rulesArray['default_duration_minutes'] ?? 120);
            $duration        = $service['duration'] ?? $defaultDuration; // Use default from scheduling rules
            // Clamp to sensible bounds
            $duration = min(max($duration, 30), 480);

            // Extract customer information from message
            $customerName    = $this->extractCustomerNameFromMessage($message->message);
            $customerPhone   = $this->extractCustomerPhoneFromMessage($message->message);
            $customerEmail   = $this->extractCustomerEmailFromMessage($message->message);
            $customerAddress = $this->extractCustomerAddressFromMessage($message->message);

            // Resolve platform information
            $platformInfo = $this->resolvePlatformInfo($message, $userAgent);

            // Resolve customer name: extract from text, then IG username by message_id, then fallback
            if (!$customerName) {
                $resolvedUsername = null;
                try {
                    if ((($message->platform ?? 'instagram') === 'instagram') && !empty($message->message_id)) {
                        $resolvedUsername = app(\App\Services\InstagramService::class)
                            ->getUsernameByMessageId((string) $message->message_id, (string) ($message->recipient_id ?? null));
                    }
                } catch (\Throwable $e) {
                    // ignore resolution errors
                }
                $customerName = $resolvedUsername ?? $message->sender_id;
            }

            Log::info('APPOINTMENT: Prepared appointment data', [
                'customer_name'    => $customerName,
                'customer_phone'   => $customerPhone,
                'customer_email'   => $customerEmail,
                'customer_address' => $customerAddress,
                'platform_info'    => $platformInfo,
                'user_id'          => $userAgent->user_id,
                'user_agent_id'    => $userAgent->id,
                'appointment_date' => $dateTime->format('Y-m-d'),
                'appointment_time' => $dateTime->format('H:i:s'),
                'service_name'     => $service['name'] ?? 'General Service',
                'duration'         => $duration,
            ]);

            // Resolve platform token (if any)
            $token = $this->findTokenForAppointment($message, $userAgent);

            // Compute final service price with possible weekend multiplier
            $finalPrice = $service['price'] ?? null;
            try {
                $businessData  = $this->ensureArrayFromJson($userAgent->agent_business_data);
                $weekend       = $businessData['weekend_settings'] ?? [];
                $dayName       = strtolower($dateTime->format('l'));
                $isWeekendDay  = in_array($dayName, ['saturday', 'sunday']);
                $multiplier    = $weekend['weekend_pricing_multiplier'] ?? 1;
                $weekendEnable = ($dayName === 'saturday' && ($weekend['saturday_enabled'] ?? false)) ||
                    ($dayName === 'sunday' && ($weekend['sunday_enabled'] ?? false));
                if ($finalPrice !== null && $isWeekendDay && $weekendEnable && $multiplier > 1) {
                    $finalPrice = round(((float) $finalPrice) * (float) $multiplier, 2);
                }
            } catch (\Exception $e) {
                // ignore price adjustment errors
            }

            $appointment = app(\App\Repositories\Appointment\AppointmentRepositoryInterface::class)->createAppointment([
                'customer_name'        => $customerName,
                'customer_phone'       => $customerPhone,
                'customer_email'       => $customerEmail,
                'platform_customer_id' => $platformInfo['customer_id'],
                'platform_username'    => $platformInfo['username'],
                'platform_name'        => $platformInfo['platform'],
                'user_id'              => $userAgent->user_id,
                'user_agent_id'        => $userAgent->id,
                'token_id'             => $token?->id,
                'appointment_date'     => $dateTime->format('Y-m-d'),
                'appointment_time'     => $dateTime->format('H:i:s'),
                'duration_minutes'     => $duration,
                'service_name'         => $service['name'] ?? 'General Service',
                'service_key'          => $service['key'] ?? null,
                'service_price'        => $finalPrice,
                'service_category'     => $service['category'] ?? null,
                'status'               => 'pending',
                'notes'                => "Flexible booking - Sequence {$sequenceNumber}",
                'metadata'             => [
                    'flexible_booking' => true,
                    'sequence_number'  => $sequenceNumber,
                    'service_details'  => $service,
                    'original_message' => $message->message,
                ],
                'booked_at'            => now(),
                'booking_method'       => 'chatbot',
            ]);

            Log::info('Appointment created successfully', [
                'appointment_id'   => $appointment->id,
                'customer_name'    => $customerName,
                'platform_info'    => $platformInfo,
                'service_name'     => $service['name'] ?? 'General Service',
                'appointment_date' => $dateTime->format('Y-m-d'),
                'appointment_time' => $dateTime->format('H:i:s'),
            ]);

            return $appointment;

        } catch (Exception $e) {
            Log::error('CRITICAL: Error creating flexible appointment', [
                'error'         => $e->getMessage(),
                'error_code'    => $e->getCode(),
                'file'          => $e->getFile(),
                'line'          => $e->getLine(),
                'user_agent_id' => $userAgent->id,
                'date_time'     => $dateTime->toISOString(),
                'service'       => $service,
                'message_id'    => $message->id,
                'sender_id'     => $message->sender_id,
                'customer_name' => $customerName ?? 'not_extracted',
                'trace'         => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    /**
     * Extract service keywords from UserAgent business data
     */
    private function getServiceKeywordsFromUserAgent(?UserAgent $userAgent): array
    {
        if (!$userAgent) {
            return ['consultation' => ['name' => 'consultation', 'category' => 'service']];
        }

        try {
            $businessData = $this->ensureArrayFromJson($userAgent->agent_business_data);

            if (isset($businessData['business_services']) && is_array($businessData['business_services'])) {
                return $businessData['business_services'];
            }

            return ['consultation' => ['name' => 'consultation', 'category' => 'service']];

        } catch (Exception $e) {
            Log::warning('Failed to extract service keywords from user agent', [
                'error'         => $e->getMessage(),
                'user_agent_id' => $userAgent->id,
            ]);
            return ['consultation' => ['name' => 'consultation', 'category' => 'service']];
        }
    }

    /**
     * Find the appropriate token for this appointment based on message and user agent
     */
    private function findTokenForAppointment(Message $message, UserAgent $userAgent): ?\App\Models\Token
    {
        try {
            // Prefer token_id from message metadata if present
            $tokenIdFromMeta = $message->meta_data['token_id'] ?? null;
            if ($tokenIdFromMeta) {
                $token = \App\Models\Token::find((int) $tokenIdFromMeta);
                if ($token) {
                    return $token;
                }
            }

            // Try by platform + user
            $platformKey = $message->platform ?? 'instagram';
            $platformId  = (int) (\App\Models\Platform::where('key', $platformKey)->value('id') ?? 0);
            if ($platformId > 0) {
                $token = app(\App\Repositories\Token\TokenRepositoryInterface::class)
                    ->findByPlatformAndUser($platformId, (int) $userAgent->user_id);
                if ($token) {
                    return $token;
                }
            }

            // Fallback: Get any active token for this user and platform
            return app(\App\Repositories\Token\TokenRepositoryInterface::class)
                ->getActiveTokenForUserAndPlatform((int) $userAgent->user_id, $platformId);

        } catch (Exception $e) {
            Log::warning('Failed to find token for appointment', [
                'error'         => $e->getMessage(),
                'user_agent_id' => $userAgent->id,
                'message_id'    => $message->id,
            ]);
            return null;
        }
    }

    /**
     * Resolve platform information from message and user agent
     */
    private function resolvePlatformInfo(Message $message, UserAgent $userAgent) : array
    {
        $platform = $message->platform ?? 'instagram';

        // For Instagram, try to get username from message_id
        $username   = null;
        $customerId = $message->sender_id;

        if ($platform === 'instagram' && !empty($message->message_id)) {
            try {
                $username = app(\App\Services\InstagramService::class)
                    ->getUsernameByMessageId((string) $message->message_id, (string) ($message->recipient_id ?? null));
            } catch (\Throwable $e) {
                // ignore resolution errors
            }
        }

        return [
            'platform'    => $platform,
            'customer_id' => $customerId,
            'username'    => $username,
        ];
    }

    /**
     * Get recent conversation context for intelligent analysis
     */
    private function getRecentConversationContext(string $currentMessage, ?string $senderId): array
    {
        if (!$senderId) {
            return [];
        }

        // Get last 10 messages from conversation via repository
        $recentMessages = $this->messageRepository->getConversationHistory($senderId, 10)
            ->map(fn($m) => (object) ['message' => $m->message, 'reply' => $m->reply, 'created_at' => $m->created_at]);

        $context = [];
        foreach ($recentMessages as $msg) {
            if ($msg->message) {
                $context[] = [
                    'role'      => 'user',
                    'content'   => $msg->message,
                    'timestamp' => $msg->created_at,
                ];
            }
            if ($msg->reply) {
                $context[] = [
                    'role'      => 'assistant',
                    'content'   => $msg->reply,
                    'timestamp' => $msg->created_at,
                ];
            }
        }

        return array_reverse($context); // Chronological order
    }

    /**
     * Analyze booking context using AI-powered understanding
     */
    private function analyzeBookingContextWithAI(string $message, array $conversationHistory, ?UserAgent $userAgent): array
    {
        // Default intent scaffold
        $intent = [
            'detected'          => false,
            'confidence'        => 0,
            'urgency'           => 'normal',
            'service_mentioned' => false,
            'specific_services' => [],
            'keywords_found'    => [],
            'booking_stage'     => 'none', // none, interested, selecting, scheduling, confirming
            'ready_to_book'     => false,
            // LLM extras
            'llm_date'          => null,
            'llm_time'          => null,
            'llm_quantity'      => null,
            'llm_confirmation'  => 'none', // none|affirm|explicit
        ];

        try {
            // Build compact services context
            $servicesCtx = [];
            if ($userAgent) {
                $biz = $this->ensureArrayFromJson($userAgent->agent_business_data);
                foreach (($biz['business_services'] ?? []) as $key => $svc) {
                    if (is_array($svc)) {
                        $servicesCtx[] = [
                            'key'      => (string) $key,
                            'name'     => (string) ($svc['name'] ?? $key),
                            'duration' => (int) ($svc['duration'] ?? 60),
                        ];
                    }
                }
            }

            // Prepare recent conversation (limit to last ~8 turns)
            $history     = array_slice($conversationHistory, max(0, count($conversationHistory) - 16));
            $historyText = '';
            foreach ($history as $h) {
                $r = $h['role'] ?? 'user';
                $c = $h['content'] ?? '';
                if ($c !== '') {
                    $historyText .= strtoupper($r) . ': ' . $c . "\n";
                }
            }
            $historyText .= 'USER: ' . $message;

            // LLM classification prompt
            $prompt = "You are an expert booking assistant. Analyze the conversation and classify the user's intent to book an appointment.\n" .
            "IMPORTANT: Only set 'detected' to true if the user wants to BOOK/SCHEDULE an appointment, not just ask for information.\n" .
            "Information requests (tell me about, what is, more details, explain) should have detected=false.\n" .
            "Booking requests (book, schedule, appointment, when can I, I want to book) should have detected=true.\n" .
            "Return STRICT MINIFIED JSON ONLY, no prose. Use this JSON schema: {\"detected\":bool,\"booking_stage\":\"none|interested|selecting|scheduling|confirming\",\"ready_to_book\":bool,\"service\":{\"key\":string,\"name\":string,\"duration\":number}|null,\"date\":\"YYYY-MM-DD\"|null,\"time\":\"HH:MM\"|null,\"quantity\":number|null,\"confirmation\":\"none|affirm|explicit\"}.\n" .
            "Do not invent details. If uncertain, set null.\n\n" .
            "SERVICES: " . json_encode($servicesCtx) . "\n\n" .
                "CONVERSATION:\n" . $historyText . "\n\n" .
                "Now output ONLY the JSON.";

            $ai  = app(\App\Services\Chatbot\ChatbotAIService::class);
            $raw = $ai->generateResponse($prompt);

            $json = $this->extractJson($raw);
            if (is_array($json)) {
                $intent['detected']         = (bool) ($json['detected'] ?? false);
                $intent['booking_stage']    = (string) ($json['booking_stage'] ?? 'none');
                $intent['ready_to_book']    = (bool) ($json['ready_to_book'] ?? false);
                $intent['llm_date']         = $json['date'] ?? null;
                $intent['llm_time']         = $json['time'] ?? null;
                $intent['llm_quantity']     = $json['quantity'] ?? null;
                $intent['llm_confirmation'] = (string) ($json['confirmation'] ?? 'none');

                if (!empty($json['service']) && is_array($json['service'])) {
                    $svc                           = $json['service'];
                    $intent['service_mentioned']   = true;
                    $intent['specific_services'][] = $this->normalizeServiceInfo([
                        'key'      => $svc['key'] ?? null,
                        'name'     => $svc['name'] ?? 'Service',
                        'duration' => $svc['duration'] ?? 60,
                        'price'    => null,
                        'category' => 'service',
                    ]);
                }

                // Derive a simple confidence score
                $intent['confidence'] = ($intent['detected'] ? 0.6 : 0.0) + ($intent['ready_to_book'] ? 0.2 : 0.0);
            }

            Log::info('APPOINTMENT: LLM intent classification', [
                'detected'      => $intent['detected'],
                'stage'         => $intent['booking_stage'],
                'ready_to_book' => $intent['ready_to_book'],
                'llm_date'      => $intent['llm_date'],
                'llm_time'      => $intent['llm_time'],
                'llm_quantity'  => $intent['llm_quantity'],
                'llm_confirm'   => $intent['llm_confirmation'],
            ]);

        } catch (\Throwable $e) {
            Log::warning('APPOINTMENT: LLM classification failed, will fallback to flow heuristics', [
                'error' => $e->getMessage(),
            ]);

            // Fallback to existing flow heuristics
            $conversationFlow = $this->analyzeConversationFlow($conversationHistory, $message);
            if ($this->isInBookingFlow($conversationFlow, $message)) {
                $intent['detected']      = true;
                $intent['confidence']    = 0.6;
                $intent['booking_stage'] = $this->determineBookingStage($conversationFlow, $message);
                $serviceInfo             = $this->extractServiceFromContext($conversationFlow, $message, $userAgent);
                if ($serviceInfo) {
                    $intent['service_mentioned']   = true;
                    $intent['specific_services'][] = $serviceInfo;
                }
                $intent['ready_to_book'] = $this->isReadyToBook($conversationFlow, $message, $serviceInfo);
            }
        }

        return $intent;
    }

    /**
     * Extract a JSON object from text and decode it
     */
    private function extractJson(?string $text): ?array
    {
        if (!$text) {
            return null;
        }
        // Find first {...} block
        if (preg_match('/\{[\s\S]*\}/', $text, $m)) {
            $decoded = json_decode($m[0], true);
            return is_array($decoded) ? $decoded : null;
        }
        // Try direct decode
        $decoded = json_decode($text, true);
        return is_array($decoded) ? $decoded : null;
    }

    /**
     * Analyze conversation flow to understand booking patterns
     */
    private function analyzeConversationFlow(array $conversationHistory, string $currentMessage): array
    {
        $flow = [
            'has_service_inquiry'       => false,
            'has_service_selection'     => false,
            'has_time_discussion'       => false,
            'has_price_discussion'      => false,
            'assistant_offered_booking' => false,
            'user_confirmed_interest'   => false,
        ];

        $allMessages = array_merge($conversationHistory, [['role' => 'user', 'content' => $currentMessage]]);

        foreach ($allMessages as $msg) {
            $content = strtolower($msg['content']);

            if ($msg['role'] === 'user') {
                // User expressing interest in services
                if (preg_match('/(want|need|interested|looking for).*(wrap|color|paint|tint|protect)/i', $content) ||
                    preg_match('/(wrap|color|paint|tint|protect).*(car|vehicle|auto)/i', $content)) {
                    $flow['has_service_inquiry'] = true;
                }

                // User selecting specific service
                if (preg_match('/(ok|yes|sure).*(full|partial|complete).*(wrap|service)/i', $content) ||
                    preg_match('/^(full|partial|complete).*(wrap|service)/i', $content)) {
                    $flow['has_service_selection']   = true;
                    $flow['user_confirmed_interest'] = true;
                }

                // User providing time preferences
                if (preg_match('/(tomorrow|today|next|morning|afternoon|evening|\d+:\d+|am|pm)/i', $content)) {
                    $flow['has_time_discussion'] = true;
                }
            } else {
                // Assistant offering services/prices
                if (preg_match('/(starts at|costs|\$\d+|takes about|hours)/i', $content)) {
                    $flow['has_price_discussion'] = true;
                }

                // Assistant asking for booking details
                if (preg_match('/(what day|what time|when would|schedule|book)/i', $content)) {
                    $flow['assistant_offered_booking'] = true;
                }
            }
        }

        return $flow;
    }

    /**
     * Check if conversation is in booking flow
     */
    private function isInBookingFlow(array $flow, string $message): bool
    {
        $hasTimeExpression = $this->hasTimeExpression($message);

        // Strong indicators of booking flow - service inquiry alone is enough to start
        $isInFlow = $flow['has_service_inquiry'] || (
            $flow['has_service_selection'] ||
            $flow['assistant_offered_booking'] ||
            $flow['has_time_discussion'] ||
            $hasTimeExpression
        );

        Log::info('APPOINTMENT: Checking booking flow', [
            'has_service_inquiry'       => $flow['has_service_inquiry'],
            'has_service_selection'     => $flow['has_service_selection'],
            'assistant_offered_booking' => $flow['assistant_offered_booking'],
            'has_time_discussion'       => $flow['has_time_discussion'],
            'has_time_expression'       => $hasTimeExpression,
            'is_in_booking_flow'        => $isInFlow,
            'message'                   => $message,
        ]);

        return $isInFlow;
    }

    /**
     * Determine current booking stage
     */
    private function determineBookingStage(array $flow, string $message): string
    {
        if ($flow['has_time_discussion'] || $this->hasTimeExpression($message)) {
            return 'scheduling';
        } elseif ($flow['has_service_selection']) {
            return 'selected';
        } elseif ($flow['has_service_inquiry']) {
            return 'interested';
        }
        return 'none';
    }

    /**
     * Check if message contains time expression
     */
    private function hasTimeExpression(string $message): bool
    {
        return (bool) preg_match('/(tomorrow|today|next|morning|afternoon|evening|after morning|\d+:\d+|am|pm|\d+\s*(am|pm))/i', $message);
    }

    /**
     * Extract service information from conversation context
     */
    private function extractServiceFromContext(array $flow, string $message, ?UserAgent $userAgent): ?array
    {
        // Check current message first
        $dynamicPatterns = $this->buildServicePatternsFromUserAgent($userAgent);
        foreach ($dynamicPatterns as $pattern => $serviceData) {
            if (preg_match('/' . preg_quote($pattern, '/') . '/i', $message)) {
                return $this->normalizeServiceInfo($serviceData);
            }
        }

        // If user confirmed service selection, assume they want the service mentioned by assistant
        if ($flow['has_service_selection'] && preg_match('/(ok|yes|sure|that)/i', $message)) {
            // Try to pick the last offered service name from patterns (fallback to first configured service)
            $services = array_values($dynamicPatterns);
            return !empty($services) ? $this->normalizeServiceInfo($services[0]) : null;
        }

        return null;
    }

    /**
     * Build dynamic service patterns from UserAgent business configuration
     */
    private function buildServicePatternsFromUserAgent(?UserAgent $userAgent): array
    {
        $patterns = [];
        if (!$userAgent) {
            return $patterns;
        }
        try {
            $businessData = $this->ensureArrayFromJson($userAgent->agent_business_data);
            $services     = $businessData['business_services'] ?? [];
            foreach ($services as $key => $svc) {
                if (!is_array($svc)) {
                    continue;
                }
                $name     = strtolower($svc['name'] ?? $key);
                $category = strtolower($svc['category'] ?? 'service');
                // Map multiple textual cues to the same service
                $patterns[$name]     = ['key' => $key, 'name' => $svc['name'] ?? $key, 'price' => $svc['price'] ?? null, 'duration' => $svc['duration'] ?? null, 'category' => $category];
                $patterns[$category] = ['key' => $key, 'name' => $svc['name'] ?? $key, 'price' => $svc['price'] ?? null, 'duration' => $svc['duration'] ?? null, 'category' => $category];
                // If service has aliases, include them
                if (!empty($svc['aliases']) && is_array($svc['aliases'])) {
                    foreach ($svc['aliases'] as $alias) {
                        $alias = strtolower((string) $alias);
                        if ($alias !== '') {
                            $patterns[$alias] = ['key' => $key, 'name' => $svc['name'] ?? $key, 'price' => $svc['price'] ?? null, 'duration' => $svc['duration'] ?? null, 'category' => $category];
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed building service patterns from UserAgent', ['error' => $e->getMessage()]);
        }
        return $patterns;
    }

    /**
     * Normalize service info array to consistent structure
     */
    private function normalizeServiceInfo(array $service): array
    {
        return [
            'key'      => $service['key'] ?? null,
            'name'     => $service['name'] ?? 'Service',
            'price'    => $service['price'] ?? null,
            'duration' => $service['duration'] ?? 60,
            'category' => $service['category'] ?? 'service',
        ];
    }

    /**
     * Check if ready to book (has service and time)
     */
    private function isReadyToBook(array $flow, string $message, ?array $serviceInfo): bool
    {
        return $serviceInfo !== null && (
            $flow['has_time_discussion'] ||
            $this->hasTimeExpression($message)
        );
    }

    /**
     * Extract customer name from message content
     */
    private function extractCustomerNameFromMessage(string $message): ?string
    {
        // Pattern to match name introductions
        $patterns = [
            '/(?:my name is|i\'m|i am|name is|call me)\s+([a-zA-Z\s]{2,50}?)(?:\s+and|\.|\s*$)/i',
            '/name:\s*([a-zA-Z\s]{2,50})(?:\s+and|,|\.|$)/i',
            '/^([a-zA-Z]{2,15}(?:\s+[a-zA-Z]{2,15}){1,3})(?:\s+and|\s+phone|\s+number|\s+\d|,|$)/i', // Full name pattern (2-4 words)
            '/\b([a-zA-Z]{2,15}\s+[a-zA-Z]{2,15}(?:\s+[a-zA-Z]{2,15})?)\b/i', // Fallback: 2-3 word names anywhere in message
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $message, $matches)) {
                $name = trim($matches[1]);
                // Validate name (should be 2-4 words, each 2+ characters)
                if (preg_match('/^[a-zA-Z]{2,15}(\s+[a-zA-Z]{2,15}){1,3}$/', $name)) {
                    // Avoid common false positives
                    $lowerName      = strtolower($name);
                    $falsePositives = ['phone number', 'email address', 'full vehicle', 'vehicle wrap', 'august th'];
                    if (!in_array($lowerName, $falsePositives)) {
                        return ucwords(strtolower($name));
                    }
                }
            }
        }

        return null;
    }

    /**
     * Extract customer phone from message content
     */
    private function extractCustomerPhoneFromMessage(string $message): ?string
    {
        // Phone number patterns - support international formats
        $patterns = [
            // Explicit phone mentions (highest priority)
            '/(?:phone|mobile|cell|tel|call|text|sms)\s*(?:number|#|:)?\s*([+]?[0-9]{1,4}?[-.\s]?[0-9]{8,15})/i',

            // Simple digit sequence (10-15 digits) - prioritize longer numbers
            '/\b([0-9]{11,15})\b/', // 11-15 digits first
            '/\b([0-9]{10})\b/', // exactly 10 digits

            // International format with country code: +880 1723604950, etc.
            '/([+][0-9]{1,4}[-.\s]?[0-9]{8,15})/',

            // US format: (*************, ************, ************ (lowest priority)
            '/([+]?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $message, $matches)) {
                $phone = preg_replace('/[^0-9+]/', '', $matches[1]);
                // Accept phone numbers between 10-15 digits (international standard)
                if (strlen($phone) >= 10 && strlen($phone) <= 15) {
                    return $phone;
                }
            }
        }

        return null;
    }

    /**
     * Extract customer email from message content
     */
    private function extractCustomerEmailFromMessage(string $message): ?string
    {
        // Email pattern
        if (preg_match('/(?:email|e-mail|mail)\s*(?:address|:)?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i', $message, $matches)) {
            return strtolower(trim($matches[1]));
        }

        // Direct email pattern
        if (preg_match('/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/', $message, $matches)) {
            return strtolower(trim($matches[1]));
        }

        return null;
    }

    /**
     * Extract customer address from message content
     */
    private function extractCustomerAddressFromMessage(string $message): ?string
    {
        // Address patterns - more flexible to handle international addresses
        $patterns = [
            // Explicit address mentions with keywords
            '/(?:address|location|where|live)\s*(?:is|:)?\s*([a-zA-Z0-9\s,.-]{3,100})/i',
            '/(?:my address|i live|located at)\s*([a-zA-Z0-9\s,.-]{3,100})/i',

            // US-style addresses with street names
            '/([0-9]+\s+[a-zA-Z\s]+(?:street|st|avenue|ave|road|rd|drive|dr|lane|ln|way|court|ct|boulevard|blvd)[\s,]+[a-zA-Z\s]+[a-zA-Z]{2}\s+[0-9]{5})/i',

            // International addresses - number + street/area + city pattern
            '/([0-9]+\s+[a-zA-Z\s,.-]+(?:dhaka|bangladesh|bd|chittagong|sylhet|rajshahi|khulna|barisal|rangpur|mymensingh))/i',

            // City/area combinations (common in Bangladesh/international)
            '/\b([a-zA-Z\s]+(?:dhaka|bangladesh|bd|chittagong|sylhet|rajshahi|khulna|barisal|rangpur|mymensingh))\b/i',
            '/\b((?:banani|dhanmondi|gulshan|uttara|mirpur|wari|old dhaka|new market|farmgate|mohammadpur|tejgaon|ramna|motijheel|paltan|segunbagicha|eskaton|malibagh|rampura|badda|baridhara|bashundhara|lalmatia|shyamoli|adabor|mohakhali|kalabagan|azimpur|lalbagh|chawkbazar|sutrapur|kotwali|ramna|shahbagh|tsb|dcc|wari|gendaria|shantinagar|arambagh|kamrangirchar|hazaribagh|rayerbazar|sher-e-bangla nagar|agargaon|kafrul|cantonment|banasree|aftabnagar|merul badda|khilgaon|sabujbagh|demra|shyampur|kadamtali|jatrabari|mugda|kamalapur|gandaria|islambagh|lalbagh|chawkbazar|bangshal|kotwali|sutrapur|wari|ramna|tejgaon|pallabi|mirpur|shah ali|kafrul|cantonment|uttara|airport|dakshinkhan|turag|savar|keraniganj|dohar|nawabganj|dhamrai)\s*,?\s*dhaka)\b/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $message, $matches)) {
                $address = trim($matches[1]);

                // Clean up the address
                $address = preg_replace('/\s+/', ' ', $address); // normalize whitespace
                $address = trim($address, '.,');

                // Basic validation - address should be reasonable length and not just numbers
                if (strlen($address) >= 3 && strlen($address) <= 100 && !preg_match('/^\d+$/', $address)) {
                    // Additional validation: should contain at least one letter
                    if (preg_match('/[a-zA-Z]/', $address)) {
                        return $address;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Ensure data is properly decoded from JSON if it's a string
     */
    private function ensureArrayFromJson($data): mixed
    {
        if (is_string($data)) {
            try {
                $decoded = json_decode($data, true);
                return $decoded ?: [];
            } catch (Exception $e) {
                Log::warning('Failed to decode JSON data', [
                    'data'  => substr($data, 0, 100),
                    'error' => $e->getMessage(),
                ]);
                return [];
            }
        }

        return $data;
    }

    /**
     * Format date for display (e.g., "tomorrow", "Monday, December 16th")
     */
    private function formatDateForDisplay(string $dateString): string
    {
        try {
            $date     = Carbon::parse($dateString);
            $today    = Carbon::today();
            $tomorrow = Carbon::tomorrow();

            if ($date->isSameDay($today)) {
                return "today";
            } elseif ($date->isSameDay($tomorrow)) {
                return "tomorrow";
            } else {
                return $date->format('l, F jS'); // e.g., "Monday, December 16th"
            }
        } catch (Exception $e) {
            return $dateString;
        }
    }

    /**
     * Format time for display (e.g., "2:00 PM", "14:00")
     */
    private function formatTimeForDisplay(string | \DateTimeInterface $timeInput): string
    {
        // If it's already a DateTime/Carbon, format directly
        if ($timeInput instanceof \DateTimeInterface) {
            return Carbon::make($timeInput)->format('g:i A');
        }

        $timeString = $timeInput; // string path
        $formats    = ['H:i:s', 'H:i', 'g:i A', 'h:i A'];
        foreach ($formats as $fmt) {
            try {
                $time = Carbon::createFromFormat($fmt, $timeString);
                return $time->format('g:i A');
            } catch (Exception $e) {
                // try next
            }
        }
        // last resort: let Carbon try to parse
        try {
            return Carbon::parse($timeString)->format('g:i A');
        } catch (Exception $e) {
            return $timeString;
        }
    }

    /**
     * Remove any false booking/confirmation claims from AI-only text when no booking has been created yet
     */
    private function sanitizeFalseConfirmation(string $text): string
    {
        $patterns = [
            '/\b(appointment|booking) (is )?(now )?confirmed\b/i',
            '/\b(i have|i\'ve) (booked|scheduled) (it|your appointment)\b/i',
            '/\bconfirmation (id|number)\b/i',
        ];
        $cleaned = preg_replace($patterns, ' ', $text);
        // Normalize whitespace
        return trim(preg_replace('/\s{2,}/', ' ', $cleaned ?? $text));
    }

    /**
     * Final processing of AI text: sanitize false confirmations and align any date/time mentions
     * to the computed date/time (when available).
     */
    private function finalizeAiResponse(string $aiText, ?string $date, ?string $time): string
    {
        $text = $this->sanitizeFalseConfirmation($aiText);
        if ($date) {
            try {
                $d   = Carbon::parse($date);
                $dow = $d->format('l');
                $md  = $d->format('F j');
                // Replace relative words
                $text = preg_replace('/\b(tomorrow|today)\b/i', $md, $text ?? '');
                // Replace weekdays with correct one
                $text = preg_replace('/\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/i', $dow, $text ?? '');
                // Replace any explicit month-day with the computed one
                $monthPattern = '/\b(jan(?:uary)?|feb(?:ruary)?|mar(?:ch)?|apr(?:il)?|may|jun(?:e)?|jul(?:y)?|aug(?:ust)?|sep(?:t(?:ember)?)?|oct(?:ober)?|nov(?:ember)?|dec(?:ember)?)\s+\d{1,2}(?:st|nd|rd|th)?\b/i';
                $text         = preg_replace($monthPattern, $md, $text ?? '');
            } catch (\Throwable $e) {
                // ignore failures
            }
        }
        // We could normalize time if needed (e.g., "10am" -> "10:00 AM"). Keep minimal for now.
        return trim($text ?? $aiText);
    }

    /**
     * Detect if AI already asked for date/time to avoid duplicate questions
     */
    private function aiAsksForDateOrTime(string $text): bool
    {
        $t = strtolower($text);
        return (bool) preg_match('/(what time|what date|when would|preferred date|preferred time|schedule)/i', $t);
    }

    /**
     * Detect if AI already included an explicit confirmation instruction.
     */
    private function aiContainsConfirmationInstruction(string $text): bool
    {
        $t = strtolower($text);
        return (bool) preg_match('/\b(confirm(ed)?\b|to finalize|reply with: \'confirm|please confirm)/i', $t);
    }

    /**
     * Check if assistant recently asked for confirmation in the conversation context
     */
    private function assistantRecentlyAskedForConfirmation(string $senderId): bool
    {
        try {
            $history = app(\App\Services\Chatbot\ConversationService::class)->getConversationContext($senderId);
            $history = array_slice($history, -6); // last ~3 turns
            foreach (array_reverse($history) as $msg) {
                if (($msg['role'] ?? '') !== 'assistant') {
                    continue;
                }
                $content = strtolower((string) ($msg['content'] ?? ''));
                if ($this->aiContainsConfirmationInstruction($content)) {
                    return true;
                }
            }
        } catch (\Throwable $e) {
            // ignore failures
        }
        return false;
    }

    /**
     * Build concise slot suggestions list using business hours and availability.
     * If a date or time is given, bias suggestions accordingly.
     */
    private function buildSlotSuggestions(?UserAgent $userAgent, int $limit = 3, ?string $forDate = null, ?string $atTime = null): string
    {
        if (!$userAgent) {
            return '';
        }

        try {
            $slots   = $this->getAvailableSlots($userAgent, 7);
            $options = [];

            foreach ($slots as $day) {
                if ($forDate && $day['date'] !== $forDate) {
                    continue;
                }
                foreach ($day['slots'] as $slot) {
                    if ($atTime && strpos($slot['start_time'], substr($atTime, 0, 2)) !== 0) {
                        continue;
                    }
                    $options[] = [
                        'date' => $day['formatted_date'],
                        'time' => $slot['formatted'],
                    ];
                    if (count($options) >= $limit) {
                        break 2;
                    }
                }
            }

            if (empty($options)) {
                // Fallback: first few slots regardless of filters
                foreach ($slots as $day) {
                    foreach ($day['slots'] as $slot) {
                        $options[] = [
                            'date' => $day['formatted_date'],
                            'time' => $slot['formatted'],
                        ];
                        if (count($options) >= $limit) {
                            break 2;
                        }
                    }
                }
            }

            if (empty($options)) {
                return '';
            }

            $lines = [];
            foreach ($options as $idx => $opt) {
                $n       = $idx + 1;
                $lines[] = "{$n}) {$opt['date']} at {$opt['time']}";
            }
            return implode("\n", $lines);

        } catch (Exception $e) {
            Log::warning('Failed to build slot suggestions', ['error' => $e->getMessage()]);
            return '';
        }
    }

    /**
     * Check if we need to collect additional customer information
     */
    private function needsCustomerInfoCollection(UserAgent $userAgent, array $currentInfo): array
    {
        $businessData = $this->ensureArrayFromJson($userAgent->agent_business_data);
        $config       = $this->ensureArrayFromJson($userAgent->customize_configuration);

        // Use simplified customer_info_collection_settings configuration
        $customerInfoConfig = $config['customer_info_collection_settings'] ?? $businessData['customer_info_collection_settings'] ?? [
            'name'    => true, // Always collect and require name
            'phone'   => true, // Collect phone and require it
            'email'   => true, // Collect email and require it
            'address' => true, // Collect address and require it
        ];

        $missing         = [];
        $collectSettings = [];

        foreach (['name', 'phone', 'email', 'address'] as $field) {
            $collectSettings[$field] = $customerInfoConfig[$field] ?? false;

            if ($collectSettings[$field] && empty($currentInfo[$field])) {
                $missing[] = $field;
            }
        }

        return [
            'missing'          => $missing,
            'collect_settings' => $collectSettings,
        ];
    }

    /**
     * Generate customer information collection prompt
     */
    private function generateCustomerInfoPrompt(array $missing, array $collectSettings): string
    {
        $prompts = [];

        if (in_array('name', $missing)) {
            $prompts[] = 'What is your name?';
        }

        if (in_array('phone', $missing)) {
            $prompts[] = 'What is your phone number?';
        }

        if (in_array('email', $missing)) {
            $prompts[] = 'What is your email address?';
        }

        if (in_array('address', $missing)) {
            $prompts[] = 'What is your address?';
        }

        if (empty($prompts)) {
            return '';
        }

        return "To complete your appointment, I need a few more details:\n" . implode("\n", $prompts);
    }

    /**
     * Check if the message contains explicit booking language
     */
    private function hasExplicitBookingLanguage(string $message): bool
    {
        $explicitBookingPhrases = [
            'book an appointment',
            'schedule an appointment',
            'make an appointment',
            'book a consultation',
            'schedule a consultation',
            'i want to book',
            'i want to schedule',
            'i need to book',
            'i need to schedule',
            'can i book',
            'can i schedule',
            'when can i book',
            'when can i schedule',
            'book me',
            'schedule me',
            'set up an appointment',
            'arrange an appointment',
            'reserve a time',
            'reserve an appointment',
        ];

        $messageLower = strtolower($message);

        foreach ($explicitBookingPhrases as $phrase) {
            if (strpos($messageLower, $phrase) !== false) {
                return true;
            }
        }

        return false;
    }
}
