<?php

declare (strict_types = 1);

namespace App\Models;

use App\Models\User;
use App\Models\UserAgent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class KnowledgeFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'file_name',
        'original_name',
        'uploaded_by',
        'user_agent_id',
        'type',
        'file_path',
        'file_size',
        'mime_type',
        'status',
    ];

    protected $casts = [
        'file_size'     => 'integer',
        'uploaded_by'   => 'integer',
        'user_agent_id' => 'integer',
        'status'        => 'string',
        'type'          => 'string',
    ];

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Get all chunks for this file
     */
    public function chunks(): HasMany
    {
        return $this->hasMany(KnowledgeChunk::class, 'knowledge_file_id')
            ->orderBy('chunk_index', 'asc');
    }

    /**
     * Get the user agent that owns this knowledge file
     */
    public function userAgent(): BelongsTo
    {
        return $this->belongsTo(UserAgent::class, 'user_agent_id');
    }

    /**
     * Get the user who uploaded this file
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope to get completed files
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get files by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get file size in human readable format
     */
    public function getHumanReadableSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}