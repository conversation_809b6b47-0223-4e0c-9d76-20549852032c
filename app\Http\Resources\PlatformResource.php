<?php

declare (strict_types = 1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class PlatformResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'name'                => $this->name,
            'key'                 => $this->key,
            'category'            => $this->category,
            'platform'            => $this->platform,
            'status'              => $this->status,
            'enabled'             => $this->enabled,
            'description'         => $this->description,
            'webhook_url'         => $this->webhook_url,
            'auth_url'            => $this->auth_url,
            'priority'            => $this->priority,
            'sort_order'          => $this->sort_order,
            'connected_at'        => $this->connected_at?->format('Y-m-d H:i:s'),
            'last_sync_at'        => $this->last_sync_at?->format('Y-m-d H:i:s'),
            'last_error'          => $this->when($this->last_error, $this->last_error),
            'last_error_at'       => $this->when($this->last_error_at, $this->last_error_at?->format('Y-m-d H:i:s')),
            'error_count'         => $this->when($this->error_count > 0, $this->error_count),
            'config'              => $this->when($this->config, $this->config),
            'metadata'            => $this->when($this->metadata, $this->metadata),
            'created_at'          => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at'          => $this->updated_at->format('Y-m-d H:i:s'),

            // Computed properties
            'is_connected'        => $this->isConnected(),
            'is_active'           => $this->isActive(),
            'status_with_context' => $this->getStatusWithContext(),
        ];
    }
}