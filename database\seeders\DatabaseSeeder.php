<?php

declare (strict_types = 1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;

final class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            UserSeeder::class,
            PlatformCategorySeeder::class,
            PlatformSeeder::class,
            AgentsSeeder::class,
            KnowledgeSeeder::class, // Run after AgentsSeeder so user agents exist
            BusinessEmbeddingSeeder::class, // Run after AgentsSeeder so user agents exist
            AppointmentsSeeder::class, // Run after all other seeders to ensure relationships exist
        ]);
    }
}