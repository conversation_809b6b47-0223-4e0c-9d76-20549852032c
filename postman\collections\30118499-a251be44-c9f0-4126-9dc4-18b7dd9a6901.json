{"info": {"_postman_id": "30118499-a251be44-c9f0-4126-9dc4-18b7dd9a6901", "name": "Kortana AI - Instagram Chatbot API", "description": "Complete API collection for testing Kortana AI Instagram Chatbot endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"id": "a686cd01-af78-49c0-94e3-ef9343cd8c8b", "exec": ["if (pm.response.code === 200) {\r", "    try {\r", "        const response = pm.response.json();\r", "        if (response.data && response.data.token) {\r", "            pm.environment.set('auth_token', response.data.token);\r", "            console.log(\"Token saved to environment: \", response.data.token);\r", "        } else {\r", "            console.warn(\"Token not found in response\");\r", "        }\r", "    } catch (e) {\r", "        console.error(\"Error parsing JSON response:\", e);\r", "    }\r", "} else {\r", "    console.warn(\"Response code was not 200:\", pm.response.code);\r", "}\r", ""], "type": "text/javascript", "packages": {}}}], "id": "30118499-03cb0604-3ccb-45f2-a30f-85f8d9497bf6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"<PERSON><PERSON>@ssw0rd\",\n  \"password_confirmation\": \"<PERSON><PERSON>@ssw0rd\",\n  \"phone\": \"+1234567890\",\n  \"address\": \"123 Main St, City, Country\",\n  \"company\": \"Example Corp\",\n  \"timezone\": \"America/New_York\",\n  \"about\": \"Software developer with 5 years of experience\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user with the updated fields"}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "3a605fb4-de7f-4e9f-8967-7402d92c324c", "exec": ["if (pm.response.code === 200) {\r", "    const response = pm.response.json();\r", "    if (response.data && response.data.token) {\r", "        pm.environment.set('auth_token', response.data.token);\r", "    }\r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "id": "30118499-762f9da9-da7e-4ced-a276-e1ef5dc46412", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"StrongP@ssw0rd\",\n  \"remember\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login with user credentials"}, "response": []}, {"name": "Get User Profile", "id": "30118499-48c023ee-5850-406a-9004-a8a324bba0c8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/user", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "user"]}, "description": "Get the current user's profile"}, "response": []}, {"name": "Update Profile", "id": "30118499-bc66ff7c-363e-4bac-bc6e-dabefbc881cc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"phone\": \"+1234567890\",\n  \"address\": \"456 New St, City, Country\",\n  \"company\": \"New Corp Ltd\",\n  \"timezone\": \"Europe/London\",\n  \"about\": \"Updated profile information\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "profile"]}, "description": "Update the user's profile information"}, "response": []}, {"name": "Update Profile with Avatar", "id": "30118499-92b5f4df-c547-47a0-bfb0-e10f9ce20dc1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "avatar", "type": "file", "uuid": "54564f9e-38ee-4081-b9ef-ab08a485738a", "src": "XpoDyZcSK/robot-img.png"}]}, "url": {"raw": "{{base_url}}/api/v1/auth/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "profile"]}, "description": "Update the user's profile with an avatar"}, "response": []}, {"name": "Logout", "id": "30118499-f11e0304-40bd-4d5a-b011-eb1b00cab15d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout the current user"}, "response": []}, {"name": "Forgot Password", "id": "30118499-5fd0b50c-6406-4aa3-83a5-ff4a4bb34779", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/forgot-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "forgot-password"]}, "description": "Request a password reset"}, "response": []}, {"name": "Reset Password", "id": "30118499-22528014-2170-4e2b-bfb5-1b7d357165cb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\",\n  \"password\": \"NewP@ssw0rd\",\n  \"password_confirmation\": \"NewP@ssw0rd\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/reset-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "reset-password"]}, "description": "Reset the password using OTP"}, "response": []}, {"name": "OTP Verify", "event": [{"listen": "test", "script": {"id": "184acf23-a6d1-45d7-82e4-06ed5caeeba5", "exec": ["if (pm.response.code === 200) {\r", "    const response = pm.response.json();\r", "    if (response.data && response.data.token) {\r", "        pm.environment.set('auth_token', response.data.token);\r", "    }\r", "}"], "type": "text/javascript", "packages": {}}}], "id": "30118499-4d238212-e8ef-45c7-be16-f90f098bf33a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"otp\": \"771738\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/otp-verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "otp-verify"]}, "description": "Verify email with OTP"}, "response": []}, {"name": "Resend OTP", "id": "30118499-ba8de5b9-8f7b-4105-add0-56fe2e0eeb4c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"state_code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-otp", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-otp"]}, "description": "Resend OTP to email address using state code (no authentication required)"}, "response": []}], "id": "30118499-7ff8030f-a761-43c9-ae8f-8d9a73afe636"}, {"name": "Social Media", "item": [{"name": "Send Message", "id": "30118499-929bbf65-e8bc-484b-9e7a-c8906359d3ce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"platform\": \"instagram\",\n    \"recipient_id\": \"1234567890\",\n    \"message\": \"Hello! How can I help you today?\",\n    \"message_type\": \"text\",\n    \"metadata\": {\n        \"campaign_id\": \"welcome_campaign\",\n        \"tags\": [\"greeting\", \"automated\"],\n        \"priority\": \"normal\"\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/social/send-message", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "send-message"]}}, "response": []}, {"name": "Get Instagram Messages", "id": "30118499-a8bc78ba-2df5-438c-a5c2-cc7e892fba8a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/messages?per_page=20", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "messages"], "query": [{"key": "per_page", "value": "20"}]}}, "response": []}, {"name": "Get Instagram Stats", "id": "30118499-47cb23e8-5b1e-46c5-a67c-b07fa4e34036", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "stats"]}}, "response": []}, {"name": "Get Conversation History", "id": "30118499-72379318-01fa-4eaa-8905-19026306a1b5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/conversations/history?conversation_id=conv_123&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "conversations", "history"], "query": [{"key": "conversation_id", "value": "conv_123"}, {"key": "limit", "value": "20"}]}}, "response": []}, {"name": "Get Active Conversations", "id": "30118499-9f0db3c0-8a16-4804-afa6-58bebb3e28a5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/conversations/active", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "conversations", "active"]}}, "response": []}, {"name": "Get Pending Messages", "id": "30118499-bc8b7499-fa5b-4aa8-a243-78bd3f5979b2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/messages/pending", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "messages", "pending"]}}, "response": []}, {"name": "Get Hourly Analytics", "id": "30118499-f275e33d-762c-4a6d-9b33-899a89968700", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/social/instagram/analytics/hourly", "host": ["{{base_url}}"], "path": ["api", "v1", "social", "instagram", "analytics", "hourly"]}}, "response": []}], "id": "30118499-cbeaea3d-e2bf-4deb-a17f-a4bad1a93864"}, {"name": "Instagram Authentication", "item": [{"name": "Instagram OAuth Redirect", "id": "30118499-d4d122df-f208-43bd-be96-4d28066c58c6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram"]}}, "response": []}, {"name": "Instagram OAuth <PERSON>back", "id": "30118499-f68f65e8-775c-4df8-a72b-f19396ca2354", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/callback?code=AUTH_CODE&state=STATE_VALUE", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "callback"], "query": [{"key": "code", "value": "AUTH_CODE"}, {"key": "state", "value": "STATE_VALUE"}]}}, "response": []}, {"name": "Instagram Connection Status", "id": "30118499-bfd246ca-719a-4488-bcca-8cc9014caa1e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/status", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "status"]}}, "response": []}, {"name": "Disconnect Instagram", "id": "30118499-05d05b53-c337-420b-8574-0294d2f8f65b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/disconnect", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "disconnect"]}}, "response": []}, {"name": "Refresh Instagram Token", "id": "30118499-f10fb5ed-2baa-44ef-900d-b10786480e0f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/auth/instagram/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "instagram", "refresh"]}}, "response": []}], "id": "30118499-2f975f80-f48b-4787-a3bc-5188f9214bcd"}, {"name": "Webhooks", "item": [{"name": "Instagram Webhook Verification", "id": "30118499-de26fd4d-af94-4f05-8dda-7d64239b1de1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/webhooks/instagram?hub.mode=subscribe&hub.challenge=CHALLENGE_VALUE&hub.verify_token=kortana_webhook_verify_2024", "host": ["{{base_url}}"], "path": ["api", "webhooks", "instagram"], "query": [{"key": "hub.mode", "value": "subscribe"}, {"key": "hub.challenge", "value": "CHALLENGE_VALUE"}, {"key": "hub.verify_token", "value": "kort<PERSON>_webhook_verify_2024"}]}}, "response": []}, {"name": "Instagram Webhook Handler", "id": "30118499-6f5876d9-4453-4258-b9a6-d705b3a12ec9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Hub-Signature-256", "value": "sha256=SIGNATURE_HASH"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"page\",\n    \"entry\": [\n        {\n            \"id\": \"17841475512211505\",\n            \"time\": 1234567890,\n            \"messaging\": [\n                {\n                    \"sender\": {\n                        \"id\": \"1234567890\"\n                    },\n                    \"recipient\": {\n                        \"id\": \"17841475512211505\"\n                    },\n                    \"timestamp\": 1234567890,\n                    \"message\": {\n                        \"mid\": \"message_id\",\n                        \"text\": \"Hello, I need help with my order\"\n                    }\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/webhooks/instagram", "host": ["{{base_url}}"], "path": ["api", "webhooks", "instagram"]}}, "response": []}], "id": "30118499-23d09314-57e1-4a97-8528-2b46cff2aaf9"}, {"name": "Platforms", "item": [{"name": "Get Available Services", "id": "30118499-0e1b8278-3edc-4fcb-bbc9-bb265bc87e1f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/available", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "available"]}, "description": "Get all available platform services"}, "response": []}, {"name": "Get Services By Category", "id": "30118499-c0af48d2-f65a-4ff4-aa73-df4e4c0c13ec", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/category/social-media", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "category", "social-media"]}, "description": "Get platform services by category"}, "response": []}, {"name": "Get Service Details", "id": "30118499-dab530ff-9139-4a8b-b080-d9750c47765c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/service/instagram", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "service", "instagram"]}, "description": "Get details of a specific platform service"}, "response": []}, {"name": "Get Service Auth URL", "id": "30118499-e1c64101-4f5a-4e8c-9b2a-8f7d3e9c1a2b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/service/instagram/auth-url", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "service", "instagram", "auth-url"]}, "description": "Get authentication URL for a specific service"}, "response": []}, {"name": "Get Service Webhook URL", "id": "30118499-f2d75212-5g6b-5f9d-0c3b-9g8e4f0d2b3c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/service/instagram/webhook-url", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "service", "instagram", "webhook-url"]}, "description": "Get webhook URL for a specific service"}, "response": []}, {"name": "Get Service URLs", "id": "30118499-g3e86323-6h7c-6g0e-1d4c-0h9f5g1e3c4d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/service/instagram/urls", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "service", "instagram", "urls"]}, "description": "Get all URLs for a specific service"}, "response": []}, {"name": "Get Service Connection Status", "id": "30118499-h4f97434-7i8d-7h1f-2e5d-1i0g6h2f4d5e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/service/instagram/status", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "service", "instagram", "status"]}, "description": "Get connection status for a specific service"}, "response": []}, {"name": "Get All Services Status", "id": "30118499-i5g08545-8j9e-8i2g-3f6e-2j1h7i3g5e6f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/status", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "status"]}, "description": "Get status of all platform services"}, "response": []}, {"name": "Get Platform Channels", "id": "30118499-j6h19656-9k0f-9j3h-4g7f-3k2i8j4h6f7g", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/channels", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "channels"]}, "description": "Get platform channels configuration"}, "response": []}, {"name": "Update Platform Channels", "id": "30118499-k7i20767-0l1g-0k4i-5h8g-4l3j9k5i7g8h", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"channels\": {\n        \"instagram\": {\n            \"enabled\": true,\n            \"auto_reply\": true,\n            \"business_hours_only\": false,\n            \"auto_reply_message\": \"Thank you for your message. We'll get back to you soon.\"\n        },\n        \"facebook\": {\n            \"enabled\": true,\n            \"auto_reply\": true,\n            \"business_hours_only\": true,\n            \"auto_reply_message\": \"Thanks for reaching out! We'll respond during business hours.\"\n        },\n        \"whatsapp\": {\n            \"enabled\": false,\n            \"auto_reply\": false,\n            \"business_hours_only\": true\n        }\n    }\n}"}, "url": {"raw": "{{base_url}}/api/v1/platforms/channels", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "channels"]}, "description": "Update platform channels configuration"}, "response": []}, {"name": "Toggle Service (Superadmin)", "id": "30118499-l8j31878-1m2h-1l5j-6i9h-5m4k0l6j8h9i", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"enabled\": true,\n    \"reason\": \"Service activation for testing\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/platforms/service/instagram/toggle", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "service", "instagram", "toggle"]}, "description": "Toggle a service on/off (Superadmin only)"}, "response": []}, {"name": "Get All Services (Superadmin)", "id": "30118499-m9k42989-2n3i-2m6k-7j0i-6n5l1m7k9i0j", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/platforms/all", "host": ["{{base_url}}"], "path": ["api", "v1", "platforms", "all"]}, "description": "Get all platform services (Superadmin only)"}, "response": []}], "id": "30118499-d529562e-4785-4223-b09c-b4656b5c5ba8"}, {"name": "Media Management", "item": [{"name": "Upload Media", "id": "30118499-ff94402b-fe9e-44b3-8a60-160b2d8ea5a1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/file.jpg"}, {"key": "model_type", "value": "User", "type": "text"}, {"key": "model_id", "value": "{{user_id}}", "type": "text"}, {"key": "collection", "value": "avatars", "type": "text"}, {"key": "custom_properties", "value": "{\"alt\": \"Profile picture\", \"description\": \"User avatar image\"}", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/media/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "media", "upload"]}, "description": "Upload a media file"}, "response": []}, {"name": "List Media", "id": "30118499-20a7d718-3e33-4a94-9788-3e2276f613ec", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/media/list?model_type=User&model_id={{user_id}}&collection=avatars", "host": ["{{base_url}}"], "path": ["api", "v1", "media", "list"], "query": [{"key": "model_type", "value": "User"}, {"key": "model_id", "value": "{{user_id}}"}, {"key": "collection", "value": "avatars"}]}, "description": "List media files"}, "response": []}, {"name": "Delete Media", "id": "30118499-7278689d-24c9-49bd-8b8c-3a2accd01e4b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/media/1", "host": ["{{base_url}}"], "path": ["api", "v1", "media", "1"]}, "description": "Delete a media file"}, "response": []}], "id": "30118499-7611baa5-8aee-4e36-9e9e-98a3d6d3dd5b"}, {"name": "Agents Management", "item": [{"name": "Get All Agents", "id": "30118499-aa11bb22-cc33-dd44-ee55-ff6677889900", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/agents?per_page=10&page=1&active=true&search=booking&all=false", "host": ["{{base_url}}"], "path": ["api", "v1", "agents"], "query": [{"key": "per_page", "value": "10", "description": "Number of items per page (default: 15)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "active", "value": "true", "description": "Filter by active status (true/false)"}, {"key": "search", "value": "booking", "description": "Search by name, key, or description"}, {"key": "all", "value": "false", "description": "Get all data without pagination (true/false)"}]}, "description": "Get all available agent types with filtering and pagination options. Returns: name, key, description, is_active, is_subscribed (for authenticated user)"}, "response": []}, {"name": "Get Agent Details", "id": "30118499-cc33dd44-ee55-ff66-7788-990011223344", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/agents/1", "host": ["{{base_url}}"], "path": ["api", "v1", "agents", "1"]}, "description": "Get details of a specific agent (returns: name, key, description, is_active, is_subscribed)"}, "response": []}, {"name": "Get Agent Metrics", "id": "30118499-ff667788-9900-1122-3344-556677889900", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/agents/1/metrics?date_from=2024-01-01&date_to=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "v1", "agents", "1", "metrics"], "query": [{"key": "date_from", "value": "2024-01-01", "description": "Start date for metrics (optional, defaults to 30 days ago)"}, {"key": "date_to", "value": "2024-01-31", "description": "End date for metrics (optional, defaults to today)"}]}, "description": "Get aggregated metrics for a specific agent type across all user subscriptions"}, "response": []}, {"name": "Update Agent Status", "id": "30118499-77889900-1122-3344-5566-778899001122", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"is_active\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/agents/1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "agents", "1", "status"]}, "description": "Update agent status (activate/deactivate) - returns: name, key, description, is_active, is_subscribed"}, "response": []}, {"name": "Subscribe to Agent", "id": "30118499-subscribe-agent-endpoint", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"agent_gender\": \"female\",\n    \"personality\": \"friendly\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/agents/2/subscribe", "host": ["{{base_url}}"], "path": ["api", "v1", "agents", "2", "subscribe"]}, "description": "Subscribe user to an agent. Creates a new UserAgent record or activates existing one. Required parameters:\n- name: Custom name for the agent (e.g., '<PERSON>', '<PERSON>', '<PERSON>')\n- agent_gender: Agent gender (male, female, neutral)\n- personality: Agent personality (friendly, professional, casual, formal, helpful, enthusiastic)"}, "response": []}, {"name": "Unsubscribe from Agent", "id": "30118499-unsubscribe-agent-endpoint", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/agents/2/unsubscribe", "host": ["{{base_url}}"], "path": ["api", "v1", "agents", "2", "unsubscribe"]}, "description": "Unsubscribe user from an agent. Changes the UserAgent status to inactive (doesn't delete the record)"}, "response": []}, {"name": "Update User Agent", "id": "30118499-update-user-agent-endpoint", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON>", "type": "text", "description": "Custom name for the agent"}, {"key": "personality", "value": "professional", "type": "text", "description": "Agent personality (friendly, professional, casual, formal, helpful, enthusiastic)"}, {"key": "agent_gender", "value": "female", "type": "text", "description": "Agent gender (male, female)"}, {"key": "customize_configuration", "value": "{\"language\": \"en\", \"timezone\": \"UTC\", \"response_time_target\": 10}", "type": "text", "description": "JSON object with custom configuration - CLEAN JSON (no extra quotes)"}, {"key": "prompt_templates", "value": "{\"greeting\": \"Hello! I'm <PERSON>, how can I help you today?\", \"fallback\": \"I apologize, I didn't understand. Could you please rephrase?\"}", "type": "text", "description": "JSON object with custom prompt templates - CLEAN JSON (no extra quotes)"}, {"key": "company_details", "value": "{\"name\": \"Updated Company Name\", \"type\": \"service_business\"}", "type": "text", "description": "JSON object with company details - CLEAN JSON (no extra quotes)"}, {"key": "knowledge_files", "type": "file", "description": "Knowledge files to upload (PDF, TXT, DOC, DOCX, CSV, JSON - max 10MB each)", "value": null}, {"key": "", "type": "file", "description": "Additional knowledge file (can upload multiple files)", "value": null, "disabled": true}]}, "url": {"raw": "{{base_url}}/api/v1/agents/1/user-agent", "host": ["{{base_url}}"], "path": ["api", "v1", "agents", "1", "user-agent"]}, "description": "FIXED: Update user agent configuration and upload knowledge files. This endpoint uses proper JSON handling without extra quotes. You can update any combination of: name, status, personality, agent_gender, customize_configuration, prompt_templates, company_details, agent_business_data, notification_settings, analytics_settings, performance_targets, escalation_rules, and upload knowledge_files."}, "response": []}], "id": "30118499-agents-management-folder-id"}, {"name": "Appointments Management", "item": [{"name": "List All Appointments", "id": "30118499-appointments-list-all", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?per_page=15&page=1", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "per_page", "value": "15", "description": "Items per page (1-100)"}, {"key": "page", "value": "1", "description": "Page number"}]}, "description": "Get all appointments with pagination support for infinite scrolling"}, "response": []}, {"name": "Search Appointments", "id": "30118499-appointments-search", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?type=search&search=john&per_page=15", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "type", "value": "search", "description": "Query type: search"}, {"key": "search", "value": "john", "description": "Search term for customer name, service, notes"}, {"key": "per_page", "value": "15", "description": "Items per page"}]}, "description": "Search appointments by customer name, service, or notes"}, "response": []}, {"name": "Filter by Status", "id": "30118499-appointments-filter-status", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?status=pending&per_page=15", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "status", "value": "pending", "description": "Filter by status: pending, confirmed, in_progress, completed, cancelled, no_show"}, {"key": "per_page", "value": "15", "description": "Items per page"}]}, "description": "Filter appointments by status"}, "response": []}, {"name": "Filter by Platform", "id": "30118499-appointments-filter-platform", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?platform=instagram&per_page=15", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "platform", "value": "instagram", "description": "Filter by platform key"}, {"key": "per_page", "value": "15", "description": "Items per page"}]}, "description": "Filter appointments by platform"}, "response": []}, {"name": "Filter by Date Range", "id": "30118499-appointments-filter-date", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?date_from=2025-08-01&date_to=2025-08-31&per_page=15", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "date_from", "value": "2025-08-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "date_to", "value": "2025-08-31", "description": "End date (YYYY-MM-DD)"}, {"key": "per_page", "value": "15", "description": "Items per page"}]}, "description": "Filter appointments by date range"}, "response": []}, {"name": "Today's Appointments", "id": "30118499-appointments-today", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?type=today", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "type", "value": "today", "description": "Get today's appointments only"}]}, "description": "Get all appointments for today"}, "response": []}, {"name": "Calendar View", "id": "30118499-appointments-calendar", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?type=calendar&month=8&year=2025", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "type", "value": "calendar", "description": "Calendar view type"}, {"key": "month", "value": "8", "description": "Month (1-12)"}, {"key": "year", "value": "2025", "description": "Year"}]}, "description": "Get appointments formatted for calendar display"}, "response": []}, {"name": "Appointment Statistics", "id": "30118499-appointments-stats", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?type=stats", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "type", "value": "stats", "description": "Get appointment statistics"}]}, "description": "Get appointment statistics for dashboard"}, "response": []}, {"name": "Combined Filters", "id": "30118499-appointments-combined-filters", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments?status=pending&platform=instagram&date=2025-08-05&per_page=20", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"], "query": [{"key": "status", "value": "pending", "description": "Filter by status"}, {"key": "platform", "value": "instagram", "description": "Filter by platform"}, {"key": "date", "value": "2025-08-05", "description": "Filter by specific date"}, {"key": "per_page", "value": "20", "description": "Items per page"}]}, "description": "Example of combining multiple filters"}, "response": []}, {"name": "Create Appointment", "id": "30118499-appointments-create", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_name\": \"<PERSON>\",\n  \"customer_id\": \"customer_instagram_123\",\n  \"customer_phone\": \"+1234567890\",\n  \"customer_email\": \"<EMAIL>\",\n  \"user_agent_id\": 1,\n  \"token_id\": 1,\n  \"conversation_id\": \"conv_123\",\n  \"appointment_date\": \"2025-08-15\",\n  \"appointment_time\": \"14:00\",\n  \"duration_minutes\": 120,\n  \"service_name\": \"Full Vehicle Wrap\",\n  \"service_key\": \"full_vehicle_wrap\",\n  \"service_price\": 2500.00,\n  \"service_category\": \"vehicle_wrapping\",\n  \"notes\": \"Customer wants red color wrap\",\n  \"metadata\": {\n    \"platform\": \"instagram\",\n    \"booking_source\": \"chatbot\",\n    \"priority\": \"normal\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments"]}, "description": "Create a new appointment"}, "response": []}, {"name": "Get Appointment Details", "id": "30118499-appointments-show", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments/1", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1"]}, "description": "Get details of a specific appointment"}, "response": []}, {"name": "Update Appointment", "id": "30118499-appointments-update", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_name\": \"<PERSON>\",\n  \"customer_phone\": \"+1234567891\",\n  \"appointment_date\": \"2025-08-16\",\n  \"appointment_time\": \"15:00\",\n  \"notes\": \"Updated: Customer wants blue color wrap instead\",\n  \"service_price\": 2600.00\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1"]}, "description": "Update an existing appointment"}, "response": []}, {"name": "Confirm Appointment", "id": "30118499-appointments-confirm", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmed\",\n  \"notes\": \"Customer confirmed via phone call\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", "status"]}, "description": "Confirm an appointment using universal status endpoint"}, "response": []}, {"name": "<PERSON>cel Appointment", "id": "30118499-appointments-cancel", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"cancelled\",\n  \"notes\": \"Customer requested cancellation due to schedule conflict\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", "status"]}, "description": "Cancel an appointment using universal status endpoint"}, "response": []}, {"name": "Complete Appointment", "id": "30118499-appointments-complete", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\",\n  \"notes\": \"Service completed successfully. Customer satisfied with the result.\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", "status"]}, "description": "Mark appointment as completed using universal status endpoint"}, "response": []}, {"name": "<PERSON> as No <PERSON>", "id": "30118499-appointments-no-show", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"no_show\",\n  \"notes\": \"Customer did not show up for the appointment\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", "status"]}, "description": "Mark appointment as no-show using universal status endpoint"}, "response": []}, {"name": "Set In Progress", "id": "30118499-appointments-in-progress", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_progress\",\n  \"notes\": \"Service has started\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/appointments/1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1", "status"]}, "description": "Set appointment status to in-progress using universal status endpoint"}, "response": []}, {"name": "Delete Appointment", "id": "30118499-appointments-delete", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments/1", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "1"]}, "description": "Delete an appointment permanently"}, "response": []}, {"name": "Get Available Statuses", "id": "30118499-appointments-statuses", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/appointments/meta/statuses", "host": ["{{base_url}}"], "path": ["api", "v1", "appointments", "meta", "statuses"]}, "description": "Get all available appointment statuses"}, "response": []}], "id": "30118499-appointments-management-folder"}, {"name": "Health Check", "id": "30118499-3847397c-a09e-4e75-a43b-bd90f1afcd9d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"id": "f6eb5d47-b30f-43d2-9267-1c76c71cda5f", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "fcf8749a-303f-41cc-8e9a-c239ac717891", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://kortana-ai.loca.lt", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}]}