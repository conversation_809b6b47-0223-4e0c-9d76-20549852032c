<?php

declare (strict_types = 1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PlatformService;
use App\Services\PlatformUrlService;
use App\Traits\ApiResponseTrait;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class PlatformsController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private PlatformUrlService $urlService,
        private PlatformService $platformService
    ) {}

    /**
     * Get all available platforms (enabled by superadmin) grouped by category with user connection status
     */
    public function getAvailablePlatforms(): JsonResponse
    {
        $userId = Auth::id();

        if (!$userId) {
            return $this->errorResponse('User not authenticated', 401);
        }

        $services = $this->platformService->getGroupedPlatformsWithStatus();

        return $this->successResponse($services, 'Available platforms retrieved successfully');
    }

    /**
     * Get services by category with user connection status
     */
    public function getServicesByCategory(string $category): JsonResponse
    {
        $userId = Auth::id();

        if (!$userId) {
            return $this->errorResponse('User not authenticated', 401);
        }

        $services = $this->platformService->getPlatformsByCategory($category)
            ->map(function ($service) use ($userId) {
                $data = $service->toApiArrayForUser($userId);

                // If not connected, add auth URL
                if (!$data['connected']) {
                    $authUrl = $this->urlService->getAuthUrl($service->key);
                    if ($authUrl) {
                        $data['auth_url'] = $this->urlService->getFullAuthUrl($service->key);
                    }
                }

                return $data;
            });

        if ($services->isEmpty()) {
            return $this->errorResponse("Category '{$category}' not found or no services available", 404);
        }

        return $this->successResponse($services, "Services in category '{$category}' retrieved successfully");
    }

    /**
     * Get specific service details with user connection status
     */
    public function getService(string $service): JsonResponse
    {
        $userId = Auth::id();

        if (!$userId) {
            return $this->errorResponse('User not authenticated', 401);
        }

        $platform = $this->platformService->findPlatformByKey($service);

        if (!$platform || !$platform->isEnabled()) {
            return $this->errorResponse('Service not found or not enabled', 404);
        }

        $data = $platform->toApiArrayForUser($userId);

        // If not connected, add auth URL
        if (!$data['connected']) {
            $authUrl = $this->urlService->getAuthUrl($service);
            if ($authUrl) {
                $data['auth_url'] = $this->urlService->getFullAuthUrl($service);
            }
        }

        return $this->successResponse($data, 'Service details retrieved successfully');
    }

    /**
     * Get auth URL for a specific service
     */
    public function getAuthUrl(string $service): JsonResponse
    {
        $userId = Auth::id();

        if (!$userId) {
            return $this->errorResponse('User not authenticated', 401);
        }

        // Check if service is enabled by superadmin
        $platform = $this->platformService->findPlatformByKey($service);

        if (!$platform || !$platform->isEnabled()) {
            return $this->errorResponse('Service not found or not enabled', 404);
        }

        // Check if user is already connected
        if ($platform->isConnectedForUser($userId)) {
            return $this->errorResponse('User is already connected to this service', 400);
        }

        // Get auth URL from environment configuration
        $authUrl = $this->urlService->getAuthUrl($service);

        if (!$authUrl) {
            return $this->errorResponse('Auth URL not configured for this service', 404);
        }

        return $this->successResponse([
            'service'       => $service,
            'auth_url'      => $authUrl,
            'full_auth_url' => $this->urlService->getFullAuthUrl($service),
        ], 'Auth URL generated successfully');
    }

    /**
     * Get webhook URL for a specific service
     */
    public function getWebhookUrl(string $service): JsonResponse
    {
        // Check if service is enabled by superadmin
        $platform = $this->platformService->findPlatformByKey($service);

        if (!$platform || !$platform->isEnabled()) {
            return $this->errorResponse('Service not found or not enabled', 404);
        }

        // Get webhook URL from environment configuration
        $webhookUrl = $this->urlService->getWebhookUrl($service);

        if (!$webhookUrl) {
            return $this->errorResponse('Webhook URL not configured for this service', 404);
        }

        return $this->successResponse([
            'service'          => $service,
            'webhook_url'      => $webhookUrl,
            'full_webhook_url' => $this->urlService->getFullWebhookUrl($service),
        ], 'Webhook URL generated successfully');
    }

    /**
     * Get all URLs for a specific service
     */
    public function getServiceUrls(string $service): JsonResponse
    {
        // Check if service is enabled by superadmin
        $platform = $this->platformService->findPlatformByKey($service);

        if (!$platform || !$platform->isEnabled()) {
            return $this->errorResponse('Service not found or not enabled', 404);
        }

        $urls = $this->urlService->getPlatformUrls($service);

        return $this->successResponse([
            'service' => $service,
            'urls'    => $urls,
        ], 'Service URLs generated successfully');
    }

    /**
     * Get connection status for a specific service
     */
    public function getConnectionStatus(string $service): JsonResponse
    {
        $userId = Auth::id();

        if (!$userId) {
            return $this->errorResponse('User not authenticated', 401);
        }

        $platform = $this->platformService->findPlatformByKey($service);

        if (!$platform || !$platform->isEnabled()) {
            return $this->errorResponse('Service not found or not enabled', 404);
        }

        $status = $platform->getStatusWithContextForUser($userId);

        // If not connected, add auth URL
        if (!$status['connected']) {
            $authUrl = $this->urlService->getAuthUrl($service);
            if ($authUrl) {
                $status['auth_url'] = $this->urlService->getFullAuthUrl($service);
            }
        }
        // If connected, make sure we display profile information in a consistent format
        else if (isset($status['token'])) {
            // Transform token data to profile format if needed
            $status['profile'] = [
                'name'                => null, // Add if available in token data
                'profile_picture_url' => null, // Add if available in token data
            ];
        }

        return $this->successResponse([
            'service' => $service,
            'status'  => $status,
        ], 'Connection status retrieved successfully');
    }

    /**
     * Get all services with their connection status for the authenticated user
     */
    public function getAllServicesStatus(): JsonResponse
    {
        $userId = Auth::id();

        if (!$userId) {
            return $this->errorResponse('User not authenticated', 401);
        }

        $services = $this->platformService->getEnabledPlatforms()
            ->map(function ($service) use ($userId) {
                $data = [
                    'service'   => $service->key,
                    'name'      => $service->name,
                    'category'  => $service->getCategoryName(),
                    'enabled'   => $service->enabled,
                    'connected' => $service->isConnectedForUser($userId),
                ];

                // If connected, add token info and profile details
                if ($data['connected']) {
                    $activeToken = $service->getActiveTokenForUser($userId);
                    if ($activeToken && $activeToken->tokenable) {
                        $tokenData = $activeToken->tokenable;

                        // Add simplified profile information (only name and profile picture)
                        $data['profile'] = [
                            'name'                => $tokenData->name ?? null,
                            'profile_picture_url' => $tokenData->profile_picture_url ?? null,
                        ];

                        // Keep token data for backward compatibility
                        $data['token'] = [
                            'username'              => $tokenData->username ?? null,
                            'account_type'          => $tokenData->account_type ?? null,
                            'expires_at'            => $tokenData->expires_at?->format('Y-m-d H:i:s'),
                            'days_until_expiration' => $this->calculateDaysUntilExpiration($tokenData->expires_at),
                        ];
                    }
                } else {
                    // If not connected, add auth URL
                    $authUrl = $this->urlService->getAuthUrl($service->key);
                    if ($authUrl) {
                        $data['auth_url'] = $this->urlService->getFullAuthUrl($service->key);
                    }
                }

                return $data;
            });

        return $this->successResponse($services, 'All services status retrieved successfully');
    }

    /**
     * Superadmin: Enable/disable a service
     */
    public function toggleService(Request $request, string $service): JsonResponse
    {
        $platform = $this->platformService->findPlatformByKey($service);

        if (!$platform) {
            return $this->errorResponse('Service not found', 404);
        }

        $enabled = $request->boolean('enabled', !$platform->enabled);

        if ($enabled) {
            $this->platformService->enablePlatform($platform);
        } else {
            $this->platformService->disablePlatform($platform);
        }

        return $this->successResponse([
            'service' => $service,
            'enabled' => $enabled,
        ], "Service '{$service}' " . ($enabled ? 'enabled' : 'disabled') . ' successfully');
    }

    /**
     * Superadmin: Get all services (including disabled ones)
     */
    public function getAllServices(): JsonResponse
    {
        $services = $this->platformService->getAllPlatforms()['platforms'];

        return $this->successResponse($services, 'All services retrieved successfully');
    }

    /**
     * Calculate days until token expiration.
     */
    private function calculateDaysUntilExpiration(?Carbon $expiresAt): ?int
    {
        if (!$expiresAt) {
            return null;
        }

        $now = now();
        if ($expiresAt->isPast()) {
            return 0;
        }

        return (int) $now->diffInDays($expiresAt, false);
    }
}