<?php

declare (strict_types = 1);

namespace App\Traits;

use App\Models\Media;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

trait HasMedia
{
    /**
     * Get all media associated with the model.
     */
    public function media(): MorphMany
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    /**
     * Get media from a specific collection.
     *
     * @param string $collection
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getMedia(string $collection = 'default')
    {
        return $this->media()->where('collection', $collection)->get();
    }

    /**
     * Get the first media item from a collection.
     *
     * @param string $collection
     * @return Media|null
     */
    public function getFirstMedia(string $collection = 'default')
    {
        return $this->media()->where('collection', $collection)->first();
    }

    /**
     * Add a media file to the model.
     *
     * @param UploadedFile $file
     * @param string $collection
     * @param array $customProperties
     * @return Media
     */
    public function addMedia(UploadedFile $file, string $collection = 'default', array $customProperties = []): Media
    {
        $fileName          = $file->getClientOriginalName();
        $name              = pathinfo($fileName, PATHINFO_FILENAME);
        $extension         = $file->getClientOriginalExtension();
        $sanitizedFileName = Str::slug($name) . '_' . time() . '.' . $extension;

        $path = $file->storeAs(
            $this->getStoragePath($collection),
            $sanitizedFileName,
            'public'
        );

        return $this->media()->create([
            'name'              => $name,
            'file_name'         => $sanitizedFileName,
            'mime_type'         => $file->getMimeType(),
            'path'              => $path,
            'disk'              => 'public',
            'collection'        => $collection,
            'size'              => $file->getSize(),
            'custom_properties' => $customProperties,
        ]);
    }

    /**
     * Delete all media in a collection.
     *
     * @param string $collection
     * @return void
     */
    public function clearMediaCollection(string $collection = 'default'): void
    {
        $items = $this->getMedia($collection);

        foreach ($items as $media) {
            Storage::disk($media->disk)->delete($media->path);
            $media->delete();
        }
    }

    /**
     * Get the storage path for a collection.
     *
     * @param string $collection
     * @return string
     */
    protected function getStoragePath(string $collection): string
    {
        $modelName = Str::plural(Str::snake(class_basename($this)));
        return $modelName . '/' . $this->id . '/' . $collection;
    }
}