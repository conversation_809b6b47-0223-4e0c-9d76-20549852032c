<?php

declare (strict_types = 1);

namespace App\Http\Requests;

class AgentIdRequest extends BaseFormRequest
{

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|min:1|max:2147483647',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Add the route parameter to the request data for validation
        $this->merge([
            'id' => $this->route('id'),
        ]);
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'id.required' => 'Agent ID is required.',
            'id.integer'  => 'Agent ID must be a valid integer.',
            'id.min'      => 'Agent ID must be a positive integer.',
            'id.max'      => 'Agent ID is too large.',
        ];
    }

    /**
     * Get the validated agent ID.
     */
    public function getAgentId(): int
    {
        return (int) $this->validated()['id'];
    }
}